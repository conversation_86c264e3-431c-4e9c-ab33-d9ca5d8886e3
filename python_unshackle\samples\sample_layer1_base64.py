# Sample Layer 1: Base64 Obfuscation
# This sample demonstrates simple base64 encoding obfuscation

import base64

# Simple base64 encoded string
exec(base64.b64decode(b'cHJpbnQoIkhlbGxvLCBJIHdhcyBoaWRkZW4gaW4gYmFzZTY0ISIp'))

# Another base64 encoded piece
code = 'ZGVmIGdyZWV0KG5hbWUpOgogICAgcHJpbnQoZiJIZWxsbywge25hbWV9ISIpCgpncmVldCgiV29ybGQiKQ=='
exec(base64.b64decode(code))

# Base64 with additional complexity
hidden_message = base64.b64decode('IyBUaGlzIGlzIGEgaGlkZGVuIG1lc3NhZ2UKcHJpbnQoIkRlb2JmdXNjYXRpb24gc3VjY2Vzc2Z1bCEiKQ==')
exec(hidden_message)
