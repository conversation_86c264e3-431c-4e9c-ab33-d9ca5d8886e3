#!/usr/bin/env python3
"""
Test script for PyThon-Unshackle

This script tests the basic functionality of the deobfuscation pipeline.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python_unshackle.core.pipeline import Deobfusca<PERSON><PERSON>ipeline, PipelineConfig
from python_unshackle.core.detector import PatternDetector
from python_unshackle.utils.logger import UnshackleLogger


def test_pattern_detection():
    """Test the pattern detection engine."""
    print("Testing Pattern Detection Engine...")
    
    detector = PatternDetector()
    
    # Test base64 detection
    base64_code = '''
import base64
exec(base64.b64decode(b'cHJpbnQoIkhlbGxvLCBXb3JsZCEiKQ=='))
'''
    
    results = detector.detect_obfuscation(base64_code)
    print(f"Base64 detection results: {len(results)} patterns found")
    for result in results:
        print(f"  - {result.obfuscation_type.value}: {result.confidence:.2f}")
    
    print()


def test_ast_navigator():
    """Test the AST navigator."""
    print("Testing AST Navigator...")
    
    from python_unshackle.core.ast_navigator import ASTNavigator
    
    navigator = ASTNavigator()
    
    # Test exec call detection
    exec_code = '''
import base64
exec(base64.b64decode("SGVsbG8gV29ybGQ="))
eval("print('Hello from eval')")
'''
    
    exec_calls = navigator.find_exec_calls(exec_code)
    print(f"Found {len(exec_calls)} exec/eval calls")
    for call in exec_calls:
        print(f"  - {call.function_name} at line {call.line_number}")
    
    print()


def test_layers():
    """Test individual layers."""
    print("Testing Individual Layers...")
    
    # Test encoding peeler
    from python_unshackle.layers.encoding_peeler import EncodingPeeler
    
    peeler = EncodingPeeler()
    
    # Test base64 data
    base64_data = "SGVsbG8sIFdvcmxkIQ=="  # "Hello, World!"
    can_handle, confidence = peeler.can_handle(base64_data)
    print(f"Encoding peeler can handle base64: {can_handle} (confidence: {confidence:.2f})")
    
    if can_handle:
        result = peeler.process(base64_data)
        print(f"Processing result: {result.success}")
        if result.success:
            print(f"Decoded: {result.data}")
    
    print()


def test_simple_pipeline():
    """Test the complete pipeline with simple data."""
    print("Testing Simple Pipeline...")
    
    # Create logger
    logger = UnshackleLogger(verbose=True)
    
    # Create pipeline
    config = PipelineConfig(max_iterations=3)
    pipeline = DeobfuscationPipeline(config=config, logger=logger)
    
    # Test with simple base64 encoded Python code
    simple_code = '''
import base64
exec(base64.b64decode(b'cHJpbnQoIkhlbGxvIGZyb20gZGVvYmZ1c2NhdGVkIGNvZGUhIik='))
'''
    
    result = pipeline.deobfuscate(simple_code)
    print(f"Pipeline result: {result.success}")
    print(f"Layers applied: {result.layers_applied}")
    print(f"Iterations: {result.total_iterations}")
    
    if result.final_code:
        print("Final code:")
        print("-" * 40)
        print(result.final_code)
        print("-" * 40)
    
    print()


def main():
    """Run all tests."""
    print("PyThon-Unshackle Test Suite")
    print("=" * 50)
    
    try:
        test_pattern_detection()
        test_ast_navigator()
        test_layers()
        test_simple_pipeline()
        
        print("All tests completed!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
