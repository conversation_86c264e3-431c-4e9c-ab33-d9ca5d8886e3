"""
Base Layer Abstract Class for PyThon-Unshackle

This module defines the abstract base class that all deobfuscation layers (peelers)
must inherit from. It establishes the contract and interface that ensures consistency
across all peeling operations.

The BaseLayer class enforces the "onion peeling" methodology where each layer:
1. Detects if it can handle the current obfuscation type
2. Attempts to peel/decode/deobfuscate the input
3. Returns the cleaned result for the next layer
4. Provides metadata about the operation performed
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum


class LayerResult(Enum):
    """Enumeration of possible layer processing results"""
    SUCCESS = "success"
    FAILED = "failed"
    NOT_APPLICABLE = "not_applicable"
    PARTIAL_SUCCESS = "partial_success"


@dataclass
class LayerMetadata:
    """Metadata about a layer processing operation"""
    layer_name: str
    result: LayerResult
    input_type: str
    output_type: str
    bytes_processed: int
    processing_time: float
    confidence_score: float  # 0.0 to 1.0
    additional_info: Dict[str, Any]


@dataclass
class LayerOutput:
    """Output from a layer processing operation"""
    data: Union[str, bytes, Any]
    metadata: LayerMetadata
    success: bool
    error_message: Optional[str] = None


class BaseLayer(ABC):
    """
    Abstract base class for all deobfuscation layers.
    
    Each concrete layer must implement the detection and processing methods
    to handle a specific type of obfuscation technique.
    """
    
    def __init__(self, name: str, priority: int = 50):
        """
        Initialize the base layer.
        
        Args:
            name: Human-readable name of the layer
            priority: Processing priority (lower numbers = higher priority)
        """
        self.name = name
        self.priority = priority
        self.statistics = {
            "total_processed": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "total_bytes_processed": 0
        }
    
    @abstractmethod
    def can_handle(self, data: Union[str, bytes]) -> Tuple[bool, float]:
        """
        Determine if this layer can handle the given data.
        
        Args:
            data: The input data to analyze
            
        Returns:
            Tuple of (can_handle: bool, confidence: float)
            confidence should be between 0.0 and 1.0
        """
        pass
    
    @abstractmethod
    def process(self, data: Union[str, bytes]) -> LayerOutput:
        """
        Process the input data and attempt to deobfuscate it.
        
        Args:
            data: The obfuscated data to process
            
        Returns:
            LayerOutput containing the processed data and metadata
        """
        pass
    
    def get_patterns(self) -> Dict[str, str]:
        """
        Get the regex patterns this layer uses for detection.
        
        Returns:
            Dictionary mapping pattern names to regex strings
        """
        return {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get processing statistics for this layer.
        
        Returns:
            Dictionary containing processing statistics
        """
        return self.statistics.copy()
    
    def reset_statistics(self) -> None:
        """Reset all processing statistics."""
        self.statistics = {
            "total_processed": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "total_bytes_processed": 0
        }
    
    def _update_statistics(self, success: bool, bytes_processed: int) -> None:
        """
        Update internal statistics after processing.
        
        Args:
            success: Whether the operation was successful
            bytes_processed: Number of bytes processed
        """
        self.statistics["total_processed"] += 1
        self.statistics["total_bytes_processed"] += bytes_processed
        
        if success:
            self.statistics["successful_operations"] += 1
        else:
            self.statistics["failed_operations"] += 1
    
    def _create_metadata(
        self,
        result: LayerResult,
        input_type: str,
        output_type: str,
        bytes_processed: int,
        processing_time: float,
        confidence_score: float,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> LayerMetadata:
        """
        Create metadata for a layer operation.
        
        Args:
            result: The result of the operation
            input_type: Type of input data
            output_type: Type of output data
            bytes_processed: Number of bytes processed
            processing_time: Time taken for processing
            confidence_score: Confidence in the result (0.0 to 1.0)
            additional_info: Additional information about the operation
            
        Returns:
            LayerMetadata object
        """
        return LayerMetadata(
            layer_name=self.name,
            result=result,
            input_type=input_type,
            output_type=output_type,
            bytes_processed=bytes_processed,
            processing_time=processing_time,
            confidence_score=confidence_score,
            additional_info=additional_info or {}
        )
    
    def __str__(self) -> str:
        """String representation of the layer."""
        return f"{self.__class__.__name__}(name='{self.name}', priority={self.priority})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the layer."""
        return (f"{self.__class__.__name__}("
                f"name='{self.name}', "
                f"priority={self.priority}, "
                f"processed={self.statistics['total_processed']})")
