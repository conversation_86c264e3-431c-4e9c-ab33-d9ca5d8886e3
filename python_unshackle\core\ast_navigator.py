"""
AST Navigator for PyThon-Unshackle

This module provides powerful AST (Abstract Syntax Tree) manipulation and analysis
capabilities for safely inspecting and extracting information from Python code
without executing it.

The ASTNavigator is the core safety mechanism that allows us to analyze exec(),
eval(), and other potentially dangerous code constructs without running them.
"""

import ast
import re
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass


@dataclass
class ExecCallInfo:
    """Information about an exec() or eval() call found in AST"""
    node: ast.AST
    function_name: str  # 'exec' or 'eval'
    argument: Any
    argument_type: str
    line_number: int
    col_offset: int
    extracted_code: Optional[str] = None


@dataclass
class FunctionCallInfo:
    """Information about a function call found in AST"""
    node: ast.Call
    function_name: str
    args: List[Any]
    keywords: Dict[str, Any]
    line_number: int
    col_offset: int


class ASTNavigator:
    """
    Powerful AST manipulation and search engine for safe code analysis.
    
    This class provides methods to:
    - Parse Python code into AST safely
    - Search for specific patterns (exec, eval, function calls)
    - Extract string literals and encoded data
    - Analyze code structure without execution
    """
    
    def __init__(self):
        self.last_parsed_ast = None
        self.parsing_errors = []
    
    def parse_code(self, code: str) -> Optional[ast.AST]:
        """
        Safely parse Python code into an AST.
        
        Args:
            code: Python source code to parse
            
        Returns:
            AST object if successful, None if parsing failed
        """
        try:
            # Clean the code first
            code = self._clean_code(code)
            
            # Parse the code
            tree = ast.parse(code)
            self.last_parsed_ast = tree
            return tree
            
        except SyntaxError as e:
            self.parsing_errors.append(f"Syntax error: {e}")
            return None
        except Exception as e:
            self.parsing_errors.append(f"Parsing error: {e}")
            return None
    
    def find_exec_calls(self, code: str) -> List[ExecCallInfo]:
        """
        Find all exec() and eval() calls in the code.
        
        Args:
            code: Python source code to analyze
            
        Returns:
            List of ExecCallInfo objects
        """
        tree = self.parse_code(code)
        if not tree:
            return []
        
        exec_calls = []
        
        navigator = self  # Reference to outer instance

        class ExecVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                if isinstance(node.func, ast.Name):
                    if node.func.id in ('exec', 'eval'):
                        if node.args:
                            arg = node.args[0]
                            exec_info = ExecCallInfo(
                                node=node,
                                function_name=node.func.id,
                                argument=arg,
                                argument_type=type(arg).__name__,
                                line_number=getattr(node, 'lineno', 0),
                                col_offset=getattr(node, 'col_offset', 0)
                            )

                            # Try to extract the actual code
                            extracted = navigator._extract_argument_value(arg)
                            if extracted:
                                exec_info.extracted_code = extracted

                            exec_calls.append(exec_info)

                self.generic_visit(node)
        
        visitor = ExecVisitor()
        visitor.visit(tree)
        return exec_calls
    
    def find_function_calls(self, code: str, function_names: Set[str]) -> List[FunctionCallInfo]:
        """
        Find all calls to specific functions.
        
        Args:
            code: Python source code to analyze
            function_names: Set of function names to search for
            
        Returns:
            List of FunctionCallInfo objects
        """
        tree = self.parse_code(code)
        if not tree:
            return []
        
        function_calls = []
        navigator = self  # Reference to outer instance

        class FunctionVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                func_name = navigator._get_function_name(node.func)
                if func_name in function_names:
                    # Extract arguments
                    args = []
                    for arg in node.args:
                        args.append(navigator._extract_argument_value(arg))

                    # Extract keyword arguments
                    keywords = {}
                    for keyword in node.keywords:
                        keywords[keyword.arg] = navigator._extract_argument_value(keyword.value)

                    call_info = FunctionCallInfo(
                        node=node,
                        function_name=func_name,
                        args=args,
                        keywords=keywords,
                        line_number=getattr(node, 'lineno', 0),
                        col_offset=getattr(node, 'col_offset', 0)
                    )
                    function_calls.append(call_info)

                self.generic_visit(node)
        
        visitor = FunctionVisitor()
        visitor.visit(tree)
        return function_calls
    
    def extract_string_literals(self, code: str) -> List[str]:
        """
        Extract all string literals from the code.
        
        Args:
            code: Python source code to analyze
            
        Returns:
            List of string literals found in the code
        """
        tree = self.parse_code(code)
        if not tree:
            return []
        
        strings = []

        class StringVisitor(ast.NodeVisitor):
            def visit_Constant(self, node):  # Python >= 3.8
                if isinstance(node.value, str):
                    strings.append(node.value)
                self.generic_visit(node)
        
        visitor = StringVisitor()
        visitor.visit(tree)
        return strings
    
    def _extract_argument_value(self, node: ast.AST) -> Any:
        """
        Extract the value from an AST node if possible.
        
        Args:
            node: AST node to extract value from
            
        Returns:
            Extracted value or None if not extractable
        """
        if isinstance(node, ast.Constant):  # Python >= 3.8
            return node.value
        elif isinstance(node, ast.Name):
            return f"<variable:{node.id}>"
        elif isinstance(node, ast.Call):
            func_name = self._get_function_name(node.func)
            return f"<function_call:{func_name}>"
        else:
            return f"<{type(node).__name__}>"
    
    def _get_function_name(self, node: ast.AST) -> str:
        """
        Get the function name from a function call node.
        
        Args:
            node: AST node representing a function
            
        Returns:
            Function name as string
        """
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_function_name(node.value)}.{node.attr}"
        else:
            return f"<unknown:{type(node).__name__}>"
    
    def _clean_code(self, code: str) -> str:
        """
        Clean and prepare code for parsing.
        
        Args:
            code: Raw code string
            
        Returns:
            Cleaned code string
        """
        # Remove common encoding declarations that might interfere
        code = re.sub(r'^\s*#.*?coding[:=]\s*([-\w.]+)', '', code, flags=re.MULTILINE)
        
        # Remove shebang lines
        code = re.sub(r'^#!.*$', '', code, flags=re.MULTILINE)
        
        # Normalize line endings
        code = code.replace('\r\n', '\n').replace('\r', '\n')
        
        return code.strip()
    
    def get_last_parsing_errors(self) -> List[str]:
        """
        Get the parsing errors from the last operation.
        
        Returns:
            List of error messages
        """
        return self.parsing_errors.copy()
    
    def clear_errors(self) -> None:
        """Clear all stored parsing errors."""
        self.parsing_errors.clear()
