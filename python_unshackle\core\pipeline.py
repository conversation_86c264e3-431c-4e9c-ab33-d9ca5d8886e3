"""
Pipeline Orchestrator for PyThon-Unshackle

This module implements the core recursive pipeline system that coordinates
the multi-layer deobfuscation process. It's the "conductor" that manages
the entire deobfuscation workflow.

The pipeline follows the "onion peeling" methodology:
1. Detect obfuscation patterns in the input
2. Apply appropriate deobfuscation layers
3. Recursively process the output until no more layers can be peeled
4. Return the final clean source code
"""

import time
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from pathlib import Path

from .detector import PatternDetector, DetectionResult, ObfuscationType
from .ast_navigator import ASTNavigator
from ..utils.logger import UnshackleLogger
from ..layers.base_layer import BaseLayer, LayerOutput, LayerResult


@dataclass
class PipelineResult:
    """Result of the complete deobfuscation pipeline"""
    success: bool
    final_code: Optional[str]
    layers_applied: List[str]
    total_iterations: int
    processing_time: float
    error_message: Optional[str] = None
    intermediate_results: List[Any] = None


@dataclass
class PipelineConfig:
    """Configuration for the deobfuscation pipeline"""
    max_iterations: int = 10
    min_confidence_threshold: float = 0.5
    enable_recursive_processing: bool = True
    save_intermediate_results: bool = False
    timeout_seconds: Optional[float] = None


class DeobfuscationPipeline:
    """
    Core pipeline orchestrator for multi-layer deobfuscation.
    
    Manages the entire deobfuscation workflow:
    - Pattern detection and analysis
    - Layer selection and application
    - Recursive processing
    - Result validation and formatting
    """
    
    def __init__(self, config: Optional[PipelineConfig] = None, logger: Optional[UnshackleLogger] = None):
        """
        Initialize the deobfuscation pipeline.
        
        Args:
            config: Pipeline configuration
            logger: Logger instance for reporting
        """
        self.config = config or PipelineConfig()
        self.logger = logger or UnshackleLogger()
        self.detector = PatternDetector()
        self.ast_navigator = ASTNavigator()
        
        # Registry of available layers
        self.layer_registry: Dict[str, BaseLayer] = {}
        self._register_default_layers()
        
        # Pipeline state
        self.current_iteration = 0
        self.processing_start_time = 0.0
        self.intermediate_results = []
    
    def _register_default_layers(self) -> None:
        """Register the default deobfuscation layers."""
        # Import layers here to avoid circular imports
        try:
            from ..layers.encoding_peeler import EncodingPeeler
            from ..layers.compression_peeler import CompressionPeeler
            from ..layers.serialization_peeler import SerializationPeeler
            from ..layers.execution_peeler import ExecutionPeeler
            
            # Register layers with their priorities
            self.register_layer(ExecutionPeeler())  # Highest priority
            self.register_layer(EncodingPeeler())
            self.register_layer(CompressionPeeler())
            self.register_layer(SerializationPeeler())  # Lowest priority
            
        except ImportError as e:
            self.logger.warning(f"Could not import some layers: {e}")
    
    def register_layer(self, layer: BaseLayer) -> None:
        """
        Register a deobfuscation layer.
        
        Args:
            layer: Layer instance to register
        """
        self.layer_registry[layer.__class__.__name__] = layer
        self.logger.debug(f"Registered layer: {layer.name}")
    
    def deobfuscate_file(self, file_path: str) -> PipelineResult:
        """
        Deobfuscate a Python file.
        
        Args:
            file_path: Path to the obfuscated Python file
            
        Returns:
            PipelineResult containing the deobfuscation results
        """
        try:
            # Read the file
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # Try to decode as UTF-8, fallback to bytes
            try:
                text_data = data.decode('utf-8')
            except UnicodeDecodeError:
                text_data = data
            
            self.logger.start_deobfuscation(file_path)
            return self.deobfuscate(text_data)
            
        except FileNotFoundError:
            error_msg = f"File not found: {file_path}"
            self.logger.error(error_msg)
            return PipelineResult(
                success=False,
                final_code=None,
                layers_applied=[],
                total_iterations=0,
                processing_time=0.0,
                error_message=error_msg
            )
        except Exception as e:
            error_msg = f"Error reading file: {e}"
            self.logger.error(error_msg)
            return PipelineResult(
                success=False,
                final_code=None,
                layers_applied=[],
                total_iterations=0,
                processing_time=0.0,
                error_message=error_msg
            )
    
    def deobfuscate(self, data: Union[str, bytes]) -> PipelineResult:
        """
        Deobfuscate the given data using the recursive pipeline.
        
        Args:
            data: Obfuscated data to process
            
        Returns:
            PipelineResult containing the deobfuscation results
        """
        self.processing_start_time = time.time()
        self.current_iteration = 0
        self.intermediate_results = []
        layers_applied = []
        
        current_data = data
        
        try:
            # Main deobfuscation loop
            while self.current_iteration < self.config.max_iterations:
                self.current_iteration += 1
                
                # Check timeout
                if self.config.timeout_seconds:
                    elapsed = time.time() - self.processing_start_time
                    if elapsed > self.config.timeout_seconds:
                        raise TimeoutError(f"Pipeline timeout after {elapsed:.2f} seconds")
                
                self.logger.debug(f"Starting iteration {self.current_iteration}")
                
                # Detect obfuscation patterns
                detection_results = self.detector.detect_obfuscation(current_data)
                
                if not detection_results:
                    self.logger.debug("No obfuscation patterns detected")
                    break
                
                # Filter by confidence threshold
                high_confidence_results = [
                    r for r in detection_results 
                    if r.confidence >= self.config.min_confidence_threshold
                ]
                
                if not high_confidence_results:
                    self.logger.debug("No high-confidence obfuscation patterns found")
                    break
                
                # Apply the best matching layer
                layer_applied = False
                for result in high_confidence_results:
                    layer_output = self._apply_best_layer(current_data, result)
                    
                    if layer_output and layer_output.success:
                        current_data = layer_output.data
                        layers_applied.append(layer_output.metadata.layer_name)
                        layer_applied = True
                        
                        # Save intermediate result if configured
                        if self.config.save_intermediate_results:
                            self.intermediate_results.append({
                                'iteration': self.current_iteration,
                                'layer': layer_output.metadata.layer_name,
                                'data': current_data,
                                'metadata': layer_output.metadata
                            })
                        
                        break  # Process one layer per iteration
                
                if not layer_applied:
                    self.logger.debug("No layers could process the current data")
                    break
                
                # Check if we should continue recursively
                if not self.config.enable_recursive_processing:
                    break
            
            # Calculate final results
            processing_time = time.time() - self.processing_start_time
            
            # Determine if we have clean Python code
            final_code = self._extract_final_code(current_data)
            success = final_code is not None

            # Debug logging
            self.logger.debug(f"Final data type: {type(current_data)}")
            self.logger.debug(f"Final data length: {len(str(current_data))}")
            self.logger.debug(f"Final code extracted: {success}")
            if final_code:
                self.logger.debug(f"Final code preview: {str(final_code)[:200]}...")

            if success:
                self.logger.end_deobfuscation(True)
            else:
                self.logger.end_deobfuscation(False)
                # Try to save whatever we got for debugging
                if current_data:
                    self.logger.debug(f"Raw final data: {str(current_data)[:500]}...")
            
            return PipelineResult(
                success=success,
                final_code=final_code,
                layers_applied=layers_applied,
                total_iterations=self.current_iteration,
                processing_time=processing_time,
                intermediate_results=self.intermediate_results if self.config.save_intermediate_results else None
            )
            
        except Exception as e:
            processing_time = time.time() - self.processing_start_time
            error_msg = f"Pipeline error: {e}"
            self.logger.error(error_msg)
            
            return PipelineResult(
                success=False,
                final_code=None,
                layers_applied=layers_applied,
                total_iterations=self.current_iteration,
                processing_time=processing_time,
                error_message=error_msg,
                intermediate_results=self.intermediate_results if self.config.save_intermediate_results else None
            )

    def _apply_best_layer(self, data: Union[str, bytes], detection_result: DetectionResult) -> Optional[LayerOutput]:
        """
        Apply the best matching layer for the detected obfuscation type.

        Args:
            data: Data to process
            detection_result: Detection result indicating obfuscation type

        Returns:
            LayerOutput if successful, None otherwise
        """
        # Get recommended layers for this detection
        recommended_layers = self.detector.get_recommended_layers([detection_result])

        for layer_name in recommended_layers:
            if layer_name in self.layer_registry:
                layer = self.layer_registry[layer_name]

                # Check if layer can handle this data
                can_handle, confidence = layer.can_handle(data)

                if can_handle and confidence >= self.config.min_confidence_threshold:
                    self.logger.layer_start(layer.name, f"Detected {detection_result.obfuscation_type.value}")

                    # Process the data
                    layer_output = layer.process(data)

                    if layer_output.success:
                        self.logger.layer_success(layer.name, f"Processed {detection_result.obfuscation_type.value}")
                        return layer_output
                    else:
                        self.logger.layer_failed(layer.name, f"Failed to process {detection_result.obfuscation_type.value}")

        return None

    def _extract_final_code(self, data: Union[str, bytes]) -> Optional[str]:
        """
        Extract the final clean Python code from the processed data.

        Args:
            data: Final processed data

        Returns:
            Clean Python code string if valid, None otherwise
        """
        # Convert to string if bytes
        if isinstance(data, bytes):
            try:
                code_str = data.decode('utf-8')
            except UnicodeDecodeError:
                # Try latin-1 as fallback
                try:
                    code_str = data.decode('latin-1')
                except:
                    return None
        else:
            code_str = str(data)

        # Clean the code string
        code_str = code_str.strip()

        # If it's empty, return None
        if not code_str:
            return None

        # Check if it looks like Python code (more lenient)
        if self._looks_like_python_code(code_str):
            return code_str

        # Try to validate that it's valid Python code
        try:
            tree = self.ast_navigator.parse_code(code_str)
            if tree:
                return code_str
        except:
            # If AST parsing fails, but it looks like code, still return it
            if any(keyword in code_str for keyword in ['def ', 'class ', 'import ', 'print', 'if ', 'for ', 'while ', '#']):
                return code_str

        return None

    def _looks_like_python_code(self, text: str) -> bool:
        """Check if text looks like Python code."""
        # Check for common Python patterns
        python_indicators = [
            'def ', 'class ', 'import ', 'from ', 'print(', 'print ',
            'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except',
            'return ', 'yield ', 'with ', 'lambda ', '# ', 'pass', 'break',
            'continue', 'raise ', 'assert ', 'global ', 'nonlocal '
        ]

        # Check if any Python keywords are present
        text_lower = text.lower()
        for indicator in python_indicators:
            if indicator in text_lower:
                return True

        # Check for Python-like structure (indentation, colons, etc.)
        lines = text.split('\n')
        has_indentation = any(line.startswith('    ') or line.startswith('\t') for line in lines)
        has_colons = ':' in text
        has_parentheses = '(' in text and ')' in text

        # If it has multiple Python-like features, consider it code
        features = sum([has_indentation, has_colons, has_parentheses])
        return features >= 2

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about the pipeline and layers.

        Returns:
            Dictionary containing pipeline and layer statistics
        """
        stats = {
            'pipeline': {
                'total_iterations': self.current_iteration,
                'processing_time': time.time() - self.processing_start_time if self.processing_start_time else 0,
                'registered_layers': len(self.layer_registry)
            },
            'layers': {}
        }

        for layer_name, layer in self.layer_registry.items():
            stats['layers'][layer_name] = layer.get_statistics()

        return stats

    def reset_pipeline(self) -> None:
        """Reset the pipeline state and layer statistics."""
        self.current_iteration = 0
        self.processing_start_time = 0.0
        self.intermediate_results = []

        for layer in self.layer_registry.values():
            layer.reset_statistics()

    def save_result(self, result: PipelineResult, output_path: str) -> bool:
        """
        Save the deobfuscation result to a file.

        Args:
            result: Pipeline result to save
            output_path: Path to save the result

        Returns:
            True if successful, False otherwise
        """
        try:
            if result.final_code:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(result.final_code)

                self.logger.success(f"Result saved to '{output_path}'")
                return True
            else:
                self.logger.error("No final code to save")
                return False

        except Exception as e:
            self.logger.error(f"Failed to save result: {e}")
            return False
