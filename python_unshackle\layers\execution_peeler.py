"""
Execution Peeler for PyThon-Unshackle

This module implements the most sophisticated peeler that safely handles
exec() and eval() calls using AST analysis instead of execution. This is
the pinnacle of safety and innovation in the deobfuscation pipeline.

SAFETY PRINCIPLE: Never execute suspicious code directly. Always use AST
analysis to extract the hidden code without running it.
"""

import ast
import re
import time
from typing import Union, Tuple, List, Dict, Any, Optional

from .base_layer import BaseLayer, LayerOutput, LayerResult, LayerMetadata
from ..core.ast_navigator import ASTNavigator, ExecCallInfo


class ExecutionPeeler(BaseLayer):
    """
    Execution layer peeler for exec/eval obfuscation techniques.
    
    This is the most advanced peeler that uses AST analysis to safely
    extract code from exec() and eval() calls without executing them.
    
    Handles:
    - exec() calls with extractable arguments
    - eval() calls with code strings
    - Nested exec/eval structures
    - Complex argument patterns
    """
    
    def __init__(self):
        super().__init__("Execution Peeler", priority=10)  # Highest priority
        self.ast_navigator = ASTNavigator()
        self._init_patterns()
    
    def _init_patterns(self) -> None:
        """Initialize regex patterns for execution detection."""
        self.patterns = {
            'exec_call': r'exec\s*\(',
            'eval_call': r'eval\s*\(',
            'compile_call': r'compile\s*\(',
            'exec_with_string': r'exec\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
            'eval_with_string': r'eval\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
        }
    
    def can_handle(self, data: Union[str, bytes]) -> Tuple[bool, float]:
        """
        Determine if this layer can handle the given data.
        
        Args:
            data: Input data to analyze
            
        Returns:
            Tuple of (can_handle: bool, confidence: float)
        """
        if isinstance(data, bytes):
            try:
                text_data = data.decode('utf-8', errors='ignore')
            except:
                return False, 0.0
        else:
            text_data = data
        
        confidence = 0.0
        evidence_count = 0
        
        # Check for exec/eval patterns
        for pattern_name, pattern in self.patterns.items():
            if re.search(pattern, text_data, re.IGNORECASE | re.DOTALL):
                confidence += 0.4
                evidence_count += 1
        
        # Use AST navigator for more sophisticated detection
        exec_calls = self.ast_navigator.find_exec_calls(text_data)
        if exec_calls:
            confidence += 0.6
            evidence_count += len(exec_calls)
            
            # Higher confidence if we can extract the code
            for call in exec_calls:
                if call.extracted_code:
                    confidence += 0.3
        
        return evidence_count > 0, min(confidence, 1.0)
    
    def process(self, data: Union[str, bytes]) -> LayerOutput:
        """
        Process the input data and extract code from exec/eval calls.
        
        Args:
            data: Code containing exec/eval calls
            
        Returns:
            LayerOutput containing the extracted code and metadata
        """
        start_time = time.time()
        
        if isinstance(data, bytes):
            try:
                text_data = data.decode('utf-8')
            except UnicodeDecodeError:
                return self._create_failed_output("Cannot decode bytes to text", start_time, len(data))
        else:
            text_data = data
        
        input_size = len(text_data.encode('utf-8'))
        
        # Find all exec/eval calls using AST analysis
        exec_calls = self.ast_navigator.find_exec_calls(text_data)
        
        if not exec_calls:
            return self._create_failed_output("No exec/eval calls found", start_time, input_size)
        
        # Process each exec/eval call
        extracted_codes = []
        processing_info = []
        
        for call in exec_calls:
            extracted = self._extract_code_from_call(call, text_data)
            if extracted:
                extracted_codes.append(extracted)
                processing_info.append({
                    'function': call.function_name,
                    'line': call.line_number,
                    'argument_type': call.argument_type,
                    'extracted_length': len(extracted)
                })
        
        if not extracted_codes:
            return self._create_failed_output("Could not extract code from exec/eval calls", start_time, input_size)
        
        # Combine extracted codes
        final_code = self._combine_extracted_codes(extracted_codes)
        
        processing_time = time.time() - start_time
        self._update_statistics(True, input_size)
        
        return LayerOutput(
            data=final_code,
            metadata=self._create_metadata(
                LayerResult.SUCCESS,
                "exec_eval_code",
                "extracted_code",
                input_size,
                processing_time,
                0.9,
                {
                    "exec_calls_found": len(exec_calls),
                    "codes_extracted": len(extracted_codes),
                    "processing_info": processing_info
                }
            ),
            success=True
        )
    
    def _extract_code_from_call(self, call: ExecCallInfo, full_text: str) -> Optional[str]:
        """
        Extract code from a specific exec/eval call.
        
        Args:
            call: Information about the exec/eval call
            full_text: Full source text for context
            
        Returns:
            Extracted code string if successful, None otherwise
        """
        # If AST navigator already extracted the code, use it
        if call.extracted_code:
            return call.extracted_code
        
        # Try different extraction methods based on argument type
        if call.argument_type == 'Str' or call.argument_type == 'Constant':
            # Direct string argument
            return self._extract_from_string_argument(call, full_text)
        
        elif call.argument_type == 'Name':
            # Variable argument - try to find the variable definition
            return self._extract_from_variable_argument(call, full_text)
        
        elif call.argument_type == 'Call':
            # Function call argument - analyze the function call
            return self._extract_from_function_call_argument(call, full_text)
        
        elif call.argument_type == 'BinOp':
            # Binary operation (like string concatenation)
            return self._extract_from_binary_operation(call, full_text)
        
        return None
    
    def _extract_from_string_argument(self, call: ExecCallInfo, full_text: str) -> Optional[str]:
        """Extract code from a direct string argument."""
        try:
            # Use regex to find the string content
            line_start = self._find_line_start(full_text, call.line_number)
            line_content = self._get_line_content(full_text, call.line_number)
            
            # Look for string patterns in the exec/eval call
            patterns = [
                rf'{call.function_name}\s*\(\s*[\'"]([^\'"]*)[\'"]',
                rf'{call.function_name}\s*\(\s*"""([^"]*)"""',
                rf'{call.function_name}\s*\(\s*\'\'\'([^\']*)\'\'\'',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, line_content, re.DOTALL)
                if match:
                    return match.group(1)
            
        except Exception:
            pass
        
        return None
    
    def _extract_from_variable_argument(self, call: ExecCallInfo, full_text: str) -> Optional[str]:
        """Extract code from a variable argument by finding the variable definition."""
        try:
            # Parse the full text to find variable assignments
            tree = self.ast_navigator.parse_code(full_text)
            if not tree:
                return None
            
            # Find assignments to the variable
            variable_name = self._get_variable_name_from_call(call)
            if not variable_name:
                return None
            
            # Look for assignments
            assignments = self._find_variable_assignments(tree, variable_name)
            
            for assignment in assignments:
                if isinstance(assignment, str):
                    return assignment
            
        except Exception:
            pass
        
        return None
    
    def _extract_from_function_call_argument(self, call: ExecCallInfo, full_text: str) -> Optional[str]:
        """Extract code from a function call argument (like base64.b64decode())."""
        try:
            # This is complex - the argument is itself a function call
            # We need to identify what function is being called and potentially
            # extract its result without executing it
            
            # For now, we'll try to identify common patterns
            line_content = self._get_line_content(full_text, call.line_number)
            
            # Look for common obfuscation patterns
            if 'base64.b64decode' in line_content:
                return self._extract_from_base64_call(line_content)
            elif 'zlib.decompress' in line_content:
                return self._extract_from_zlib_call(line_content)
            elif 'marshal.loads' in line_content:
                return self._extract_from_marshal_call(line_content)
            
        except Exception:
            pass
        
        return None
    
    def _extract_from_binary_operation(self, call: ExecCallInfo, full_text: str) -> Optional[str]:
        """Extract code from binary operations (like string concatenation)."""
        try:
            # This handles cases like exec("print(" + "hello" + ")")
            line_content = self._get_line_content(full_text, call.line_number)
            
            # Try to evaluate simple string concatenations
            # This is safe because we're only dealing with string literals
            return self._evaluate_string_concatenation(line_content)
            
        except Exception:
            pass
        
        return None
    
    def _extract_from_base64_call(self, line_content: str) -> Optional[str]:
        """Extract code from base64.b64decode() calls."""
        pattern = r'base64\.b64decode\s*\(\s*[\'"]([A-Za-z0-9+/=]+)[\'"]\s*\)'
        match = re.search(pattern, line_content)
        if match:
            try:
                import base64
                decoded = base64.b64decode(match.group(1))
                return decoded.decode('utf-8')
            except:
                pass
        return None
    
    def _extract_from_zlib_call(self, line_content: str) -> Optional[str]:
        """Extract code from zlib.decompress() calls."""
        # This is more complex as zlib data is binary
        # For now, we'll return a placeholder
        return "# zlib.decompress() call detected - needs binary data processing"
    
    def _extract_from_marshal_call(self, line_content: str) -> Optional[str]:
        """Extract code from marshal.loads() calls."""
        # This is complex as marshal data is binary and contains code objects
        return "# marshal.loads() call detected - needs code object decompilation"
    
    def _find_line_start(self, text: str, line_number: int) -> int:
        """Find the character position where a line starts."""
        lines = text.split('\n')
        if line_number <= len(lines):
            return sum(len(line) + 1 for line in lines[:line_number-1])
        return 0
    
    def _get_line_content(self, text: str, line_number: int) -> str:
        """Get the content of a specific line."""
        lines = text.split('\n')
        if 1 <= line_number <= len(lines):
            return lines[line_number - 1]
        return ""
    
    def _get_variable_name_from_call(self, call: ExecCallInfo) -> Optional[str]:
        """Extract variable name from the call argument."""
        # This would need to analyze the AST node to get the variable name
        # For now, return None as a placeholder
        return None
    
    def _find_variable_assignments(self, tree: ast.AST, variable_name: str) -> List[str]:
        """Find all assignments to a specific variable."""
        assignments = []
        
        class AssignmentVisitor(ast.NodeVisitor):
            def visit_Assign(self, node):
                for target in node.targets:
                    if isinstance(target, ast.Name) and target.id == variable_name:
                        if isinstance(node.value, (ast.Str, ast.Constant)):
                            value = node.value.s if hasattr(node.value, 's') else node.value.value
                            if isinstance(value, str):
                                assignments.append(value)
                self.generic_visit(node)
        
        visitor = AssignmentVisitor()
        visitor.visit(tree)
        return assignments
    
    def _evaluate_string_concatenation(self, line_content: str) -> Optional[str]:
        """Safely evaluate string concatenation expressions."""
        # This is a simplified version - in practice, you'd want more robust parsing
        try:
            # Look for simple patterns like "string1" + "string2"
            pattern = r'[\'"]([^\'"]*)[\'"](?:\s*\+\s*[\'"]([^\'"]*)[\'"])*'
            matches = re.findall(r'[\'"]([^\'"]*)[\'"]', line_content)
            if matches:
                return ''.join(matches)
        except:
            pass
        
        return None
    
    def _combine_extracted_codes(self, codes: List[str]) -> str:
        """Combine multiple extracted code pieces."""
        if len(codes) == 1:
            return codes[0]
        
        # Combine multiple codes with separators
        combined = []
        for i, code in enumerate(codes):
            combined.append(f"# Extracted code block {i+1}")
            combined.append(code)
            combined.append("")
        
        return '\n'.join(combined)
    
    def _create_failed_output(self, error_msg: str, start_time: float, input_size: int) -> LayerOutput:
        """Create a failed LayerOutput."""
        processing_time = time.time() - start_time
        
        return LayerOutput(
            data=None,
            metadata=self._create_metadata(
                LayerResult.FAILED,
                "exec_eval_code",
                "none",
                input_size,
                processing_time,
                0.0,
                {"error": error_msg}
            ),
            success=False,
            error_message=error_msg
        )
