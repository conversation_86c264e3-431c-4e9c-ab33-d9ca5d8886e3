#!/usr/bin/env python3
"""
Advanced deobfuscator for complex marshal/Cython files
This script uses multiple techniques to extract the real code
"""

import marshal
import sys
import re
import ast
import dis
import binascii
from io import StringIO


def extract_raw_marshal_bytes(filename):
    """Extract the raw marshal bytes from the file."""
    try:
        with open(filename, 'rb') as f:
            content = f.read()
        
        # Look for the marshal.loads pattern in binary
        # Pattern: marshal.loads(b'...')
        pattern = rb'marshal\.loads\s*\(\s*b[\'"]([^\'"]*)[\'"]'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            binary_str = match.group(1)
            print(f"Found marshal pattern, raw length: {len(binary_str)}")
            return binary_str
        else:
            print("No marshal pattern found in binary")
            return None
            
    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def analyze_marshal_structure(binary_data):
    """Analyze the structure of marshal data."""
    print(f"\n=== MARSHAL STRUCTURE ANALYSIS ===")
    print(f"Raw data length: {len(binary_data)}")
    print(f"First 20 bytes (hex): {binascii.hexlify(binary_data[:20])}")
    print(f"First 20 bytes (repr): {repr(binary_data[:20])}")
    
    # Check marshal magic bytes
    if binary_data.startswith(b'c\x00\x00\x00'):
        print("✓ Detected marshal code object signature")
    else:
        print("⚠ Unknown marshal signature")
    
    # Try to find patterns
    patterns = {
        b'\x00\x00\x00': 'Null bytes (common in marshal)',
        b'\x83\x01': 'Tuple marker',
        b'\xa0\x05': 'Method call marker',
        b'\x17\x00': 'Binary operation marker',
    }
    
    for pattern, description in patterns.items():
        count = binary_data.count(pattern)
        if count > 0:
            print(f"Found {count}x {description}")


def try_different_marshal_approaches(binary_data):
    """Try different approaches to load marshal data."""
    print(f"\n=== TRYING DIFFERENT MARSHAL APPROACHES ===")
    
    approaches = [
        ("Direct marshal.loads", lambda d: marshal.loads(d)),
        ("Skip first 4 bytes", lambda d: marshal.loads(d[4:])),
        ("Skip first 8 bytes", lambda d: marshal.loads(d[8:])),
        ("Try as escaped string", lambda d: marshal.loads(d.decode('unicode_escape').encode('latin-1'))),
    ]
    
    for name, func in approaches:
        try:
            print(f"\nTrying: {name}")
            result = func(binary_data)
            print(f"✓ SUCCESS with {name}")
            print(f"Result type: {type(result)}")
            if hasattr(result, 'co_code'):
                print(f"Code object found: {result.co_name}")
                return result
            else:
                print(f"Result: {repr(result)[:100]}...")
        except Exception as e:
            print(f"✗ Failed: {e}")
    
    return None


def extract_strings_from_binary(binary_data):
    """Extract readable strings from binary data."""
    print(f"\n=== EXTRACTING STRINGS ===")
    
    # Look for Python-like strings
    patterns = [
        rb'def\s+\w+',
        rb'class\s+\w+',
        rb'import\s+\w+',
        rb'print\s*\(',
        rb'if\s+.*:',
        rb'for\s+.*:',
        rb'while\s+.*:',
    ]
    
    strings_found = []
    
    for pattern in patterns:
        matches = re.findall(pattern, binary_data)
        for match in matches:
            try:
                decoded = match.decode('utf-8', errors='ignore')
                if len(decoded) > 3:
                    strings_found.append(decoded)
            except:
                continue
    
    if strings_found:
        print("Found Python-like strings:")
        for s in strings_found[:10]:  # Show first 10
            print(f"  {repr(s)}")
    else:
        print("No Python-like strings found")
    
    # Look for any readable strings
    readable_strings = []
    current_string = b""
    
    for byte in binary_data:
        if 32 <= byte <= 126:  # Printable ASCII
            current_string += bytes([byte])
        else:
            if len(current_string) > 10:
                try:
                    decoded = current_string.decode('utf-8')
                    readable_strings.append(decoded)
                except:
                    pass
            current_string = b""
    
    if readable_strings:
        print(f"\nFound {len(readable_strings)} readable strings:")
        for s in readable_strings[:5]:  # Show first 5
            print(f"  {repr(s[:50])}...")


def hex_dump_analysis(binary_data):
    """Perform hex dump analysis."""
    print(f"\n=== HEX DUMP ANALYSIS ===")
    
    # Show hex dump of first 200 bytes
    print("First 200 bytes:")
    for i in range(0, min(200, len(binary_data)), 16):
        chunk = binary_data[i:i+16]
        hex_part = ' '.join(f'{b:02x}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        print(f"{i:04x}: {hex_part:<48} {ascii_part}")


def brute_force_decode(binary_data):
    """Try brute force decoding with different encodings."""
    print(f"\n=== BRUTE FORCE DECODING ===")
    
    encodings = ['utf-8', 'latin-1', 'cp1252', 'ascii', 'utf-16', 'utf-32']
    
    for encoding in encodings:
        try:
            decoded = binary_data.decode(encoding, errors='ignore')
            if any(keyword in decoded.lower() for keyword in ['def ', 'class ', 'import ', 'print']):
                print(f"✓ Found Python-like content with {encoding}:")
                print(f"  {repr(decoded[:100])}...")
        except Exception as e:
            print(f"✗ {encoding} failed: {e}")


def main():
    """Main function."""
    filename = "test.py"
    
    print(f"Advanced deobfuscation analysis of {filename}")
    print("=" * 60)
    
    # Extract raw marshal bytes
    binary_data = extract_raw_marshal_bytes(filename)
    if not binary_data:
        print("Failed to extract marshal data")
        return 1
    
    # Convert escape sequences to actual bytes
    try:
        actual_bytes = binary_data.decode('unicode_escape').encode('latin-1')
        print(f"Converted to actual bytes, length: {len(actual_bytes)}")
    except Exception as e:
        print(f"Error converting escape sequences: {e}")
        actual_bytes = binary_data
    
    # Perform various analyses
    analyze_marshal_structure(actual_bytes)
    
    # Try to load marshal data
    code_obj = try_different_marshal_approaches(actual_bytes)
    
    # Extract strings
    extract_strings_from_binary(actual_bytes)
    
    # Hex dump analysis
    hex_dump_analysis(actual_bytes)
    
    # Brute force decoding
    brute_force_decode(actual_bytes)
    
    if code_obj:
        print(f"\n=== CODE OBJECT ANALYSIS ===")
        print(f"Successfully loaded code object: {code_obj.co_name}")
        
        # Try to decompile
        try:
            import uncompyle6
            output = StringIO()
            uncompyle6.decompile(code_obj, output)
            result = output.getvalue()
            output.close()
            
            print("✓ DECOMPILATION SUCCESSFUL!")
            print("-" * 50)
            print(result)
            print("-" * 50)
            
            # Save result
            with open("test_advanced_decompiled.py", "w", encoding="utf-8") as f:
                f.write(result)
            print("Saved to test_advanced_decompiled.py")
            
        except Exception as e:
            print(f"Decompilation failed: {e}")
            
            # Show disassembly instead
            print("Showing disassembly:")
            dis.dis(code_obj)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
