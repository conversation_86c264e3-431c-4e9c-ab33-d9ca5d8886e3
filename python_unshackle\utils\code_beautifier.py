"""
Code Beautifier for PyThon-Unshackle

This module provides code beautification and formatting capabilities
for the final deobfuscated Python code output.
"""

import ast
import re
from typing import Optional, Dict, Any


class CodeBeautifier:
    """
    Code beautifier for cleaning and formatting deobfuscated Python code.
    
    Provides:
    - Code formatting and indentation
    - Comment cleanup
    - Import organization
    - Variable name improvement
    - Code structure optimization
    """
    
    def __init__(self):
        self.formatting_errors = []
    
    def beautify(self, code: str) -> str:
        """
        Beautify and format Python code.
        
        Args:
            code: Raw Python code to beautify
            
        Returns:
            Beautified Python code
        """
        self.formatting_errors.clear()
        
        try:
            # Step 1: Basic cleanup
            cleaned_code = self._basic_cleanup(code)
            
            # Step 2: Try to use autopep8 for PEP 8 formatting
            formatted_code = self._format_with_autopep8(cleaned_code)
            
            # Step 3: Try to use black for additional formatting
            if formatted_code:
                final_code = self._format_with_black(formatted_code)
                if final_code:
                    formatted_code = final_code
            
            # Step 4: Manual improvements
            if formatted_code:
                improved_code = self._manual_improvements(formatted_code)
                return improved_code
            else:
                # Fallback to manual formatting
                return self._manual_formatting(cleaned_code)
                
        except Exception as e:
            self.formatting_errors.append(f"Beautification error: {e}")
            return self._manual_formatting(code)
    
    def _basic_cleanup(self, code: str) -> str:
        """Perform basic code cleanup."""
        # Remove excessive blank lines
        lines = code.split('\n')
        cleaned_lines = []
        prev_blank = False
        
        for line in lines:
            stripped = line.strip()
            is_blank = len(stripped) == 0
            
            # Skip excessive blank lines
            if is_blank and prev_blank:
                continue
            
            cleaned_lines.append(line)
            prev_blank = is_blank
        
        # Remove trailing whitespace
        cleaned_lines = [line.rstrip() for line in cleaned_lines]
        
        # Ensure file ends with single newline
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    def _format_with_autopep8(self, code: str) -> Optional[str]:
        """Format code using autopep8."""
        try:
            import autopep8
            
            # Configure autopep8 options
            options = {
                'aggressive': 1,
                'max_line_length': 88,  # Black's default
                'experimental': True,
            }
            
            formatted = autopep8.fix_code(code, options=options)
            return formatted
            
        except ImportError:
            self.formatting_errors.append("autopep8 not available")
            return None
        except Exception as e:
            self.formatting_errors.append(f"autopep8 formatting failed: {e}")
            return None
    
    def _format_with_black(self, code: str) -> Optional[str]:
        """Format code using black."""
        try:
            import black
            
            # Configure black
            mode = black.FileMode(
                line_length=88,
                string_normalization=True,
                is_pyi=False,
            )
            
            formatted = black.format_str(code, mode=mode)
            return formatted
            
        except ImportError:
            self.formatting_errors.append("black not available")
            return None
        except Exception as e:
            self.formatting_errors.append(f"black formatting failed: {e}")
            return None
    
    def _manual_improvements(self, code: str) -> str:
        """Apply manual improvements to the code."""
        # Improve comments
        code = self._improve_comments(code)
        
        # Organize imports
        code = self._organize_imports(code)
        
        # Improve variable names if possible
        code = self._improve_variable_names(code)
        
        return code
    
    def _improve_comments(self, code: str) -> str:
        """Improve comment formatting and content."""
        lines = code.split('\n')
        improved_lines = []
        
        for line in lines:
            stripped = line.strip()
            
            # Improve decompilation comments
            if stripped.startswith('# Decompiled') or stripped.startswith('# Code object'):
                # Make these comments more prominent
                if not any(improved_lines[-3:]) if len(improved_lines) >= 3 else True:
                    improved_lines.append('')
                improved_lines.append('# ' + '=' * 50)
                improved_lines.append(line)
                improved_lines.append('# ' + '=' * 50)
                improved_lines.append('')
                continue
            
            # Clean up excessive comment markers
            if stripped.startswith('####'):
                cleaned = re.sub(r'^#+\s*', '# ', stripped)
                improved_lines.append(line.replace(stripped, cleaned))
                continue
            
            improved_lines.append(line)
        
        return '\n'.join(improved_lines)
    
    def _organize_imports(self, code: str) -> str:
        """Organize import statements."""
        try:
            tree = ast.parse(code)
            
            # Extract imports and other statements
            imports = []
            other_statements = []
            
            for node in tree.body:
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    imports.append(ast.unparse(node))
                else:
                    other_statements.append(ast.unparse(node))
            
            if imports:
                # Sort imports
                imports.sort()
                
                # Rebuild code with organized imports
                organized_parts = []
                
                # Add header comment if present
                lines = code.split('\n')
                header_comments = []
                for line in lines:
                    if line.strip().startswith('#') or not line.strip():
                        header_comments.append(line)
                    else:
                        break
                
                if header_comments:
                    organized_parts.extend(header_comments)
                    organized_parts.append('')
                
                # Add imports
                organized_parts.extend(imports)
                organized_parts.append('')
                organized_parts.append('')
                
                # Add other statements
                organized_parts.extend(other_statements)
                
                return '\n'.join(organized_parts)
        
        except Exception as e:
            self.formatting_errors.append(f"Import organization failed: {e}")
        
        return code
    
    def _improve_variable_names(self, code: str) -> str:
        """Improve variable names where possible."""
        # This is a basic implementation
        # In practice, you'd want more sophisticated analysis
        
        # Replace common obfuscated patterns
        replacements = {
            r'\b_+\b': 'temp_var',
            r'\bl{2,}\b': 'long_var',
            r'\bo{2,}\b': 'obj_var',
        }
        
        improved_code = code
        for pattern, replacement in replacements.items():
            improved_code = re.sub(pattern, replacement, improved_code)
        
        return improved_code
    
    def _manual_formatting(self, code: str) -> str:
        """Manual formatting as fallback."""
        lines = code.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            
            if not stripped:
                formatted_lines.append('')
                continue
            
            # Adjust indent level
            if stripped.startswith(('def ', 'class ', 'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except', 'finally:', 'with ')):
                formatted_lines.append('    ' * indent_level + stripped)
                if stripped.endswith(':'):
                    indent_level += 1
            elif stripped in ('else:', 'elif', 'except:', 'finally:'):
                indent_level = max(0, indent_level - 1)
                formatted_lines.append('    ' * indent_level + stripped)
                indent_level += 1
            elif stripped.startswith(('return', 'break', 'continue', 'pass', 'raise')):
                formatted_lines.append('    ' * indent_level + stripped)
            else:
                # Regular statement
                formatted_lines.append('    ' * indent_level + stripped)
            
            # Decrease indent after certain statements
            if stripped.endswith(':') and not stripped.startswith(('def ', 'class ', 'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except', 'finally:', 'with ')):
                pass  # Keep current indent
            elif any(stripped.startswith(kw) for kw in ['return', 'break', 'continue', 'pass', 'raise']):
                indent_level = max(0, indent_level - 1)
        
        return '\n'.join(formatted_lines)
    
    def get_formatting_errors(self) -> list:
        """Get formatting errors from the last beautification."""
        return self.formatting_errors.copy()
    
    def validate_syntax(self, code: str) -> bool:
        """Validate that the code has correct Python syntax."""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
        except Exception:
            return False
