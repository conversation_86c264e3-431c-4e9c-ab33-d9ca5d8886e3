# PyThon-Unshackle 🔓

**The Ultimate Multi-Stage Python Deobfuscation Tool**

A sophisticated, automated pipeline for reverse engineering and deobfuscating Python source code that has been obfuscated using various techniques including marshal, zlib, base64, exec, and other complex nested obfuscation methods.

## 🎯 Vision

PyThon-Unshackle is not just a tool—it's an intelligent, iterative reverse engineering pipeline. The goal is to automate the process of "peeling" layers of complexity that developers (or attackers) use to hide the true source code of Python files.

## 🏗️ Architecture

### The Layered "Onion" Approach
The tool assumes that obfuscation is built in layers (like an onion). Its function is to systematically peel each layer and pass the result to the next stage until reaching clean code.

### Core Design Philosophy
- **Pattern Detection & Heuristics**: Intelligent engine that recognizes obfuscation patterns
- **Recursive Deobfuscation**: After unpacking one layer, the pipeline re-runs itself on the new output
- **Safe Decompilation**: Never executes suspicious code directly—relies on AST analysis instead
- **Extensible Architecture**: Easy to add new "peelers" for future obfuscation techniques

## 🚀 Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run deobfuscation
python main.py unshackle samples/sample_layer3_exec_nested.py

# Verbose mode with detailed reporting
python main.py unshackle samples/sample_layer3_exec_nested.py --verbose
```

## 📁 Project Structure

```
python_unshackle/
├── main.py                     # Powerful CLI interface
├── core/
│   ├── pipeline.py             # Operations coordinator and recursive pipeline
│   ├── detector.py             # Pattern detection and heuristics engine
│   └── ast_navigator.py        # Powerful AST manipulation and search tool
│
├── layers/                     # Heart of the project: each layer is a "peeler"
│   ├── base_layer.py           # Abstract base class (ABC) defining layer interface
│   ├── encoding_peeler.py      # Base64, Hex, URL encoding decoder
│   ├── compression_peeler.py   # zlib, gzip, bz2 decompression
│   ├── serialization_peeler.py # marshal & pickle unpacker
│   └── execution_peeler.py     # exec/eval deobfuscation via AST analysis
│
├── decompiler/                 # Bytecode decompilation module
│   ├── pyc_decompiler.py       # Interface for libraries like uncompyle6
│   └── marshal_decompiler.py   # Specialized in code objects from marshal
│
├── utils/
│   ├── code_beautifier.py      # Clean and format final code
│   └── logger.py               # Log every step in the unpacking process
│
└── samples/                    # Complex samples for testing
    ├── sample_layer1_base64.py
    ├── sample_layer2_zlib_marshal.py
    └── sample_layer3_exec_nested.py
```

## 🔧 Key Features

- **Fully Automated Pipeline**: Automatically detects and unpacks exec, base64, zlib, marshal, and bytecode
- **AST-Based exec Analysis**: Safely handles exec calls using AST analysis instead of execution
- **Marshal Object Decompilation**: Converts code objects back to readable Python source
- **Verbose Reporting**: Detailed step-by-step progress reports
- **Extensible Peelers**: Easy to add new deobfuscation modules

## 📊 Example Output

```
[INFO] Starting deobfuscation for 'sample_layer3.py'...
[LAYER 1] Detected 'exec' call. Extracting payload using AST analysis... Success.
[LAYER 2] Payload identified as Base64. Decoding... Success.
[LAYER 3] Decoded data identified as ZLIB compressed. Decompressing... Success.
[LAYER 4] Decompressed data is a MARSHAL object. Loading code object... Success.
[DECOMPILE] Decompiling final code object...
----------------- DECOMPILED SOURCE -----------------
def hello_world():
    print("I was finally unshackled!")

hello_world()
-----------------------------------------------------
[SUCCESS] Deobfuscation complete. Output saved to 'sample_layer3.py.unshackled'.
```

## 🛡️ Safety First

PyThon-Unshackle follows strict safety principles:
- **Never executes suspicious code directly**
- **Uses AST analysis for safe code inspection**
- **Sandboxed decompilation environment**
- **Comprehensive logging of all operations**

## 🎯 Roadmap

- [x] Phase 1: Solid foundation and core architecture
- [ ] Phase 2: Basic encoding and compression peelers
- [ ] Phase 3: Marshal decompilation capabilities
- [ ] Phase 4: Advanced exec/eval AST analysis
- [ ] Phase 5: CLI interface and reporting system

## 📄 License

MIT License - See LICENSE file for details.
