"""
Layers module for PyThon-Unshackle

Contains all the deobfuscation "peelers" - each layer is responsible for
removing one type of obfuscation technique.

The layered "onion" approach: each peeler removes one layer of obfuscation
and passes the result to the next stage until clean source code is obtained.
"""

from .base_layer import BaseLayer
from .encoding_peeler import EncodingPeeler
from .compression_peeler import CompressionPeeler
from .serialization_peeler import SerializationPeeler
from .execution_peeler import ExecutionPeeler

__all__ = [
    "BaseLayer",
    "EncodingPeeler", 
    "CompressionPeeler",
    "SerializationPeeler",
    "ExecutionPeeler"
]
