#!/usr/bin/env python3
"""
Final deobfuscator - extracts the actual code by running it safely
"""

import sys
import os
import subprocess
import tempfile


def create_safe_runner():
    """Create a safe runner script that captures the actual code execution."""
    runner_code = '''
import sys
import marshal
import types
import io
from contextlib import redirect_stdout, redirect_stderr

# Capture all output
output_buffer = io.StringIO()
error_buffer = io.StringIO()

# Override exec to capture what's being executed
original_exec = exec
executed_code = []

def safe_exec(code, globals_dict=None, locals_dict=None):
    """Capture executed code instead of running it."""
    if isinstance(code, str):
        executed_code.append(("STRING", code))
        print(f"EXECUTED STRING CODE:")
        print("-" * 40)
        print(code)
        print("-" * 40)
    elif isinstance(code, types.CodeType):
        executed_code.append(("CODE_OBJECT", code))
        print(f"EXECUTED CODE OBJECT: {code.co_name}")
        
        # Try to decompile the code object
        try:
            import uncompyle6
            from io import StringIO
            output = StringIO()
            uncompyle6.decompile(code, output)
            decompiled = output.getvalue()
            output.close()
            
            print("DECOMPILED CODE:")
            print("-" * 40)
            print(decompiled)
            print("-" * 40)
        except Exception as e:
            print(f"Decompilation failed: {e}")
            
            # Show disassembly instead
            import dis
            print("DISASSEMBLY:")
            print("-" * 40)
            dis.dis(code)
            print("-" * 40)
    else:
        executed_code.append(("OTHER", type(code).__name__))
        print(f"EXECUTED OTHER TYPE: {type(code).__name__}")

# Replace exec with our safe version
exec = safe_exec

try:
    # Now run the original file
    with open("test.py", "r") as f:
        original_code = f.read()
    
    print("RUNNING ORIGINAL FILE WITH SAFE EXEC:")
    print("=" * 50)
    
    # Execute the original file
    exec(original_code)
    
    print("\\nCAPTURED EXECUTIONS:")
    print("=" * 50)
    for i, (exec_type, content) in enumerate(executed_code):
        print(f"Execution {i+1}: {exec_type}")
        if exec_type == "STRING":
            print(f"Content: {repr(content[:100])}...")
        elif exec_type == "CODE_OBJECT":
            print(f"Code object: {content.co_name}")
        print()

except Exception as e:
    print(f"Error during execution: {e}")
    import traceback
    traceback.print_exc()
'''
    
    return runner_code


def run_safe_deobfuscation():
    """Run the safe deobfuscation process."""
    print("=== FINAL DEOBFUSCATION ATTEMPT ===")
    print("Running the file with exec interception...")
    
    # Create the safe runner
    runner_code = create_safe_runner()
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(runner_code)
        runner_file = f.name
    
    try:
        # Run the safe runner
        result = subprocess.run([
            sys.executable, runner_file
        ], capture_output=True, text=True, timeout=30, cwd='.')
        
        print("STDOUT:")
        print("-" * 40)
        print(result.stdout)
        print("-" * 40)
        
        if result.stderr:
            print("STDERR:")
            print("-" * 40)
            print(result.stderr)
            print("-" * 40)
        
        print(f"Return code: {result.returncode}")
        
        # Save the output
        with open("final_deobfuscation_output.txt", "w") as f:
            f.write("=== FINAL DEOBFUSCATION OUTPUT ===\\n")
            f.write(result.stdout)
            if result.stderr:
                f.write("\\n=== ERRORS ===\\n")
                f.write(result.stderr)
        
        print("Output saved to final_deobfuscation_output.txt")
        
    except subprocess.TimeoutExpired:
        print("Process timed out - the file might be doing something complex")
    except Exception as e:
        print(f"Error running safe deobfuscation: {e}")
    finally:
        # Clean up
        try:
            os.unlink(runner_file)
        except:
            pass


def try_direct_execution():
    """Try direct execution with output capture."""
    print("\\n=== DIRECT EXECUTION TEST ===")
    
    # Create a simple test to see what the file does
    test_code = '''
import sys
import io
from contextlib import redirect_stdout

# Capture output
output = io.StringIO()

try:
    with redirect_stdout(output):
        # Import and run the test file
        import test
    
    captured = output.getvalue()
    print("CAPTURED OUTPUT:")
    print("-" * 40)
    print(captured)
    print("-" * 40)
    
except Exception as e:
    print(f"Direct execution failed: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        test_file = f.name
    
    try:
        result = subprocess.run([
            sys.executable, test_file
        ], capture_output=True, text=True, timeout=10, cwd='.')
        
        print("Direct execution result:")
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
            
    except Exception as e:
        print(f"Direct execution failed: {e}")
    finally:
        try:
            os.unlink(test_file)
        except:
            pass


def main():
    """Main function."""
    print("PyThon-Unshackle - Final Deobfuscation Attempt")
    print("=" * 60)
    
    # Check if test.py exists
    if not os.path.exists("test.py"):
        print("test.py not found!")
        return 1
    
    # Try safe deobfuscation
    run_safe_deobfuscation()
    
    # Try direct execution
    try_direct_execution()
    
    print("\\n=== SUMMARY ===")
    print("1. Check final_deobfuscation_output.txt for detailed results")
    print("2. The file appears to be a complex Cython-compiled module")
    print("3. It may require specific Python version (3.9) to run properly")
    print("4. The actual functionality might be hidden in the C extension")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
