"""
Pattern Detection Engine for PyThon-Unshackle

This module implements the intelligent pattern detection system that identifies
various obfuscation techniques using heuristics, regex patterns, and statistical
analysis. It's the "brain" that determines which deobfuscation layers to apply.
"""

import re
import base64
import binascii
import zlib
import marshal
import pickle
from typing import Dict, List, Tuple, Union, Optional, Set
from dataclasses import dataclass
from enum import Enum

from .ast_navigator import ASTNavigator


class ObfuscationType(Enum):
    """Types of obfuscation that can be detected"""
    EXEC_EVAL = "exec_eval"
    BASE64 = "base64"
    HEX = "hex"
    URL_ENCODING = "url_encoding"
    ZLIB_COMPRESSION = "zlib_compression"
    GZIP_COMPRESSION = "gzip_compression"
    BZ2_COMPRESSION = "bz2_compression"
    MARSHAL_SERIALIZATION = "marshal_serialization"
    PICKLE_SERIALIZATION = "pickle_serialization"
    ROT13 = "rot13"
    UNKNOWN = "unknown"


@dataclass
class DetectionResult:
    """Result of obfuscation detection"""
    obfuscation_type: ObfuscationType
    confidence: float  # 0.0 to 1.0
    evidence: List[str]
    data_location: Optional[str] = None
    additional_info: Dict[str, any] = None


class PatternDetector:
    """
    Intelligent pattern detection engine for identifying obfuscation techniques.
    
    Uses multiple detection methods:
    - Regex pattern matching
    - Statistical analysis
    - AST analysis for code structures
    - Binary signature detection
    - Entropy analysis
    """
    
    def __init__(self):
        self.ast_navigator = ASTNavigator()
        self._init_patterns()
    
    def _init_patterns(self) -> None:
        """Initialize regex patterns for different obfuscation types."""
        self.patterns = {
            # exec/eval patterns
            ObfuscationType.EXEC_EVAL: [
                r'exec\s*\(',
                r'eval\s*\(',
                r'compile\s*\(',
                r'__import__\s*\(',
            ],
            
            # Base64 patterns
            ObfuscationType.BASE64: [
                r'base64\.b64decode\s*\(',
                r'\.decode\s*\(\s*[\'"]base64[\'"]',
                r'[A-Za-z0-9+/]{20,}={0,2}',  # Base64-like strings
            ],
            
            # Hex patterns
            ObfuscationType.HEX: [
                r'\.decode\s*\(\s*[\'"]hex[\'"]',
                r'binascii\.unhexlify\s*\(',
                r'bytes\.fromhex\s*\(',
                r'[0-9a-fA-F]{40,}',  # Long hex strings
            ],
            
            # URL encoding patterns
            ObfuscationType.URL_ENCODING: [
                r'urllib\.parse\.unquote',
                r'%[0-9a-fA-F]{2}',  # URL encoded characters
            ],
            
            # Compression patterns
            ObfuscationType.ZLIB_COMPRESSION: [
                r'zlib\.decompress\s*\(',
                r'zlib\.inflate\s*\(',
            ],
            
            ObfuscationType.GZIP_COMPRESSION: [
                r'gzip\.decompress\s*\(',
                r'gzip\.GzipFile\s*\(',
            ],
            
            ObfuscationType.BZ2_COMPRESSION: [
                r'bz2\.decompress\s*\(',
            ],
            
            # Serialization patterns
            ObfuscationType.MARSHAL_SERIALIZATION: [
                r'marshal\.loads\s*\(',
                r'marshal\.load\s*\(',
            ],
            
            ObfuscationType.PICKLE_SERIALIZATION: [
                r'pickle\.loads\s*\(',
                r'pickle\.load\s*\(',
                r'cPickle\.loads\s*\(',
            ],
        }
    
    def detect_obfuscation(self, data: Union[str, bytes]) -> List[DetectionResult]:
        """
        Detect all possible obfuscation types in the given data.
        
        Args:
            data: Data to analyze (string or bytes)
            
        Returns:
            List of DetectionResult objects, sorted by confidence (highest first)
        """
        results = []
        
        # Convert bytes to string for text analysis
        if isinstance(data, bytes):
            try:
                text_data = data.decode('utf-8', errors='ignore')
            except:
                text_data = str(data)
        else:
            text_data = data
        
        # Check for each obfuscation type
        for obf_type in ObfuscationType:
            if obf_type == ObfuscationType.UNKNOWN:
                continue
                
            result = self._detect_specific_type(obf_type, data, text_data)
            if result and result.confidence > 0.1:  # Only include results with reasonable confidence
                results.append(result)
        
        # Sort by confidence (highest first)
        results.sort(key=lambda x: x.confidence, reverse=True)
        
        return results
    
    def _detect_specific_type(self, obf_type: ObfuscationType, data: Union[str, bytes], text_data: str) -> Optional[DetectionResult]:
        """
        Detect a specific obfuscation type.
        
        Args:
            obf_type: Type of obfuscation to detect
            data: Original data
            text_data: String representation of data
            
        Returns:
            DetectionResult if detected, None otherwise
        """
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        # Pattern-based detection
        if obf_type in self.patterns:
            for pattern in self.patterns[obf_type]:
                matches = re.findall(pattern, text_data, re.IGNORECASE | re.MULTILINE)
                if matches:
                    evidence.append(f"Pattern match: {pattern}")
                    confidence += 0.3
        
        # Specific detection methods
        if obf_type == ObfuscationType.EXEC_EVAL:
            confidence, evidence, additional_info = self._detect_exec_eval(text_data)
        elif obf_type == ObfuscationType.BASE64:
            confidence, evidence, additional_info = self._detect_base64(data, text_data)
        elif obf_type == ObfuscationType.HEX:
            confidence, evidence, additional_info = self._detect_hex(data, text_data)
        elif obf_type == ObfuscationType.ZLIB_COMPRESSION:
            confidence, evidence, additional_info = self._detect_zlib(data)
        elif obf_type == ObfuscationType.MARSHAL_SERIALIZATION:
            confidence, evidence, additional_info = self._detect_marshal(data)
        elif obf_type == ObfuscationType.PICKLE_SERIALIZATION:
            confidence, evidence, additional_info = self._detect_pickle(data)
        
        if confidence > 0.1:
            return DetectionResult(
                obfuscation_type=obf_type,
                confidence=min(confidence, 1.0),  # Cap at 1.0
                evidence=evidence,
                additional_info=additional_info
            )
        
        return None
    
    def _detect_exec_eval(self, text_data: str) -> Tuple[float, List[str], Dict]:
        """Detect exec/eval obfuscation using AST analysis."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        # Use AST navigator to find exec/eval calls
        exec_calls = self.ast_navigator.find_exec_calls(text_data)
        if exec_calls:
            confidence += 0.8
            evidence.append(f"Found {len(exec_calls)} exec/eval calls")
            additional_info['exec_calls'] = len(exec_calls)
            
            # Analyze the arguments
            for call in exec_calls:
                if call.extracted_code:
                    evidence.append(f"Extractable {call.function_name} argument")
                    confidence += 0.1
        
        return confidence, evidence, additional_info
    
    def _detect_base64(self, data: Union[str, bytes], text_data: str) -> Tuple[float, List[str], Dict]:
        """Detect Base64 encoding."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        # Look for base64 function calls
        if 'base64' in text_data.lower():
            confidence += 0.4
            evidence.append("Base64 module usage detected")
        
        # Look for base64-like strings
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        matches = re.findall(base64_pattern, text_data)
        
        for match in matches:
            try:
                # Try to decode as base64
                decoded = base64.b64decode(match, validate=True)
                if len(decoded) > 10:  # Reasonable size
                    confidence += 0.6
                    evidence.append(f"Valid base64 string found (length: {len(match)})")
                    additional_info['base64_strings'] = additional_info.get('base64_strings', 0) + 1
                    break  # One good match is enough
            except:
                continue
        
        return confidence, evidence, additional_info
    
    def _detect_hex(self, data: Union[str, bytes], text_data: str) -> Tuple[float, List[str], Dict]:
        """Detect hexadecimal encoding."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        # Look for hex function calls
        if any(func in text_data.lower() for func in ['unhexlify', 'fromhex', 'hex']):
            confidence += 0.4
            evidence.append("Hex decoding functions detected")
        
        # Look for long hex strings
        hex_pattern = r'[0-9a-fA-F]{40,}'
        matches = re.findall(hex_pattern, text_data)
        
        for match in matches:
            try:
                # Try to decode as hex
                decoded = binascii.unhexlify(match)
                if len(decoded) > 10:
                    confidence += 0.6
                    evidence.append(f"Valid hex string found (length: {len(match)})")
                    additional_info['hex_strings'] = additional_info.get('hex_strings', 0) + 1
                    break
            except:
                continue
        
        return confidence, evidence, additional_info
    
    def _detect_zlib(self, data: Union[str, bytes]) -> Tuple[float, List[str], Dict]:
        """Detect zlib compression."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        if isinstance(data, bytes):
            # Check for zlib magic bytes
            if data.startswith(b'\x78'):  # zlib header
                try:
                    decompressed = zlib.decompress(data)
                    confidence = 0.9
                    evidence.append("Valid zlib compressed data")
                    additional_info['decompressed_size'] = len(decompressed)
                except:
                    confidence = 0.3
                    evidence.append("Zlib header detected but decompression failed")
        
        return confidence, evidence, additional_info
    
    def _detect_marshal(self, data: Union[str, bytes]) -> Tuple[float, List[str], Dict]:
        """Detect marshal serialization."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        if isinstance(data, bytes):
            try:
                # Try to load as marshal
                obj = marshal.loads(data)
                confidence = 0.9
                evidence.append("Valid marshal data")
                additional_info['object_type'] = type(obj).__name__
                
                # Check if it's a code object
                if hasattr(obj, 'co_code'):
                    confidence = 1.0
                    evidence.append("Marshal contains code object")
                    additional_info['is_code_object'] = True
            except:
                pass
        
        return confidence, evidence, additional_info
    
    def _detect_pickle(self, data: Union[str, bytes]) -> Tuple[float, List[str], Dict]:
        """Detect pickle serialization."""
        evidence = []
        confidence = 0.0
        additional_info = {}
        
        if isinstance(data, bytes):
            # Check for pickle magic bytes
            if data.startswith((b'\x80', b'(', b']', b'}')):  # Common pickle headers
                try:
                    obj = pickle.loads(data)
                    confidence = 0.9
                    evidence.append("Valid pickle data")
                    additional_info['object_type'] = type(obj).__name__
                except:
                    confidence = 0.3
                    evidence.append("Pickle header detected but loading failed")
        
        return confidence, evidence, additional_info
    
    def get_recommended_layers(self, detection_results: List[DetectionResult]) -> List[str]:
        """
        Get recommended layer names based on detection results.
        
        Args:
            detection_results: List of detection results
            
        Returns:
            List of recommended layer class names in processing order
        """
        layer_mapping = {
            ObfuscationType.EXEC_EVAL: "ExecutionPeeler",
            ObfuscationType.BASE64: "EncodingPeeler",
            ObfuscationType.HEX: "EncodingPeeler",
            ObfuscationType.URL_ENCODING: "EncodingPeeler",
            ObfuscationType.ZLIB_COMPRESSION: "CompressionPeeler",
            ObfuscationType.GZIP_COMPRESSION: "CompressionPeeler",
            ObfuscationType.BZ2_COMPRESSION: "CompressionPeeler",
            ObfuscationType.MARSHAL_SERIALIZATION: "SerializationPeeler",
            ObfuscationType.PICKLE_SERIALIZATION: "SerializationPeeler",
        }
        
        recommended = []
        for result in detection_results:
            if result.confidence > 0.5:  # Only high-confidence detections
                layer_name = layer_mapping.get(result.obfuscation_type)
                if layer_name and layer_name not in recommended:
                    recommended.append(layer_name)
        
        return recommended
