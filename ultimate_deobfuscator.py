#!/usr/bin/env python3
"""
Ultimate deobfuscator - handles binary files and complex encodings
"""

import sys
import os
import marshal
import subprocess
import tempfile


def extract_and_run_marshal():
    """Extract marshal data and try to execute it safely."""
    print("=== ULTIMATE DEOBFUSCATION ===")
    
    try:
        # Read the file as binary
        with open("test.py", "rb") as f:
            content = f.read()
        
        print(f"File size: {len(content)} bytes")
        
        # Find the marshal.loads pattern
        import re
        pattern = rb'marshal\.loads\s*\(\s*b[\'"]([^\'"]*)[\'"]'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print("No marshal.loads pattern found")
            return None
        
        binary_str = match.group(1)
        print(f"Found marshal data, raw length: {len(binary_str)}")
        
        # Convert escape sequences to actual bytes
        try:
            actual_bytes = binary_str.decode('unicode_escape').encode('latin-1')
            print(f"Converted to bytes, length: {len(actual_bytes)}")
        except Exception as e:
            print(f"Error converting: {e}")
            return None
        
        # Try to load the marshal data
        try:
            code_obj = marshal.loads(actual_bytes)
            print(f"Successfully loaded marshal object: {type(code_obj)}")
            
            if hasattr(code_obj, 'co_code'):
                print(f"Code object found: {code_obj.co_name}")
                print(f"Filename: {code_obj.co_filename}")
                print(f"Constants: {len(code_obj.co_consts) if code_obj.co_consts else 0}")
                
                # Show constants
                if code_obj.co_consts:
                    print("\\nConstants:")
                    for i, const in enumerate(code_obj.co_consts[:10]):
                        if isinstance(const, str) and len(const) > 50:
                            print(f"  {i}: <long string, {len(const)} chars>")
                            # Check if it looks like code
                            if any(kw in const for kw in ['def ', 'class ', 'import ', 'print']):
                                print(f"      Looks like code: {repr(const[:100])}...")
                        else:
                            print(f"  {i}: {repr(const)}")
                
                # Try to execute the code object safely
                print("\\n=== SAFE EXECUTION ===")
                return execute_code_object_safely(code_obj)
            else:
                print(f"Not a code object: {code_obj}")
                return str(code_obj)
                
        except Exception as e:
            print(f"Error loading marshal: {e}")
            return None
            
    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def execute_code_object_safely(code_obj):
    """Execute code object in a controlled environment."""
    try:
        # Create a controlled namespace
        safe_globals = {
            '__builtins__': {
                'print': print,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'range': range,
                'enumerate': enumerate,
                'zip': zip,
            }
        }
        
        # Capture output
        import io
        from contextlib import redirect_stdout, redirect_stderr
        
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        print("Executing code object...")
        
        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
            exec(code_obj, safe_globals)
        
        stdout_result = stdout_capture.getvalue()
        stderr_result = stderr_capture.getvalue()
        
        print("Execution completed!")
        
        if stdout_result:
            print("STDOUT:")
            print("-" * 40)
            print(stdout_result)
            print("-" * 40)
        
        if stderr_result:
            print("STDERR:")
            print("-" * 40)
            print(stderr_result)
            print("-" * 40)
        
        return stdout_result or "Execution completed with no output"
        
    except Exception as e:
        print(f"Safe execution failed: {e}")
        
        # Try to get more info about the code object
        print("\\nCode object analysis:")
        print(f"  Name: {code_obj.co_name}")
        print(f"  Filename: {code_obj.co_filename}")
        print(f"  First line: {code_obj.co_firstlineno}")
        print(f"  Arg count: {code_obj.co_argcount}")
        print(f"  Local count: {code_obj.co_nlocals}")
        print(f"  Stack size: {code_obj.co_stacksize}")
        print(f"  Flags: {code_obj.co_flags}")
        
        # Show disassembly
        print("\\nDisassembly:")
        import dis
        dis.dis(code_obj)
        
        return f"Execution failed: {e}"


def try_uncompyle6_decompilation():
    """Try decompilation with uncompyle6."""
    print("\\n=== TRYING UNCOMPYLE6 DECOMPILATION ===")
    
    try:
        # Read and extract marshal data
        with open("test.py", "rb") as f:
            content = f.read()
        
        import re
        pattern = rb'marshal\.loads\s*\(\s*b[\'"]([^\'"]*)[\'"]'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            binary_str = match.group(1)
            actual_bytes = binary_str.decode('unicode_escape').encode('latin-1')
            code_obj = marshal.loads(actual_bytes)
            
            if hasattr(code_obj, 'co_code'):
                try:
                    import uncompyle6
                    from io import StringIO
                    
                    output = StringIO()
                    uncompyle6.decompile(code_obj, output)
                    result = output.getvalue()
                    output.close()
                    
                    print("✓ UNCOMPYLE6 DECOMPILATION SUCCESSFUL!")
                    print("-" * 50)
                    print(result)
                    print("-" * 50)
                    
                    # Save to file
                    with open("test_uncompyle6_decompiled.py", "w", encoding="utf-8") as f:
                        f.write(result)
                    print("Saved to test_uncompyle6_decompiled.py")
                    
                    return result
                    
                except ImportError:
                    print("uncompyle6 not installed. Install with: pip install uncompyle6")
                except Exception as e:
                    print(f"uncompyle6 decompilation failed: {e}")
        
    except Exception as e:
        print(f"Error in uncompyle6 attempt: {e}")
    
    return None


def main():
    """Main function."""
    print("PyThon-Unshackle - Ultimate Deobfuscation")
    print("=" * 50)
    
    if not os.path.exists("test.py"):
        print("test.py not found!")
        return 1
    
    # Try marshal extraction and execution
    result = extract_and_run_marshal()
    
    # Try uncompyle6 decompilation
    decompiled = try_uncompyle6_decompilation()
    
    # Summary
    print("\\n=== FINAL SUMMARY ===")
    if result:
        print("✓ Successfully extracted and executed marshal data")
        print(f"Result: {result[:200]}...")
    else:
        print("✗ Failed to extract/execute marshal data")
    
    if decompiled:
        print("✓ Successfully decompiled with uncompyle6")
    else:
        print("✗ Decompilation failed - try installing uncompyle6")
    
    print("\\nThe file appears to be a complex obfuscated Python module.")
    print("It may contain:")
    print("- Cython compiled code")
    print("- Complex marshal serialization")
    print("- Version-specific bytecode")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
