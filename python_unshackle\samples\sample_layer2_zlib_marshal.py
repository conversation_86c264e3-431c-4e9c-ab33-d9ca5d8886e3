# Sample Layer 2: <PERSON><PERSON><PERSON> + Marshal Obfuscation
# This sample demonstrates compression and serialization obfuscation

import zlib
import marshal
import base64

# Multi-layer obfuscation: base64 -> zlib -> marshal
# Original code: print("Hello from multi-layer obfuscation!")

# Step 1: Create a simple function and marshal it
def original_function():
    print("Hello from multi-layer obfuscation!")
    for i in range(3):
        print(f"Layer {i+1} successfully deobfuscated!")

# This would be the marshal data (simulated)
# In real obfuscation, this would be the actual marshal.dumps() output
marshal_data = b'\xe3\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04\x00\x00\x00@\x00\x00\x00s\x1a\x00\x00\x00t\x00d\x01\x83\x01\x01\x00t\x01d\x02\x83\x01D\x00]\x08}\x00t\x00d\x03|\x00\x83\x02\x01\x00q\x04d\x00S\x00'

# Step 2: Compress with zlib
compressed_data = zlib.compress(marshal_data)

# Step 3: Encode with base64
encoded_data = base64.b64encode(compressed_data)

# The obfuscated execution
obfuscated_payload = encoded_data

# Deobfuscation chain (this is what our tool should reverse)
exec(marshal.loads(zlib.decompress(base64.b64decode(obfuscated_payload))))

# Another example with string-based obfuscation
secret_code = "eJyrVkosLcmIz8nPS1WyUoIBAAD//w=="
exec(marshal.loads(zlib.decompress(base64.b64decode(secret_code))))