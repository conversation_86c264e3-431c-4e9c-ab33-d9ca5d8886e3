# Sample Layer 3: Complex Nested Exec Obfuscation
# This sample demonstrates the most complex obfuscation with nested exec calls

import base64

# Layer 1: Simple exec with base64
exec(base64.b64decode(b'cHJpbnQoIlN0YXJ0aW5nIGNvbXBsZXggZGVvYmZ1c2NhdGlvbi4uLiIp'))

# Layer 2: Nested exec calls
outer_code = '''
import base64
inner_code = "cHJpbnQoJ0lubmVyIGxheWVyIGV4ZWN1dGVkIScpCmZvciBpIGluIHJhbmdlKDMpOgogICAgcHJpbnQoZidTdGVwIHtpKzF9IGNvbXBsZXRlJyk="
exec(base64.b64decode(inner_code))
'''

exec(outer_code)

# Layer 3: Variable-based exec
hidden_payload = "cHJpbnQoJ1ZhcmlhYmxlLWJhc2VkIGV4ZWN1dGlvbiBzdWNjZXNzZnVsIScpCmRlZiBzZWNyZXRfZnVuY3Rpb24oKToKICAgIHJldHVybiAiVGhpcyB3YXMgaGlkZGVuIGluIGEgdmFyaWFibGUhIgpwcmludChzZWNyZXRfZnVuY3Rpb24oKSk="
exec(base64.b64decode(hidden_payload))

# Layer 4: Function call within exec
def get_secret_code():
    return "cHJpbnQoJ0Z1bmN0aW9uLWJhc2VkIGhpZGRlbiBjb2RlIGV4ZWN1dGVkIScpCmNsYXNzIEhpZGRlbkNsYXNzOgogICAgZGVmIF9faW5pdF9fKHNlbGYpOgogICAgICAgIHNlbGYubWVzc2FnZSA9ICJDbGFzcyBzdWNjZXNzZnVsbHkgZGVvYmZ1c2NhdGVkISIKICAgIGRlZiBzaG93X21lc3NhZ2Uoc2VsZik6CiAgICAgICAgcHJpbnQoc2VsZi5tZXNzYWdlKQpvYmogPSBIaWRkZW5DbGFzcygpCm9iai5zaG93X21lc3NhZ2UoKQ=="

exec(base64.b64decode(get_secret_code()))

# Layer 5: String concatenation in exec
part1 = "cHJpbnQoJ011bHRpLXBhcnQgc3RyaW5nIGRlb2JmdXNjYXRpb24nKQp0cnk6CiAgICBpbXBvcnQgcmVxdWVzdHMKICAgIHByaW50KCdSZXF1ZXN0cyBtb2R1bGUgYXZhaWxhYmxlJykKZXhjZXB0IEltcG9ydEVycm9yOgogICAgcHJpbnQoJ1JlcXVlc3RzIG1vZHVsZSBub3QgYXZhaWxhYmxlJyk="
part2 = ""
combined = part1 + part2
exec(base64.b64decode(combined))

# Layer 6: Conditional exec
condition = True
if condition:
    conditional_code = "cHJpbnQoJ0NvbmRpdGlvbmFsIGV4ZWN1dGlvbiBzdWNjZXNzZnVsIScpCmZvciBqIGluIFsnYScsICdiJywgJ2MnXToKICAgIHByaW50KGYnUHJvY2Vzc2luZyBpdGVtOiB7an0nKQ=="
    exec(base64.b64decode(conditional_code))

print("Complex nested obfuscation sample completed!")