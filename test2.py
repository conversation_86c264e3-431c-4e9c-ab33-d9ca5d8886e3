import sys

# Check version
PYTHON_VERSION = ".".join(sys.version.split(" ")[0].split(".")[:-1])
if PYTHON_VERSION != "3.9":
    print('''[!] No support for [VALUE]'''.replace("[VERSION]", sys.version.split(" ")[0]))
    exit(0)

import marshal
exec(marshal.loads(b'c\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\x00\x00\x00@\x00\x00\x00sL\x01\x00\x00d\x00Z\x00e\x00r\x10d\x01d\x02\x84!Z\x01d\x02d\x03l\x02Z\x02d\x02d\x03l\x03Z\x03d\x04Z\x04d\x05Z\x05e\x03j\x06Z\x07d\x06e\x07\x17\x00Z\x08d\x07e\x03j\t\x17\x00Z\nd\x08e\x05\x17\x00Z\x0be\x02j\x0c\xa0\re\x05\xa1\x01rve\x02\xa0\x0ee\x08d\t\x17\x00e\n\x17\x00d\t\x17\x00e\x0b\x17\x00\xa1\x01\x01\x00e\x0fd\x02\x83\x01\x01\x00d\nZ\x10d\x0bZ\x11d\x0c\xa0\x12e\x03j\x13\xa0\x14d\r\xa1\x01d\x02\x19\x00\xa0\x14d\x0c\xa1\x01d\x03d\x0e\x85\x02\x19\x00\xa1\x01Z\x15d\x0fe\x07\x17\x00d\x10\x17\x00e\x15\x17\x00d\x11\x17\x00e\x05\x17\x00d\r\x17\x00e\x11\x17\x00d\x12\x17\x00e\x07\x17\x00d\x13\x17\x00e\x15\x17\x00Z\x16e\x17e\x11d\x14\x83\x02\x8f\x1aZ\x18e\x18\xa0\x19e\x10\xa1\x01\x01\x00W\x00d\x03\x04\x00\x04\x00\x83\x03\x01\x00n\x101\x00s\xfc0\x00\x01\x00\x01\x00\x01\x00Y\x00\x01\x00e\x02j\x1ae\x02j\x0c\xa0\x1be\x05\xa1\x01d\x15d\x16\x8d\x02\x01\x00e\x02\xa0\x0ee\x08d\t\x17\x00e\n\x17\x00d\t\x17\x00e\x16\x17\x00d\t\x17\x00e\x0b\x17\x00\xa1\x01\x01\x00e\x02\xa0\x1ce\x11\xa1\x01\x01\x00d\x03S\x00)\x17F\xe9\x01\x00\x00\x00\xe9\x00\x00\x00\x00Nu\t\x00\x00\x00\xd8\xa8\xd8\xae \xf0\x9f\x91\x80z\x1d.PY_PRIVATE/20241208151924076z\x12export PYTHONHOME=z\x19export PYTHON_EXECUTABLE=z\x02./z\x04 && ar \x05\x00#ifndef PY_SSIZE_T_CLEAN\n#define PY_SSIZE_T_CLEAN\n#endif /* PY_SSIZE_T_CLEAN */\n#include "Python.h"\n#ifndef Py_PYTHON_H\n    #error Python headers needed to compile C extensions, please install development version of Python.\n#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)\n    #error Cython requires Python 2.6+ or Python 3.3+.\n#else\n#define CYTHON_ABI "0_29_33"\n#define CYTHON_HEX_VERSION 0x001D21F0\n#define CYTHON_FUTURE_DIVISION 1\n#include <stddef.h>\n#ifndef offsetof\n  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )\n#endif\n#if !defined(WIN32) && !defined(MS_WINDOWS)\n  #ifndef __stdcall\n    #define __stdcall\n  #endif\n  #ifndef __cdecl\n    #define __cdecl\n  #endif\n  #ifndef __fastcall\n    #define __fastcall\n  #endif\n#endif\n#ifndef DL_IMPORT\n  #define DL_IMPORT(t) t\n#endif\n#ifndef DL_EXPORT\n  #define DL_EXPORT(t) t\n#endif\n#define __PYX_COMMA ,\n#ifndef HAVE_LONG_LONG\n  #if PY_VERSION_HEX >= 0x02070000\n    #define HAVE_LONG_LONG\n  #endif\n#endif\n#ifndef PY_LONG_LONG\n  #define PY_LONG_LONG LONG_LONG\n#endif\n#ifndef Py_HUGE_VAL\n  #define Py_HUGE_VAL HUGE_VAL\n#endif\n#ifdef PYPY_VERSION\n  #define CYTHON_COMPILING_IN_PYPY 1\n  #define CYTHON_COMPILING_IN_PYSTON 0\n  #define CYTHON_COMPILING_IN_CPYTHON 0\n  #define CYTHON_COMPILING_IN_NOGIL 0\n  #undef CYTHON_USE_TYPE_SLOTS\n  #define CYTHON_USE_TYPE_SLOTS 0\n  #undef CYTHON_USE_PYTYPE_LOOKUP\n  #define CYTHON_USE_PYTYPE_LOOKUP 0\n  #if PY_VERSION_HEX < 0x03050000\n    #undef CYTHON_USE_ASYNC_SLOTS\n    #define CYTHON_USE_ASYNC_SLOTS 0\n  #elif !defined(CYTHON_USE_ASYNC_SLOTS)\n    #define CYTHON_USE_ASYNC_SLOTS 1\n  #endif\n  #undef CYTHON_USE_PYLIST_INTERNALS\n  #define CYTHON_USE_PYLIST_INTERNALS 0\n  #undef CYTHON_USE_UNICODE_INTERNALS\n  #define CYTHON_USE_UNICODE_INTERNALS 0\n  #undef CYTHON_USE_UNICODE_WRITER\n  #define CYTHON_USE_UNICODE_WRITER 0\n  #undef CYTHON_USE_PYLONG_INTERNALS\n  #define CYTHON_USE_PYLONG_INTERNALS 0\n  #undef CYTHON_AVOID_BORROWED_REFS\n  #define CYTHON_AVOID_BORROWED_REFS 1\n  #undef CYTHON_ASSUME_SAFE_MACROS\n  #define CYTHON_ASSUME_SAFE_MACROS 0\n  #undef CYTHON_UNPACK_METHODS\n  #define CYTHON_UNPACK_METHODS 0\n  #undef CYTHON_FAST_THREAD_STATE\n  #define CYTHON_FAST_THREAD_STATE 0\n  #undef CYTHON_FAST_PYCALL\n  #define CYTHON_FAST_PYCALL 0\n  #undef CYTHON_PEP489_MULTI_PHASE_INIT\n  #define CYTHON_PEP489_MULTI_PHASE_INIT 0\n  #undef CYTHON_USE_TP_FINALIZE\n  #define CYTHON_USE_TP_FINALIZE 0\n  #undef CYTHON_USE_DICT_VERSIONS\n  #define CYTHON_USE_DICT_VERSIONS 0\n  #undef CYTHON_USE_EXC_INFO_STACK\n  #define CYTHON_USE_EXC_INFO_STACK 0\n  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC\n    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0\n  #endif\n#elif defined(PYSTON_VERSION)\n  #define CYTHON_COMPILING_IN_PYPY 0\n  #define CYTHON_COMPILING_IN_PYSTON 1\n  #define CYTHON_COMPILING_IN_CPYTHON 0\n  #define CYTHON_COMPILING_IN_NOGIL 0\n  #ifndef CYTHON_USE_TYPE_SLOTS\n    #define CYTHON_USE_TYPE_SLOTS 1\n  #endif\n  #undef CYTHON_USE_PYTYPE_LOOKUP\n  #define CYTHON_USE_PYTYPE_LOOKUP 0\n  #undef CYTHON_USE_ASYNC_SLOTS\n  #define CYTHON_USE_ASYNC_SLOTS 0\n  #undef CYTHON_USE_PYLIST_INTERNALS\n  #define CYTHON_USE_PYLIST_INTERNALS 0\n  #ifndef CYTHON_USE_UNICODE_INTERNALS\n    #define CYTHON_USE_UNICODE_INTERNALS 1\n  #endif\n  #undef CYTHON_USE_UNICODE_WRITER\n  #define CYTHON_USE_UNICODE_WRITER 0\n  #undef CYTHON_USE_PYLONG_INTERNALS\n  #define CYTHON_USE_PYLONG_INTERNALS 0\n  #ifndef CYTHON_AVOID_BORROWED_REFS\n    #define CYTHON_AVOID_BORROWED_REFS 0\n  #endif\n  #ifndef CYTHON_ASSUME_SAFE_MACROS\n    #define CYTHON_ASSUME_SAFE_MACROS 1\n  #endif\n  #ifndef CYTHON_UNPACK_METHODS\n    #define CYTHON_UNPACK_METHODS 1\n  #endif\n  #undef CYTHON_FAST_THREAD_STATE\n  #define CYTHON_FAST_THREAD_STATE 0\n  #undef CYTHON_FAST_PYCALL\n  #define CYTHON_FAST_PYCALL 0\n  #undef CYTHON_PEP489_MULTI_PHASE_INIT\n  #define CYTHON_PEP489_MULTI_PHASE_INIT 0\n  #undef CYTHON_USE_TP_FINALIZE\n  #define CYTHON_USE_TP_FINALIZE 0\n  #undef CYTHON_USE_DICT_VERSIONS\n  #define CYTHON_USE_DICT_VERSIONS 0\n  #undef CYTHON_USE_EXC_INFO_STACK\n  #define CYTHON_USE_EXC_INFO_STACK 0\n  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC\n    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0\n  #endif\n#elif defined(PY_NOGIL)\n  #define CYTHON_COMPILING_IN_PYPY 0\n  #define CYTHON_COMPILING_IN_PYSTON 0\n  #define CYTHON_COMPILING_IN_CPYTHON 0\n  #define CYTHON_COMPILING_IN_NOGIL 1\n  #ifndef CYTHON_USE_TYPE_SLOTS\n    #define CYTHON_USE_TYPE_SLOTS 1\n  #endif\n  #undef CYTHON_USE_PYTYPE_LOOKUP\n  #define CYTHON_USE_PYTYPE_LOOKUP 0\n  #ifndef CYTHON_USE_ASYNC_SLOTS\n    #define CYTHON_USE_ASYNC_SLOTS 1\n  #endif\n  #undef CYTHON_USE_PYLIST_INTERNALS\n  #define CYTHON_USE_PYLIST_INTERNALS 0\n  #ifndef CYTHON_USE_UNICODE_INTERNALS\n    #define CYTHON_USE_UNICODE_INTERNALS 1\n  #endif\n  #undef CYTHON_USE_UNICODE_WRITER\n  #define CYTHON_USE_UNICODE_WRITER 0\n  #undef CYTHON_USE_PYLONG_INTERNALS\n  #define CYTHON_USE_PYLONG_INTERNALS 0\n  #ifndef CYTHON_AVOID_BORROWED_REFS\n    #define CYTHON_AVOID_BORROWED_REFS 0\n  #endif\n  #ifndef CYTHON_ASSUME_SAFE_MACROS\n    #define CYTHON_ASSUME_SAFE_MACROS 1\n  #endif\n  #ifndef CYTHON_UNPACK_METHODS\n    #define CYTHON_UNPACK_METHODS 1\n  #endif\n  #undef CYTHON_FAST_THREAD_STATE\n  #define CYTHON_FAST_THREAD_STATE 0\n  #undef CYTHON_FAST_PYCALL\n  #define CYTHON_FAST_PYCALL 0\n  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT\n    #define CYTHON_PEP489_MULTI_PHASE_INIT 1\n  #endif\n  #ifndef CYTHON_USE_TP_FINALIZE\n    #define CYTHON_USE_TP_FINALIZE 1\n  #endif\n  #undef CYTHON_USE_DICT_VERSIONS\n  #define CYTHON_USE_DICT_VERSIONS 0\n  #undef CYTHON_USE_EXC_INFO_STACK\n  #define CYTHON_USE_EXC_INFO_STACK 0\n#else\n  #define CYTHON_COMPILING_IN_PYPY 0\n  #define CYTHON_COMPILING_IN_PYSTON 0\n  #define CYTHON_COMPILING_IN_CPYTHON 1\n  #define CYTHON_COMPILING_IN_NOGIL 0\n  #ifndef CYTHON_USE_TYPE_SLOTS\n    #define CYTHON_USE_TYPE_SLOTS 1\n  #endif\n  #if PY_VERSION_HEX < 0x02070000\n    #undef CYTHON_USE_PYTYPE_LOOKUP\n    #define CYTHON_USE_PYTYPE_LOOKUP 0\n  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)\n    #define CYTHON_USE_PYTYPE_LOOKUP 1\n  #endif\n  #if PY_MAJOR_VERSION < 3\n    #undef CYTHON_USE_ASYNC_SLOTS\n    #define CYTHON_USE_ASYNC_SLOTS 0\n  #elif !defined(CYTHON_USE_ASYNC_SLOTS)\n    #define CYTHON_USE_ASYNC_SLOTS 1\n  #endif\n  #if PY_VERSION_HEX < 0x02070000\n    #undef CYTHON_USE_PYLONG_INTERNALS\n    #define CYTHON_USE_PYLONG_INTERNALS 0\n  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)\n    #define CYTHON_USE_PYLONG_INTERNALS 1\n  #endif\n  #ifndef CYTHON_USE_PYLIST_INTERNALS\n    #define CYTHON_USE_PYLIST_INTERNALS 1\n  #endif\n  #ifndef CYTHON_USE_UNICODE_INTERNALS\n    #define CYTHON_USE_UNICODE_INTERNALS 1\n  #endif\n  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2\n    #undef CYTHON_USE_UNICODE_WRITER\n    #define CYTHON_USE_UNICODE_WRITER 0\n  #elif !defined(CYTHON_USE_UNICODE_WRITER)\n    #define CYTHON_USE_UNICODE_WRITER 1\n  #endif\n  #ifndef CYTHON_AVOID_BORROWED_REFS\n    #define CYTHON_AVOID_BORROWED_REFS 0\n  #endif\n  #ifndef CYTHON_ASSUME_SAFE_MACROS\n    #define CYTHON_ASSUME_SAFE_MACROS 1\n  #endif\n  #ifndef CYTHON_UNPACK_METHODS\n    #define CYTHON_UNPACK_METHODS 1\n  #endif\n  #if PY_VERSION_HEX >= 0x030B00A4\n    #undef CYTHON_FAST_THREAD_STATE\n    #define CYTHON_FAST_THREAD_STATE 0\n  #elif !defined(CYTHON_FAST_THREAD_STATE)\n    #define CYTHON_FAST_THREAD_STATE 1\n  #endif\n  #ifndef CYTHON_FAST_PYCALL\n    #define CYTHON_FAST_PYCALL (PY_VERSION_HEX < 0x030A0000)\n  #endif\n  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT\n    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)\n  #endif\n  #ifndef CYTHON_USE_TP_FINALIZE\n    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)\n  #endif\n  #ifndef CYTHON_USE_DICT_VERSIONS\n    #define CYTHON_USE_DICT_VERSIONS (PY_VERSION_HEX >= 0x030600B1)\n  #endif\n  #if PY_VERSION_HEX >= 0x030B00A4\n    #undef CYTHON_USE_EXC_INFO_STACK\n    #define CYTHON_USE_EXC_INFO_STACK 0\n  #elif !defined(CYTHON_USE_EXC_INFO_STACK)\n    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)\n  #endif\n  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC\n    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1\n  #endif\n#endif\n#if !defined(CYTHON_FAST_PYCCALL)\n#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)\n#endif\n#if CYTHON_USE_PYLONG_INTERNALS\n  #if PY_MAJOR_VERSION < 3\n    #include "longintrepr.h"\n  #endif\n  #undef SHIFT\n  #undef BASE\n  #undef MASK\n  #ifdef SIZEOF_VOID_P\n    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };\n  #endif\n#endif\n#ifndef __has_attribute\n  #define __has_attribute(x) 0\n#endif\n#ifndef __has_cpp_attribute\n  #define __has_cpp_attribute(x) 0\n#endif\n#ifndef CYTHON_RESTRICT\n  #if defined(__GNUC__)\n    #define CYTHON_RESTRICT __restrict__\n  #elif defined(_MSC_VER) && _MSC_VER >= 1400\n    #define CYTHON_RESTRICT __restrict\n  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L\n    #define CYTHON_RESTRICT restrict\n  #else\n    #define CYTHON_RESTRICT\n  #endif\n#endif\n#ifndef CYTHON_UNUSED\n# if defined(__GNUC__)\n#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))\n#     define CYTHON_UNUSED __attribute__ ((__unused__))\n#   else\n#     define CYTHON_UNUSED\n#   endif\n# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))\n#   define CYTHON_UNUSED __attribute__ ((__unused__))\n# else\n#   define CYTHON_UNUSED\n# endif\n#endif\n#ifndef CYTHON_MAYBE_UNUSED_VAR\n#  if defined(__cplusplus)\n     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }\n#  else\n#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)\n#  endif\n#endif\n#ifndef CYTHON_NCP_UNUSED\n# if CYTHON_COMPILING_IN_CPYTHON\n#  define CYTHON_NCP_UNUSED\n# else\n#  define CYTHON_NCP_UNUSED CYTHON_UNUSED\n# endif\n#endif\n#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)\n#ifdef _MSC_VER\n    #ifndef _MSC_STDINT_H_\n        #if _MSC_VER < 1300\n           typedef unsigned char     uint8_t;\n           typedef unsigned int      uint32_t;\n        #else\n           typedef unsigned __int8   uint8_t;\n           typedef unsigned __int32  uint32_t;\n        #endif\n    #endif\n#else\n   #include <stdint.h>\n#endif\n#ifndef CYTHON_FALLTHROUGH\n  #if defined(__cplusplus) && __cplusplus >= 201103L\n    #if __has_cpp_attribute(fallthrough)\n      #define CYTHON_FALLTHROUGH [[fallthrough]]\n    #elif __has_cpp_attribute(clang::fallthrough)\n      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]\n    #elif __has_cpp_attribute(gnu::fallthrough)\n      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]\n    #endif\n  #endif\n  #ifndef CYTHON_FALLTHROUGH\n    #if __has_attribute(fallthrough)\n      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))\n    #else\n      #define CYTHON_FALLTHROUGH\n    #endif\n  #endif\n  #if defined(__clang__ ) && defined(__apple_build_version__)\n    #if __apple_build_version__ < 7000000\n      #undef  CYTHON_FALLTHROUGH\n      #define CYTHON_FALLTHROUGH\n    #endif\n  #endif\n#endif\n\n#ifndef CYTHON_INLINE\n  #if defined(__clang__)\n    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))\n  #elif defined(__GNUC__)\n    #define CYTHON_INLINE __inline__\n  #elif defined(_MSC_VER)\n    #define CYTHON_INLINE __inline\n  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L\n    #define CYTHON_INLINE inline\n  #else\n    #define CYTHON_INLINE\n  #endif\n#endif\n\n#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX < 0x02070600 && !defined(Py_OptimizeFlag)\n  #define Py_OptimizeFlag 0\n#endif\n#define __PYX_BUILD_PY_SSIZE_T "n"\n#define CYTHON_FORMAT_SSIZE_T "z"\n#if PY_MAJOR_VERSION < 3\n  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"\n  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\\\n          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\n  #define __Pyx_DefaultClassType PyClass_Type\n#else\n  #define __Pyx_BUILTIN_MODULE_NAME "builtins"\n  #define __Pyx_DefaultClassType PyType_Type\n#if PY_VERSION_HEX >= 0x030B00A1\n    static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int k, int l, int s, int f,\n                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,\n                                                    PyObject *fv, PyObject *cell, PyObject* fn,\n                                                    PyObject *name, int fline, PyObject *lnos) {\n        PyObject *kwds=NULL, *argcount=NULL, *posonlyargcount=NULL, *kwonlyargcount=NULL;\n        PyObject *nlocals=NULL, *stacksize=NULL, *flags=NULL, *replace=NULL, *call_result=NULL, *empty=NULL;\n        const char *fn_cstr=NULL;\n        const char *name_cstr=NULL;\n        PyCodeObject* co=NULL;\n        PyObject *type, *value, *traceback;\n        PyErr_Fetch(&type, &value, &traceback);\n        if (!(kwds=PyDict_New())) goto end;\n        if (!(argcount=PyLong_FromLong(a))) goto end;\n        if (PyDict_SetItemString(kwds, "co_argcount", argcount) != 0) goto end;\n        if (!(posonlyargcount=PyLong_FromLong(0))) goto end;\n        if (PyDict_SetItemString(kwds, "co_posonlyargcount", posonlyargcount) != 0) goto end;\n        if (!(kwonlyargcount=PyLong_FromLong(k))) goto end;\n        if (PyDict_SetItemString(kwds, "co_kwonlyargcount", kwonlyargcount) != 0) goto end;\n        if (!(nlocals=PyLong_FromLong(l))) goto end;\n        if (PyDict_SetItemString(kwds, "co_nlocals", nlocals) != 0) goto end;\n        if (!(stacksize=PyLong_FromLong(s))) goto end;\n        if (PyDict_SetItemString(kwds, "co_stacksize", stacksize) != 0) goto end;\n        if (!(flags=PyLong_FromLong(f))) goto end;\n        if (PyDict_SetItemString(kwds, "co_flags", flags) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_code", code) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_consts", c) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_names", n) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_varnames", v) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_freevars", fv) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_cellvars", cell) != 0) goto end;\n        if (PyDict_SetItemString(kwds, "co_linetable", lnos) != 0) goto end;\n        if (!(fn_cstr=PyUnicode_AsUTF8AndSize(fn, NULL))) goto end;\n        if (!(name_cstr=PyUnicode_AsUTF8AndSize(name, NULL))) goto end;\n        if (!(co = PyCode_NewEmpty(fn_cstr, name_cstr, fline))) goto end;\n        if (!(replace = PyObject_GetAttrString((PyObject*)co, "replace"))) goto cleanup_code_too;\n        if (!(empty = PyTuple_New(0))) goto cleanup_code_too; // unfortunately __pyx_empty_tuple isn\'t available here\n        if (!(call_result = PyObject_Call(replace, empty, kwds))) goto cleanup_code_too;\n        Py_XDECREF((PyObject*)co);\n        co = (PyCodeObject*)call_result;\n        call_result = NULL;\n        if (0) {\n            cleanup_code_too:\n            Py_XDECREF((PyObject*)co);\n            co = NULL;\n        }\n        end:\n        Py_XDECREF(kwds);\n        Py_XDECREF(argcount);\n        Py_XDECREF(posonlyargcount);\n        Py_XDECREF(kwonlyargcount);\n        Py_XDECREF(nlocals);\n        Py_XDECREF(stacksize);\n        Py_XDECREF(replace);\n        Py_XDECREF(call_result);\n        Py_XDECREF(empty);\n        if (type) {\n            PyErr_Restore(type, value, traceback);\n        }\n        return co;\n    }\n#else\n  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\\\n          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\n#endif\n  #define __Pyx_DefaultClassType PyType_Type\n#endif\n#ifndef Py_TPFLAGS_CHECKTYPES\n  #define Py_TPFLAGS_CHECKTYPES 0\n#endif\n#ifndef Py_TPFLAGS_HAVE_INDEX\n  #define Py_TPFLAGS_HAVE_INDEX 0\n#endif\n#ifndef Py_TPFLAGS_HAVE_NEWBUFFER\n  #define Py_TPFLAGS_HAVE_NEWBUFFER 0\n#endif\n#ifndef Py_TPFLAGS_HAVE_FINALIZE\n  #define Py_TPFLAGS_HAVE_FINALIZE 0\n#endif\n#ifndef METH_STACKLESS\n  #define METH_STACKLESS 0\n#endif\n#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)\n  #ifndef METH_FASTCALL\n     #define METH_FASTCALL 0x80\n  #endif\n  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);\n  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,\n                                                          Py_ssize_t nargs, PyObject *kwnames);\n#else\n  #define __Pyx_PyCFunctionFast _PyCFunctionFast\n  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords\n#endif\n#if CYTHON_FAST_PYCCALL\n#define __Pyx_PyFastCFunction_Check(func)\\\n    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))\n#else\n#define __Pyx_PyFastCFunction_Check(func) 0\n#endif\n#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)\n  #define PyObject_Malloc(s)   PyMem_Malloc(s)\n  #define PyObject_Free(p)     PyMem_Free(p)\n  #define PyObject_Realloc(p)  PyMem_Realloc(p)\n#endif\n#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1\n  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)\n  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)\n  #define PyMem_RawFree(p)             PyMem_Free(p)\n#endif\n#if CYTHON_COMPILING_IN_PYSTON\n  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)\n  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)\n#else\n  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)\n  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)\n#endif\n#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000\n  #define __Pyx_PyThreadState_Current PyThreadState_GET()\n#elif PY_VERSION_HEX >= 0x03060000\n  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()\n#elif PY_VERSION_HEX >= 0x03000000\n  #define __Pyx_PyThreadState_Current PyThreadState_GET()\n#else\n  #define __Pyx_PyThreadState_Current _PyThreadState_Current\n#endif\n#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)\n#include "pythread.h"\n#define Py_tss_NEEDS_INIT 0\ntypedef int Py_tss_t;\nstatic CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {\n  *key = PyThread_create_key();\n  return 0;\n}\nstatic CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {\n  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));\n  *key = Py_tss_NEEDS_INIT;\n  return key;\n}\nstatic CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {\n  PyObject_Free(key);\n}\nstatic CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {\n  return *key != Py_tss_NEEDS_INIT;\n}\nstatic CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {\n  PyThread_delete_key(*key);\n  *key = Py_tss_NEEDS_INIT;\n}\nstatic CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {\n  return PyThread_set_key_value(*key, value);\n}\nstatic CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {\n  return PyThread_get_key_value(*key);\n}\n#endif\n#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)\n#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))\n#else\n#define __Pyx_PyDict_NewPresized(n)  PyDict_New()\n#endif\n#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION\n  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)\n  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)\n#else\n  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)\n  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)\n#endif\n#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS\n#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)\n#else\n#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)\n#endif\n#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)\n  #define CYTHON_PEP393_ENABLED 1\n  #if PY_VERSION_HEX >= 0x030C0000\n    #define __Pyx_PyUnicode_READY(op)       (0)\n  #else\n    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\\\n                                                0 : _PyUnicode_Ready((PyObject *)(op)))\n  #endif\n  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)\n  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)\n  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)\n  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)\n  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)\n  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)\n  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)\n  #if PY_VERSION_HEX >= 0x030C0000\n    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))\n  #else\n    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000\n    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))\n    #else\n    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))\n    #endif\n  #endif\n#else\n  #define CYTHON_PEP393_ENABLED 0\n  #define PyUnicode_1BYTE_KIND  1\n  #define PyUnicode_2BYTE_KIND  2\n  #define PyUnicode_4BYTE_KIND  4\n  #define __Pyx_PyUnicode_READY(op)       (0)\n  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)\n  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))\n  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)\n  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))\n  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))\n  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))\n  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)\n  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))\n#endif\n#if CYTHON_COMPILING_IN_PYPY\n  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)\n  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)\n#else\n  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)\n  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\\\n      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))\n#endif\n#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)\n  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)\n#endif\n#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)\n  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)\n#endif\n#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)\n  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)\n#endif\n#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))\n#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))\n#if PY_MAJOR_VERSION >= 3\n  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)\n#else\n  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)\n#endif\n#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)\n  #define PyObject_ASCII(o)            PyObject_Repr(o)\n#endif\n#if PY_MAJOR_VERSION >= 3\n  #define PyBaseString_Type            PyUnicode_Type\n  #define PyStringObject               PyUnicodeObject\n  #define PyString_Type                PyUnicode_Type\n  #define PyString_Check               PyUnicode_Check\n  #define PyString_CheckExact          PyUnicode_CheckExact\n#ifndef PyObject_Unicode\n  #define PyObject_Unicode             PyObject_Str\n#endif\n#endif\n#if PY_MAJOR_VERSION >= 3\n  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)\n  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)\n#else\n  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))\n  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))\n#endif\n#ifndef PySet_CheckExact\n  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)\n#endif\n#if PY_VERSION_HEX >= 0x030900A4\n  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)\n  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)\n#else\n  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)\n  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)\n#endif\n#if CYTHON_ASSUME_SAFE_MACROS\n  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)\n#else\n  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)\n#endif\n#if PY_MAJOR_VERSION >= 3\n  #define PyIntObject                  PyLongObject\n  #define PyInt_Type                   PyLong_Type\n  #define PyInt_Check(op)              PyLong_Check(op)\n  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)\n  #define PyInt_FromString             PyLong_FromString\n  #define PyInt_FromUnicode            PyLong_FromUnicode\n  #define PyInt_FromLong               PyLong_FromLong\n  #define PyInt_FromSize_t             PyLong_FromSize_t\n  #define PyInt_FromSsize_t            PyLong_FromSsize_t\n  #define PyInt_AsLong                 PyLong_AsLong\n  #define PyInt_AS_LONG                PyLong_AS_LONG\n  #define PyInt_AsSsize_t              PyLong_AsSsize_t\n  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask\n  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask\n  #define PyNumber_Int                 PyNumber_Long\n#endif\n#if PY_MAJOR_VERSION >= 3\n  #define PyBoolObject                 PyLongObject\n#endif\n#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY\n  #ifndef PyUnicode_InternFromString\n    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)\n  #endif\n#endif\n#if PY_VERSION_HEX < 0x030200A4\n  typedef long Py_hash_t;\n  #define __Pyx_PyInt_FromHash_t PyInt_FromLong\n  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t\n#else\n  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t\n  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t\n#endif\n#if PY_MAJOR_VERSION >= 3\n  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? ((void)(klass), PyMethod_New(func, self)) : __Pyx_NewRef(func))\n#else\n  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)\n#endif\n#if CYTHON_USE_ASYNC_SLOTS\n  #if PY_VERSION_HEX >= 0x030500B1\n    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods\n    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)\n  #else\n    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))\n  #endif\n#else\n  #define __Pyx_PyType_AsAsync(obj) NULL\n#endif\n#ifndef __Pyx_PyAsyncMethodsStruct\n    typedef struct {\n        unaryfunc am_await;\n        unaryfunc am_aiter;\n        unaryfunc am_anext;\n    } __Pyx_PyAsyncMethodsStruct;\n#endif\n\n#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)\n  #if !defined(_USE_MATH_DEFINES)\n    #define _USE_MATH_DEFINES\n  #endif\n#endif\n#include <math.h>\n#ifdef NAN\n#define __PYX_NAN() ((float) NAN)\n#else\nstatic CYTHON_INLINE float __PYX_NAN() {\n  float value;\n  memset(&value, 0xFF, sizeof(value));\n  return value;\n}\n#endif\n#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)\n#define __Pyx_truncl trunc\n#else\n#define __Pyx_truncl truncl\n#endif\n\n#define __PYX_MARK_ERR_POS(f_index, lineno) \\\n    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__; (void)__pyx_clineno; }\n#define __PYX_ERR(f_index, lineno, Ln_error) \\\n    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }\n\n#ifndef __PYX_EXTERN_C\n  #ifdef __cplusplus\n    #define __PYX_EXTERN_C extern "C"\n  #else\n    #define __PYX_EXTERN_C extern\n  #endif\n#endif\n\n#define __PYX_HAVE__source\n#define __PYX_HAVE_API__source\n/* Early includes */\n#ifdef _OPENMP\n#include <omp.h>\n#endif /* _OPENMP */\n\n#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)\n#define CYTHON_WITHOUT_ASSERTIONS\n#endif\n\ntypedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;\n                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;\n\n#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0\n#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0\n#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)\n#define __PYX_DEFAULT_STRING_ENCODING ""\n#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString\n#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize\n#define __Pyx_uchar_cast(c) ((unsigned char)c)\n#define __Pyx_long_cast(x) ((long)x)\n#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\\\n    (sizeof(type) < sizeof(Py_ssize_t))  ||\\\n    (sizeof(type) > sizeof(Py_ssize_t) &&\\\n          likely(v < (type)PY_SSIZE_T_MAX ||\\\n                 v == (type)PY_SSIZE_T_MAX)  &&\\\n          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\\\n                                v == (type)PY_SSIZE_T_MIN)))  ||\\\n    (sizeof(type) == sizeof(Py_ssize_t) &&\\\n          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\\\n                               v == (type)PY_SSIZE_T_MAX)))  )\nstatic CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {\n    return (size_t) i < (size_t) limit;\n}\n#if defined (__cplusplus) && __cplusplus >= 201103L\n    #include <cstdlib>\n    #define __Pyx_sst_abs(value) std::abs(value)\n#elif SIZEOF_INT >= SIZEOF_SIZE_T\n    #define __Pyx_sst_abs(value) abs(value)\n#elif SIZEOF_LONG >= SIZEOF_SIZE_T\n    #define __Pyx_sst_abs(value) labs(value)\n#elif defined (_MSC_VER)\n    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))\n#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L\n    #define __Pyx_sst_abs(value) llabs(value)\n#elif defined (__GNUC__)\n    #define __Pyx_sst_abs(value) __builtin_llabs(value)\n#else\n    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)\n#endif\nstatic CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);\nstatic CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);\n#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))\n#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)\n#define __Pyx_PyBytes_FromString        PyBytes_FromString\n#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize\nstatic CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);\n#if PY_MAJOR_VERSION < 3\n    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString\n    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize\n#else\n    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString\n    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize\n#endif\n#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))\n#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))\n#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))\n#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))\n#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))\n#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))\n#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)\n#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)\n#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)\n#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)\n#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)\nstatic CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {\n    const Py_UNICODE *u_end = u;\n    while (*u_end++) ;\n    return (size_t)(u_end - u - 1);\n}\n#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))\n#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode\n#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode\n#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)\n#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)\nstatic CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);\nstatic CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);\nstatic CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);\nstatic CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);\n#define __Pyx_PySequence_Tuple(obj)\\\n    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))\nstatic CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);\nstatic CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);\nstatic CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);\n#if CYTHON_ASSUME_SAFE_MACROS\n#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))\n#else\n#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)\n#endif\n#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))\n#if PY_MAJOR_VERSION >= 3\n#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))\n#else\n#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))\n#endif\n#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))\n#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII\nstatic int __Pyx_sys_getdefaultencoding_not_ascii;\nstatic int __Pyx_init_sys_getdefaultencoding_params(void) {\n    PyObject* sys;\n    PyObject* default_encoding = NULL;\n    PyObject* ascii_chars_u = NULL;\n    PyObject* ascii_chars_b = NULL;\n    const char* default_encoding_c;\n    sys = PyImport_ImportModule("sys");\n    if (!sys) goto bad;\n    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);\n    Py_DECREF(sys);\n    if (!default_encoding) goto bad;\n    default_encoding_c = PyBytes_AsString(default_encoding);\n    if (!default_encoding_c) goto bad;\n    if (strcmp(default_encoding_c, "ascii") == 0) {\n        __Pyx_sys_getdefaultencoding_not_ascii = 0;\n    } else {\n        char ascii_chars[128];\n        int c;\n        for (c = 0; c < 128; c++) {\n            ascii_chars[c] = c;\n        }\n        __Pyx_sys_getdefaultencoding_not_ascii = 1;\n        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);\n        if (!ascii_chars_u) goto bad;\n        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);\n        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {\n            PyErr_Format(\n                PyExc_ValueError,\n                "This module compiled with c_string_encoding=ascii, but default encoding \'%.200s\' is not a superset of ascii.",\n                default_encoding_c);\n            goto bad;\n        }\n        Py_DECREF(ascii_chars_u);\n        Py_DECREF(ascii_chars_b);\n    }\n    Py_DECREF(default_encoding);\n    return 0;\nbad:\n    Py_XDECREF(default_encoding);\n    Py_XDECREF(ascii_chars_u);\n    Py_XDECREF(ascii_chars_b);\n    return -1;\n}\n#endif\n#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3\n#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)\n#else\n#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)\n#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT\nstatic char* __PYX_DEFAULT_STRING_ENCODING;\nstatic int __Pyx_init_sys_getdefaultencoding_params(void) {\n    PyObject* sys;\n    PyObject* default_encoding = NULL;\n    char* default_encoding_c;\n    sys = PyImport_ImportModule("sys");\n    if (!sys) goto bad;\n    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);\n    Py_DECREF(sys);\n    if (!default_encoding) goto bad;\n    default_encoding_c = PyBytes_AsString(default_encoding);\n    if (!default_encoding_c) goto bad;\n    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);\n    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;\n    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);\n    Py_DECREF(default_encoding);\n    return 0;\nbad:\n    Py_XDECREF(default_encoding);\n    return -1;\n}\n#endif\n#endif\n\n\n/* Test for GCC > 2.95 */\n#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))\n  #define likely(x)   __builtin_expect(!!(x), 1)\n  #define unlikely(x) __builtin_expect(!!(x), 0)\n#else /* !__GNUC__ or GCC < 2.95 */\n  #define likely(x)   (x)\n  #define unlikely(x) (x)\n#endif /* __GNUC__ */\nstatic CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }\n\nstatic PyObject *__pyx_m = NULL;\nstatic PyObject *__pyx_d;\nstatic PyObject *__pyx_b;\nstatic PyObject *__pyx_cython_runtime = NULL;\nstatic PyObject *__pyx_empty_tuple;\nstatic PyObject *__pyx_empty_bytes;\nstatic PyObject *__pyx_empty_unicode;\nstatic int __pyx_lineno;\nstatic int __pyx_clineno = 0;\nstatic const char * __pyx_cfilenm= __FILE__;\nstatic const char *__pyx_filename;\n\n\nstatic const char *__pyx_f[] = {\n  "source.py",\n};\n\n/*--- Type declarations ---*/\n\n/* --- Runtime support code (head) --- */\n/* Refnanny.proto */\n#ifndef CYTHON_REFNANNY\n  #define CYTHON_REFNANNY 0\n#endif\n#if CYTHON_REFNANNY\n  typedef struct {\n    void (*INCREF)(void*, PyObject*, int);\n    void (*DECREF)(void*, PyObject*, int);\n    void (*GOTREF)(void*, PyObject*, int);\n    void (*GIVEREF)(void*, PyObject*, int);\n    void* (*SetupContext)(const char*, int, const char*);\n    void (*FinishContext)(void**);\n  } __Pyx_RefNannyAPIStruct;\n  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;\n  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);\n  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;\n#ifdef WITH_THREAD\n  #define __Pyx_RefNannySetupContext(name, acquire_gil)\\\n          if (acquire_gil) {\\\n              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\\\n              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\\\n              PyGILState_Release(__pyx_gilstate_save);\\\n          } else {\\\n              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\\\n          }\n#else\n  #define __Pyx_RefNannySetupContext(name, acquire_gil)\\\n          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)\n#endif\n  #define __Pyx_RefNannyFinishContext()\\\n          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)\n  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)\n  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)\n  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)\n  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)\n  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)\n  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)\n  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)\n  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)\n#else\n  #define __Pyx_RefNannyDeclarations\n  #define __Pyx_RefNannySetupContext(name, acquire_gil)\n  #define __Pyx_RefNannyFinishContext()\n  #define __Pyx_INCREF(r) Py_INCREF(r)\n  #define __Pyx_DECREF(r) Py_DECREF(r)\n  #define __Pyx_GOTREF(r)\n  #define __Pyx_GIVEREF(r)\n  #define __Pyx_XINCREF(r) Py_XINCREF(r)\n  #define __Pyx_XDECREF(r) Py_XDECREF(r)\n  #define __Pyx_XGOTREF(r)\n  #define __Pyx_XGIVEREF(r)\n#endif\n#define __Pyx_XDECREF_SET(r, v) do {\\\n        PyObject *tmp = (PyObject *) r;\\\n        r = v; __Pyx_XDECREF(tmp);\\\n    } while (0)\n#define __Pyx_DECREF_SET(r, v) do {\\\n        PyObject *tmp = (PyObject *) r;\\\n        r = v; __Pyx_DECREF(tmp);\\\n    } while (0)\n#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)\n#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)\n\n/* PyObjectGetAttrStr.proto */\n#if CYTHON_USE_TYPE_SLOTS\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);\n#else\n#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)\n#endif\n\n/* GetBuiltinName.proto */\nstatic PyObject *__Pyx_GetBuiltinName(PyObject *name);\n\n/* RaiseArgTupleInvalid.proto */\nstatic void __Pyx_RaiseArgtupleInvalid(const char* func_name, int exact,\n    Py_ssize_t num_min, Py_ssize_t num_max, Py_ssize_t num_found);\n\n/* RaiseDoubleKeywords.proto */\nstatic void __Pyx_RaiseDoubleKeywordsError(const char* func_name, PyObject* kw_name);\n\n/* ParseKeywords.proto */\nstatic int __Pyx_ParseOptionalKeywords(PyObject *kwds, PyObject **argnames[],\\\n    PyObject *kwds2, PyObject *values[], Py_ssize_t num_pos_args,\\\n    const char* function_name);\n\n/* PyDictVersioning.proto */\n#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS\n#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)\n#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)\n#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\\\n    (version_var) = __PYX_GET_DICT_VERSION(dict);\\\n    (cache_var) = (value);\n#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\\\n    static PY_UINT64_T __pyx_dict_version = 0;\\\n    static PyObject *__pyx_dict_cached_value = NULL;\\\n    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\\\n        (VAR) = __pyx_dict_cached_value;\\\n    } else {\\\n        (VAR) = __pyx_dict_cached_value = (LOOKUP);\\\n        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\\\n    }\\\n}\nstatic CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);\nstatic CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);\nstatic CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);\n#else\n#define __PYX_GET_DICT_VERSION(dict)  (0)\n#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\n#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);\n#endif\n\n/* GetModuleGlobalName.proto */\n#if CYTHON_USE_DICT_VERSIONS\n#define __Pyx_GetModuleGlobalName(var, name)  do {\\\n    static PY_UINT64_T __pyx_dict_version = 0;\\\n    static PyObject *__pyx_dict_cached_value = NULL;\\\n    (var) = (likely(__pyx_dict_version == __PYX_GET_DICT_VERSION(__pyx_d))) ?\\\n        (likely(__pyx_dict_cached_value) ? __Pyx_NewRef(__pyx_dict_cached_value) : __Pyx_GetBuiltinName(name)) :\\\n        __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\\\n} while(0)\n#define __Pyx_GetModuleGlobalNameUncached(var, name)  do {\\\n    PY_UINT64_T __pyx_dict_version;\\\n    PyObject *__pyx_dict_cached_value;\\\n    (var) = __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\\\n} while(0)\nstatic PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value);\n#else\n#define __Pyx_GetModuleGlobalName(var, name)  (var) = __Pyx__GetModuleGlobalName(name)\n#define __Pyx_GetModuleGlobalNameUncached(var, name)  (var) = __Pyx__GetModuleGlobalName(name)\nstatic CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name);\n#endif\n\n/* PyCFunctionFastCall.proto */\n#if CYTHON_FAST_PYCCALL\nstatic CYTHON_INLINE PyObject *__Pyx_PyCFunction_FastCall(PyObject *func, PyObject **args, Py_ssize_t nargs);\n#else\n#define __Pyx_PyCFunction_FastCall(func, args, nargs)  (assert(0), NULL)\n#endif\n\n/* PyFunctionFastCall.proto */\n#if CYTHON_FAST_PYCALL\n#define __Pyx_PyFunction_FastCall(func, args, nargs)\\\n    __Pyx_PyFunction_FastCallDict((func), (args), (nargs), NULL)\n#if 1 || PY_VERSION_HEX < 0x030600B1\nstatic PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs);\n#else\n#define __Pyx_PyFunction_FastCallDict(func, args, nargs, kwargs) _PyFunction_FastCallDict(func, args, nargs, kwargs)\n#endif\n#define __Pyx_BUILD_ASSERT_EXPR(cond)\\\n    (sizeof(char [1 - 2*!(cond)]) - 1)\n#ifndef Py_MEMBER_SIZE\n#define Py_MEMBER_SIZE(type, member) sizeof(((type *)0)->member)\n#endif\n#if CYTHON_FAST_PYCALL\n  static size_t __pyx_pyframe_localsplus_offset = 0;\n  #include "frameobject.h"\n#if PY_VERSION_HEX >= 0x030b00a6\n  #ifndef Py_BUILD_CORE\n    #define Py_BUILD_CORE 1\n  #endif\n  #include "internal/pycore_frame.h"\n#endif\n  #define __Pxy_PyFrame_Initialize_Offsets()\\\n    ((void)__Pyx_BUILD_ASSERT_EXPR(sizeof(PyFrameObject) == offsetof(PyFrameObject, f_localsplus) + Py_MEMBER_SIZE(PyFrameObject, f_localsplus)),\\\n     (void)(__pyx_pyframe_localsplus_offset = ((size_t)PyFrame_Type.tp_basicsize) - Py_MEMBER_SIZE(PyFrameObject, f_localsplus)))\n  #define __Pyx_PyFrame_GetLocalsplus(frame)\\\n    (assert(__pyx_pyframe_localsplus_offset), (PyObject **)(((char *)(frame)) + __pyx_pyframe_localsplus_offset))\n#endif // CYTHON_FAST_PYCALL\n#endif\n\n/* PyObjectCall.proto */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw);\n#else\n#define __Pyx_PyObject_Call(func, arg, kw) PyObject_Call(func, arg, kw)\n#endif\n\n/* PyObjectCall2Args.proto */\nstatic CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2);\n\n/* PyObjectCallMethO.proto */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg);\n#endif\n\n/* PyObjectCallOneArg.proto */\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg);\n\n/* PyIntCompare.proto */\nstatic CYTHON_INLINE PyObject* __Pyx_PyInt_EqObjC(PyObject *op1, PyObject *op2, long intval, long inplace);\n\n/* PyObjectCallNoArg.proto */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func);\n#else\n#define __Pyx_PyObject_CallNoArg(func) __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL)\n#endif\n\n/* PySequenceContains.proto */\nstatic CYTHON_INLINE int __Pyx_PySequence_ContainsTF(PyObject* item, PyObject* seq, int eq) {\n    int result = PySequence_Contains(seq, item);\n    return unlikely(result < 0) ? result : (result == (eq == Py_EQ));\n}\n\n/* DictGetItem.proto */\n#if PY_MAJOR_VERSION >= 3 && !CYTHON_COMPILING_IN_PYPY\nstatic PyObject *__Pyx_PyDict_GetItem(PyObject *d, PyObject* key);\n#define __Pyx_PyObject_Dict_GetItem(obj, name)\\\n    (likely(PyDict_CheckExact(obj)) ?\\\n     __Pyx_PyDict_GetItem(obj, name) : PyObject_GetItem(obj, name))\n#else\n#define __Pyx_PyDict_GetItem(d, key) PyObject_GetItem(d, key)\n#define __Pyx_PyObject_Dict_GetItem(obj, name)  PyObject_GetItem(obj, name)\n#endif\n\n/* PyObjectFormatSimple.proto */\n#if CYTHON_COMPILING_IN_PYPY\n    #define __Pyx_PyObject_FormatSimple(s, f) (\\\n        likely(PyUnicode_CheckExact(s)) ? (Py_INCREF(s), s) :\\\n        PyObject_Format(s, f))\n#elif PY_MAJOR_VERSION < 3\n    #define __Pyx_PyObject_FormatSimple(s, f) (\\\n        likely(PyUnicode_CheckExact(s)) ? (Py_INCREF(s), s) :\\\n        likely(PyString_CheckExact(s)) ? PyUnicode_FromEncodedObject(s, NULL, "strict") :\\\n        PyObject_Format(s, f))\n#elif CYTHON_USE_TYPE_SLOTS\n    #define __Pyx_PyObject_FormatSimple(s, f) (\\\n        likely(PyUnicode_CheckExact(s)) ? (Py_INCREF(s), s) :\\\n        likely(PyLong_CheckExact(s)) ? PyLong_Type.tp_str(s) :\\\n        likely(PyFloat_CheckExact(s)) ? PyFloat_Type.tp_str(s) :\\\n        PyObject_Format(s, f))\n#else\n    #define __Pyx_PyObject_FormatSimple(s, f) (\\\n        likely(PyUnicode_CheckExact(s)) ? (Py_INCREF(s), s) :\\\n        PyObject_Format(s, f))\n#endif\n\n/* IncludeStringH.proto */\n#include <string.h>\n\n/* JoinPyUnicode.proto */\nstatic PyObject* __Pyx_PyUnicode_Join(PyObject* value_tuple, Py_ssize_t value_count, Py_ssize_t result_ulength,\n                                      Py_UCS4 max_char);\n\n/* GetTopmostException.proto */\n#if CYTHON_USE_EXC_INFO_STACK\nstatic _PyErr_StackItem * __Pyx_PyErr_GetTopmostException(PyThreadState *tstate);\n#endif\n\n/* PyThreadStateGet.proto */\n#if CYTHON_FAST_THREAD_STATE\n#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;\n#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;\n#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type\n#else\n#define __Pyx_PyThreadState_declare\n#define __Pyx_PyThreadState_assign\n#define __Pyx_PyErr_Occurred()  PyErr_Occurred()\n#endif\n\n/* SaveResetException.proto */\n#if CYTHON_FAST_THREAD_STATE\n#define __Pyx_ExceptionSave(type, value, tb)  __Pyx__ExceptionSave(__pyx_tstate, type, value, tb)\nstatic CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);\n#define __Pyx_ExceptionReset(type, value, tb)  __Pyx__ExceptionReset(__pyx_tstate, type, value, tb)\nstatic CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);\n#else\n#define __Pyx_ExceptionSave(type, value, tb)   PyErr_GetExcInfo(type, value, tb)\n#define __Pyx_ExceptionReset(type, value, tb)  PyErr_SetExcInfo(type, value, tb)\n#endif\n\n/* PyErrExceptionMatches.proto */\n#if CYTHON_FAST_THREAD_STATE\n#define __Pyx_PyErr_ExceptionMatches(err) __Pyx_PyErr_ExceptionMatchesInState(__pyx_tstate, err)\nstatic CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err);\n#else\n#define __Pyx_PyErr_ExceptionMatches(err)  PyErr_ExceptionMatches(err)\n#endif\n\n/* PyErrFetchRestore.proto */\n#if CYTHON_FAST_THREAD_STATE\n#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)\n#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)\n#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)\n#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)\n#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)\nstatic CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);\nstatic CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);\n#if CYTHON_COMPILING_IN_CPYTHON\n#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))\n#else\n#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)\n#endif\n#else\n#define __Pyx_PyErr_Clear() PyErr_Clear()\n#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)\n#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)\n#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)\n#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)\n#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)\n#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)\n#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)\n#endif\n\n/* Import.proto */\nstatic PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level);\n\n/* FetchCommonType.proto */\nstatic PyTypeObject* __Pyx_FetchCommonType(PyTypeObject* type);\n\n/* CythonFunctionShared.proto */\n#define __Pyx_CyFunction_USED 1\n#define __Pyx_CYFUNCTION_STATICMETHOD  0x01\n#define __Pyx_CYFUNCTION_CLASSMETHOD   0x02\n#define __Pyx_CYFUNCTION_CCLASS        0x04\n#define __Pyx_CyFunction_GetClosure(f)\\\n    (((__pyx_CyFunctionObject *) (f))->func_closure)\n#define __Pyx_CyFunction_GetClassObj(f)\\\n    (((__pyx_CyFunctionObject *) (f))->func_classobj)\n#define __Pyx_CyFunction_Defaults(type, f)\\\n    ((type *)(((__pyx_CyFunctionObject *) (f))->defaults))\n#define __Pyx_CyFunction_SetDefaultsGetter(f, g)\\\n    ((__pyx_CyFunctionObject *) (f))->defaults_getter = (g)\ntypedef struct {\n    PyCFunctionObject func;\n#if PY_VERSION_HEX < 0x030500A0\n    PyObject *func_weakreflist;\n#endif\n    PyObject *func_dict;\n    PyObject *func_name;\n    PyObject *func_qualname;\n    PyObject *func_doc;\n    PyObject *func_globals;\n    PyObject *func_code;\n    PyObject *func_closure;\n    PyObject *func_classobj;\n    void *defaults;\n    int defaults_pyobjects;\n    size_t defaults_size;  // used by FusedFunction for copying defaults\n    int flags;\n    PyObject *defaults_tuple;\n    PyObject *defaults_kwdict;\n    PyObject *(*defaults_getter)(PyObject *);\n    PyObject *func_annotations;\n} __pyx_CyFunctionObject;\nstatic PyTypeObject *__pyx_CyFunctionType = 0;\n#define __Pyx_CyFunction_Check(obj)  (__Pyx_TypeCheck(obj, __pyx_CyFunctionType))\nstatic PyObject *__Pyx_CyFunction_Init(__pyx_CyFunctionObject* op, PyMethodDef *ml,\n                                      int flags, PyObject* qualname,\n                                      PyObject *self,\n                                      PyObject *module, PyObject *globals,\n                                      PyObject* code);\nstatic CYTHON_INLINE void *__Pyx_CyFunction_InitDefaults(PyObject *m,\n                                                         size_t size,\n                                                         int pyobjects);\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsTuple(PyObject *m,\n                                                            PyObject *tuple);\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsKwDict(PyObject *m,\n                                                             PyObject *dict);\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetAnnotationsDict(PyObject *m,\n                                                              PyObject *dict);\nstatic int __pyx_CyFunction_init(void);\n\n/* CythonFunction.proto */\nstatic PyObject *__Pyx_CyFunction_New(PyMethodDef *ml,\n                                      int flags, PyObject* qualname,\n                                      PyObject *closure,\n                                      PyObject *module, PyObject *globals,\n                                      PyObject* code);\n\n/* BytesEquals.proto */\nstatic CYTHON_INLINE int __Pyx_PyBytes_Equals(PyObject* s1, PyObject* s2, int equals);\n\n/* UnicodeEquals.proto */\nstatic CYTHON_INLINE int __Pyx_PyUnicode_Equals(PyObject* s1, PyObject* s2, int equals);\n\n/* PyIntBinop.proto */\n#if !CYTHON_COMPILING_IN_PYPY\nstatic PyObject* __Pyx_PyInt_AddObjC(PyObject *op1, PyObject *op2, long intval, int inplace, int zerodivision_check);\n#else\n#define __Pyx_PyInt_AddObjC(op1, op2, intval, inplace, zerodivision_check)\\\n    (inplace ? PyNumber_InPlaceAdd(op1, op2) : PyNumber_Add(op1, op2))\n#endif\n\n/* PyIntBinop.proto */\n#if !CYTHON_COMPILING_IN_PYPY\nstatic PyObject* __Pyx_PyInt_RemainderObjC(PyObject *op1, PyObject *op2, long intval, int inplace, int zerodivision_check);\n#else\n#define __Pyx_PyInt_RemainderObjC(op1, op2, intval, inplace, zerodivision_check)\\\n    (inplace ? PyNumber_InPlaceRemainder(op1, op2) : PyNumber_Remainder(op1, op2))\n#endif\n\n/* PyIntCompare.proto */\nstatic CYTHON_INLINE PyObject* __Pyx_PyInt_NeObjC(PyObject *op1, PyObject *op2, long intval, long inplace);\n\n/* ListAppend.proto */\n#if CYTHON_USE_PYLIST_INTERNALS && CYTHON_ASSUME_SAFE_MACROS\nstatic CYTHON_INLINE int __Pyx_PyList_Append(PyObject* list, PyObject* x) {\n    PyListObject* L = (PyListObject*) list;\n    Py_ssize_t len = Py_SIZE(list);\n    if (likely(L->allocated > len) & likely(len > (L->allocated >> 1))) {\n        Py_INCREF(x);\n        PyList_SET_ITEM(list, len, x);\n        __Pyx_SET_SIZE(list, len + 1);\n        return 0;\n    }\n    return PyList_Append(list, x);\n}\n#else\n#define __Pyx_PyList_Append(L,x) PyList_Append(L,x)\n#endif\n\n/* PyObjectGetMethod.proto */\nstatic int __Pyx_PyObject_GetMethod(PyObject *obj, PyObject *name, PyObject **method);\n\n/* PyObjectCallMethod1.proto */\nstatic PyObject* __Pyx_PyObject_CallMethod1(PyObject* obj, PyObject* method_name, PyObject* arg);\n\n/* append.proto */\nstatic CYTHON_INLINE int __Pyx_PyObject_Append(PyObject* L, PyObject* x);\n\n/* CLineInTraceback.proto */\n#ifdef CYTHON_CLINE_IN_TRACEBACK\n#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)\n#else\nstatic int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);\n#endif\n\n/* CodeObjectCache.proto */\ntypedef struct {\n    PyCodeObject* code_object;\n    int code_line;\n} __Pyx_CodeObjectCacheEntry;\nstruct __Pyx_CodeObjectCache {\n    int count;\n    int max_count;\n    __Pyx_CodeObjectCacheEntry* entries;\n};\nstatic struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};\nstatic int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);\nstatic PyCodeObject *__pyx_find_code_object(int code_line);\nstatic void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);\n\n/* AddTraceback.proto */\nstatic void __Pyx_AddTraceback(const char *funcname, int c_line,\n                               int py_line, const char *filename);\n\n/* GCCDiagnostics.proto */\n#if defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))\n#define __Pyx_HAS_GCC_DIAGNOSTIC\n#endif\n\n/* CIntToPy.proto */\nstatic CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);\n\n/* CIntFromPy.proto */\nstatic CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);\n\n/* CIntFromPy.proto */\nstatic CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);\n\n/* FastTypeChecks.proto */\n#if CYTHON_COMPILING_IN_CPYTHON\n#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)\nstatic CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);\nstatic CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);\nstatic CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);\n#else\n#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)\n#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)\n#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))\n#endif\n#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)\n\n/* CheckBinaryVersion.proto */\nstatic int __Pyx_check_binary_version(void);\n\n/* InitStrings.proto */\nstatic int __Pyx_InitStrings(__Pyx_StringTabEntry *t);\n\n\n/* Module declarations from \'source\' */\n#define __Pyx_MODULE_NAME "source"\nextern int __pyx_module_is_main_source;\nint __pyx_module_is_main_source = 0;\n\n/* Implementation of \'source\' */\nstatic PyObject *__pyx_builtin_print;\nstatic PyObject *__pyx_builtin_input;\nstatic PyObject *__pyx_builtin_range;\nstatic const char __pyx_k_[] = "\\331\\205\\330\\250\\330\\261\\331\\210\\331\\203 \\331\\204\\331\\203 ";\nstatic const char __pyx_k_1[] = "?1";\nstatic const char __pyx_k_5[] = "5";\nstatic const char __pyx_k_F[] = "F";\nstatic const char __pyx_k_0m[] = "\\033[0m";\nstatic const char __pyx_k_AR[] = "AR";\nstatic const char __pyx_k__2[] = " \\360\\237\\224\\245\\360\\237\\230\\202";\nstatic const char __pyx_k__3[] = "\\330\\252\\331\\205 \\330\\247\\331\\204\\330\\247\\330\\264\\330\\252\\330\\261\\330\\247\\331\\203 \\331\\201\\331\\212 \\330\\247\\331\\204\\330\\271\\330\\261\\330\\266 \\330\\250\\331\\206\\330\\254\\330\\247\\330\\255!";\nstatic const char __pyx_k__4[] = "\\331\\201\\330\\264\\331\\204\\330\\252 \\330\\271\\331\\205\\331\\204\\331\\212\\330\\251 \\330\\247\\331\\204\\330\\247\\330\\264\\330\\252\\330\\261\\330\\247\\331\\203 \\342\\235\\214";\nstatic const char __pyx_k__5[] = "\\331\\204\\330\\247 \\331\\212\\331\\210\\330\\254\\330\\257 \\331\\207\\330\\257\\330\\247\\331\\212\\330\\247 \\342\\235\\214";\nstatic const char __pyx_k_id[] = "\\"id\\":\\"(.*?)\\"";\nstatic const char __pyx_k_re[] = "re";\nstatic const char __pyx_k_500[] = "500";\nstatic const char __pyx_k_91m[] = "\\033[91m";\nstatic const char __pyx_k_92m[] = "\\033[92m";\nstatic const char __pyx_k_93m[] = "\\033[93m";\nstatic const char __pyx_k_94m[] = "\\033[94m------------------------------";\nstatic const char __pyx_k_95m[] = "\\033[95m";\nstatic const char __pyx_k_96m[] = "\\033[96m";\nstatic const char __pyx_k__12[] = "";\nstatic const char __pyx_k__14[] = "\\330\\247\\331\\204\\330\\255\\330\\263\\330\\247\\330\\250 \\330\\272\\331\\212\\330\\261 \\330\\265\\330\\255\\331\\212\\330\\255 \\342\\235\\214";\nstatic const char __pyx_k__15[] = "\\330\\247\\331\\206\\330\\252\\331\\207\\330\\252 \\330\\265\\331\\204\\330\\247\\330\\255\\331\\212\\330\\251 \\330\\247\\331\\204\\330\\252\\331\\210\\331\\203\\331\\206 \\342\\235\\214";\nstatic const char __pyx_k_art[] = "art";\nstatic const char __pyx_k_get[] = "get";\nstatic const char __pyx_k_Host[] = "Host";\nstatic const char __pyx_k_args[] = "args";\nstatic const char __pyx_k_cors[] = "cors";\nstatic const char __pyx_k_data[] = "data";\nstatic const char __pyx_k_id_2[] = "id";\nstatic const char __pyx_k_join[] = "join";\nstatic const char __pyx_k_json[] = "json";\nstatic const char __pyx_k_main[] = "__main__";\nstatic const char __pyx_k_name[] = "__name__";\nstatic const char __pyx_k_post[] = "post";\nstatic const char __pyx_k_test[] = "__test__";\nstatic const char __pyx_k_text[] = "text";\nstatic const char __pyx_k_type[] = "@type";\nstatic const char __pyx_k_2_32m[] = "\\033[2;32m";\nstatic const char __pyx_k_2_36m[] = "\\033[2;36m";\nstatic const char __pyx_k_95m_2[] = "\\033[95m------------------------------";\nstatic const char __pyx_k_96m_2[] = "\\033[96m------------------------------";\nstatic const char __pyx_k_Promo[] = "Promo";\nstatic const char __pyx_k_color[] = "color";\nstatic const char __pyx_k_empty[] = "empty";\nstatic const char __pyx_k_group[] = "group";\nstatic const char __pyx_k_input[] = "input";\nstatic const char __pyx_k_patch[] = "patch";\nstatic const char __pyx_k_print[] = "print";\nstatic const char __pyx_k_range[] = "range";\nstatic const char __pyx_k_start[] = "start";\nstatic const char __pyx_k_strip[] = "strip";\nstatic const char __pyx_k_token[] = "token";\nstatic const char __pyx_k_value[] = "value";\nstatic const char __pyx_k_Accept[] = "Accept";\nstatic const char __pyx_k_Bearer[] = "Bearer ";\nstatic const char __pyx_k_Thread[] = "Thread";\nstatic const char __pyx_k_append[] = "append";\nstatic const char __pyx_k_import[] = "__import__";\nstatic const char __pyx_k_msisdn[] = "msisdn";\nstatic const char __pyx_k_search[] = "search";\nstatic const char __pyx_k_source[] = "source";\nstatic const char __pyx_k_target[] = "target";\nstatic const char __pyx_k_thread[] = "thread";\nstatic const char __pyx_k_type_2[] = "type";\nstatic const char __pyx_k_Android[] = "\\"Android\\"";\nstatic const char __pyx_k_Referer[] = "Referer";\nstatic const char __pyx_k_attempt[] = "attempt";\nstatic const char __pyx_k_channel[] = "channel";\nstatic const char __pyx_k_context[] = "context";\nstatic const char __pyx_k_headers[] = "headers";\nstatic const char __pyx_k_threads[] = "threads";\nstatic const char __pyx_k_HWDRA_MR[] = "HWDRA-MR";\nstatic const char __pyx_k_Telegram[] = "Telegram: ";\nstatic const char __pyx_k_attempts[] = "attempts";\nstatic const char __pyx_k_clientId[] = "clientId";\nstatic const char __pyx_k_match_id[] = "match_id";\nstatic const char __pyx_k_password[] = "password";\nstatic const char __pyx_k_pyfiglet[] = "pyfiglet";\nstatic const char __pyx_k_requests[] = "requests";\nstatic const char __pyx_k_username[] = "username";\nstatic const char __pyx_k_El_Jo_NeT[] = "El Jo NeT";\nstatic const char __pyx_k_El_Sultan[] = "El Sultan";\nstatic const char __pyx_k_ascii_art[] = "ascii_art";\nstatic const char __pyx_k_client_id[] = "client_id";\nstatic const char __pyx_k_red_color[] = "red_color";\nstatic const char __pyx_k_sec_ch_ua[] = "sec-ch-ua";\nstatic const char __pyx_k_source_py[] = "source.py";\nstatic const char __pyx_k_threading[] = "threading";\nstatic const char __pyx_k_url_token[] = "url_token";\nstatic const char __pyx_k_2022_1_2_3[] = "2022.1.2.3";\nstatic const char __pyx_k_APP_PORTAL[] = "APP_PORTAL";\nstatic const char __pyx_k_Connection[] = "Connection";\nstatic const char __pyx_k_ELJoNet208[] = "@ELJoNet208 ";\nstatic const char __pyx_k_User_Agent[] = "User-Agent";\nstatic const char __pyx_k_data_token[] = "data_token";\nstatic const char __pyx_k_grant_type[] = "grant_type";\nstatic const char __pyx_k_keep_alive[] = "keep-alive";\nstatic const char __pyx_k_product_id[] = "product_id";\nstatic const char __pyx_k_separator1[] = "separator1";\nstatic const char __pyx_k_separator2[] = "separator2";\nstatic const char __pyx_k_separator3[] = "separator3";\nstatic const char __pyx_k_El_Sultan_2[] = "El Sultan: ";\nstatic const char __pyx_k_green_color[] = "green_color";\nstatic const char __pyx_k_match_value[] = "match_value";\nstatic const char __pyx_k_print_ascii[] = "print_ascii";\nstatic const char __pyx_k_reset_color[] = "reset_color";\nstatic const char __pyx_k_same_origin[] = "same-origin";\nstatic const char __pyx_k_status_code[] = "status_code";\nstatic const char __pyx_k_url_product[] = "url_product";\nstatic const char __pyx_k_Content_Type[] = "Content-Type";\nstatic const char __pyx_k_access_token[] = "access_token";\nstatic const char __pyx_k_okhttp_4_9_1[] = "okhttp/4.9.1";\nstatic const char __pyx_k_yellow_color[] = "yellow_color";\nstatic const char __pyx_k_Aba_Al_Hassan[] = "Aba Al-Hassan ";\nstatic const char __pyx_k_Authorization[] = "Authorization";\nstatic const char __pyx_k_client_secret[] = "client_secret";\nstatic const char __pyx_k_figlet_format[] = "figlet_format";\nstatic const char __pyx_k_headers_token[] = "headers_token";\nstatic const char __pyx_k_x_agent_build[] = "x-agent-build";\nstatic const char __pyx_k_10_1_0_264C185[] = "10.1.0.264C185";\nstatic const char __pyx_k_Enter_a_number[] = "Enter a number: ";\nstatic const char __pyx_k_Sec_Fetch_Dest[] = "Sec-Fetch-Dest";\nstatic const char __pyx_k_Sec_Fetch_Mode[] = "Sec-Fetch-Mode";\nstatic const char __pyx_k_Sec_Fetch_Site[] = "Sec-Fetch-Site";\nstatic const char __pyx_k_response_token[] = "response_token";\nstatic const char __pyx_k_x_agent_device[] = "x-agent-device";\nstatic const char __pyx_k_Accept_Encoding[] = "Accept-Encoding";\nstatic const char __pyx_k_Accept_Language[] = "Accept-Language";\nstatic const char __pyx_k_WebsiteConsumer[] = "WebsiteConsumer";\nstatic const char __pyx_k_headers_product[] = "headers_product";\nstatic const char __pyx_k_my_vodafone_app[] = "my-vodafone-app";\nstatic const char __pyx_k_rechargeProgram[] = "rechargeProgram";\nstatic const char __pyx_k_vodafoneandroid[] = "vodafoneandroid";\nstatic const char __pyx_k_x_agent_version[] = "x-agent-version";\nstatic const char __pyx_k_Enter_a_password[] = "Enter a password: ";\nstatic const char __pyx_k_application_json[] = "application/json";\nstatic const char __pyx_k_get_access_token[] = "get_access_token";\nstatic const char __pyx_k_light_blue_color[] = "light_blue_color";\nstatic const char __pyx_k_response_product[] = "response_product";\nstatic const char __pyx_k_sec_ch_ua_mobile[] = "sec-ch-ua-mobile";\nstatic const char __pyx_k_url_subscription[] = "url_subscription";\nstatic const char __pyx_k_data_subscription[] = "data_subscription";\nstatic const char __pyx_k_AnaVodafoneAndroid[] = "AnaVodafoneAndroid";\nstatic const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";\nstatic const char __pyx_k_sec_ch_ua_platform[] = "sec-ch-ua-platform";\nstatic const char __pyx_k_web_vodafone_com_eg[] = "web.vodafone.com.eg";\nstatic const char __pyx_k_gzip_deflate_br_zstd[] = "gzip, deflate, br, zstd";\nstatic const char __pyx_k_headers_subscription[] = "headers_subscription";\nstatic const char __pyx_k_response_subscription[] = "response_subscription";\nstatic const char __pyx_k_subscribe_to_promotion[] = "subscribe_to_promotion";\nstatic const char __pyx_k_x_agent_operatingsystem[] = "x-agent-operatingsystem";\nstatic const char __pyx_k_application_json_text_plain[] = "application/json, text/plain, */*";\nstatic const char __pyx_k_Chromium_v_124_Android_WebView[] = "\\"Chromium\\";v=\\"124\\", \\"Android WebView\\";v=\\"124\\", \\"Not-A.Brand\\";v=\\"99\\"";\nstatic const char __pyx_k_name_ShortScript_Assignment_val[] = "{\\"name\\":\\"ShortScript_Assignment\\",\\"value\\":\\"(.*?)\\"}";\nstatic const char __pyx_k_a2ec6fff_0b7f_4aa4_a733_96ceae5c[] = "a2ec6fff-0b7f-4aa4-a733-96ceae5c84c3";\nstatic const char __pyx_k_application_x_www_form_urlencode[] = "application/x-www-form-urlencoded";\nstatic const char __pyx_k_https_web_vodafone_com_eg_auth_r[] = "https://web.vodafone.com.eg/auth/realms/vf-realm/protocol/openid-connect/token";\nstatic const char __pyx_k_https_web_vodafone_com_eg_portal[] = "https://web.vodafone.com.eg/portal/bf/rechargeProgram";\nstatic const char __pyx_k_https_web_vodafone_com_eg_servic[] = "https://web.vodafone.com.eg/services/dxl/promo/promotion?@type=Promo&$.context.type=rechargeProgram";\nstatic const char __pyx_k_https_web_vodafone_com_eg_servic_2[] = "https://web.vodafone.com.eg/services/dxl/promo/promotion/";\nstatic PyObject *__pyx_kp_u_;\nstatic PyObject *__pyx_kp_u_0m;\nstatic PyObject *__pyx_kp_u_1;\nstatic PyObject *__pyx_kp_u_10_1_0_264C185;\nstatic PyObject *__pyx_kp_u_2022_1_2_3;\nstatic PyObject *__pyx_kp_u_2_32m;\nstatic PyObject *__pyx_kp_u_2_36m;\nstatic PyObject *__pyx_kp_u_5;\nstatic PyObject *__pyx_kp_u_500;\nstatic PyObject *__pyx_kp_u_91m;\nstatic PyObject *__pyx_kp_u_92m;\nstatic PyObject *__pyx_kp_u_93m;\nstatic PyObject *__pyx_kp_u_94m;\nstatic PyObject *__pyx_kp_u_95m;\nstatic PyObject *__pyx_kp_u_95m_2;\nstatic PyObject *__pyx_kp_u_96m;\nstatic PyObject *__pyx_kp_u_96m_2;\nstatic PyObject *__pyx_n_u_APP_PORTAL;\nstatic PyObject *__pyx_n_u_AR;\nstatic PyObject *__pyx_kp_u_Aba_Al_Hassan;\nstatic PyObject *__pyx_n_u_Accept;\nstatic PyObject *__pyx_kp_u_Accept_Encoding;\nstatic PyObject *__pyx_kp_u_Accept_Language;\nstatic PyObject *__pyx_n_u_AnaVodafoneAndroid;\nstatic PyObject *__pyx_kp_u_Android;\nstatic PyObject *__pyx_n_u_Authorization;\nstatic PyObject *__pyx_kp_u_Bearer;\nstatic PyObject *__pyx_kp_u_Chromium_v_124_Android_WebView;\nstatic PyObject *__pyx_n_u_Connection;\nstatic PyObject *__pyx_kp_u_Content_Type;\nstatic PyObject *__pyx_kp_u_ELJoNet208;\nstatic PyObject *__pyx_kp_u_El_Jo_NeT;\nstatic PyObject *__pyx_kp_u_El_Sultan;\nstatic PyObject *__pyx_kp_u_El_Sultan_2;\nstatic PyObject *__pyx_kp_u_Enter_a_number;\nstatic PyObject *__pyx_kp_u_Enter_a_password;\nstatic PyObject *__pyx_n_s_F;\nstatic PyObject *__pyx_kp_u_HWDRA_MR;\nstatic PyObject *__pyx_n_u_Host;\nstatic PyObject *__pyx_n_u_Promo;\nstatic PyObject *__pyx_n_u_Referer;\nstatic PyObject *__pyx_kp_u_Sec_Fetch_Dest;\nstatic PyObject *__pyx_kp_u_Sec_Fetch_Mode;\nstatic PyObject *__pyx_kp_u_Sec_Fetch_Site;\nstatic PyObject *__pyx_kp_u_Telegram;\nstatic PyObject *__pyx_n_s_Thread;\nstatic PyObject *__pyx_kp_u_User_Agent;\nstatic PyObject *__pyx_n_u_WebsiteConsumer;\nstatic PyObject *__pyx_kp_u__12;\nstatic PyObject *__pyx_kp_u__14;\nstatic PyObject *__pyx_kp_u__15;\nstatic PyObject *__pyx_kp_u__2;\nstatic PyObject *__pyx_kp_u__3;\nstatic PyObject *__pyx_kp_u__4;\nstatic PyObject *__pyx_kp_u__5;\nstatic PyObject *__pyx_kp_u_a2ec6fff_0b7f_4aa4_a733_96ceae5c;\nstatic PyObject *__pyx_n_u_access_token;\nstatic PyObject *__pyx_n_s_append;\nstatic PyObject *__pyx_kp_u_application_json;\nstatic PyObject *__pyx_kp_u_application_json_text_plain;\nstatic PyObject *__pyx_kp_u_application_x_www_form_urlencode;\nstatic PyObject *__pyx_n_s_args;\nstatic PyObject *__pyx_n_s_art;\nstatic PyObject *__pyx_n_s_ascii_art;\nstatic PyObject *__pyx_n_s_attempt;\nstatic PyObject *__pyx_n_s_attempts;\nstatic PyObject *__pyx_n_u_channel;\nstatic PyObject *__pyx_n_u_clientId;\nstatic PyObject *__pyx_n_u_client_id;\nstatic PyObject *__pyx_n_u_client_secret;\nstatic PyObject *__pyx_n_s_cline_in_traceback;\nstatic PyObject *__pyx_n_s_color;\nstatic PyObject *__pyx_n_u_context;\nstatic PyObject *__pyx_n_u_cors;\nstatic PyObject *__pyx_n_s_data;\nstatic PyObject *__pyx_n_s_data_subscription;\nstatic PyObject *__pyx_n_s_data_token;\nstatic PyObject *__pyx_n_u_empty;\nstatic PyObject *__pyx_n_s_figlet_format;\nstatic PyObject *__pyx_n_s_get;\nstatic PyObject *__pyx_n_s_get_access_token;\nstatic PyObject *__pyx_n_u_grant_type;\nstatic PyObject *__pyx_n_s_green_color;\nstatic PyObject *__pyx_n_s_group;\nstatic PyObject *__pyx_kp_u_gzip_deflate_br_zstd;\nstatic PyObject *__pyx_n_s_headers;\nstatic PyObject *__pyx_n_s_headers_product;\nstatic PyObject *__pyx_n_s_headers_subscription;\nstatic PyObject *__pyx_n_s_headers_token;\nstatic PyObject *__pyx_kp_u_https_web_vodafone_com_eg_auth_r;\nstatic PyObject *__pyx_kp_u_https_web_vodafone_com_eg_portal;\nstatic PyObject *__pyx_kp_u_https_web_vodafone_com_eg_servic;\nstatic PyObject *__pyx_kp_u_https_web_vodafone_com_eg_servic_2;\nstatic PyObject *__pyx_kp_u_id;\nstatic PyObject *__pyx_n_u_id_2;\nstatic PyObject *__pyx_n_s_import;\nstatic PyObject *__pyx_n_s_input;\nstatic PyObject *__pyx_n_s_join;\nstatic PyObject *__pyx_n_s_json;\nstatic PyObject *__pyx_kp_u_keep_alive;\nstatic PyObject *__pyx_n_s_light_blue_color;\nstatic PyObject *__pyx_n_s_main;\nstatic PyObject *__pyx_n_u_main;\nstatic PyObject *__pyx_n_s_match_id;\nstatic PyObject *__pyx_n_s_match_value;\nstatic PyObject *__pyx_n_u_msisdn;\nstatic PyObject *__pyx_kp_u_my_vodafone_app;\nstatic PyObject *__pyx_n_s_name;\nstatic PyObject *__pyx_kp_u_name_ShortScript_Assignment_val;\nstatic PyObject *__pyx_kp_u_okhttp_4_9_1;\nstatic PyObject *__pyx_n_s_password;\nstatic PyObject *__pyx_n_u_password;\nstatic PyObject *__pyx_n_s_patch;\nstatic PyObject *__pyx_n_s_post;\nstatic PyObject *__pyx_n_s_print;\nstatic PyObject *__pyx_n_s_print_ascii;\nstatic PyObject *__pyx_n_s_product_id;\nstatic PyObject *__pyx_n_s_pyfiglet;\nstatic PyObject *__pyx_n_s_range;\nstatic PyObject *__pyx_n_s_re;\nstatic PyObject *__pyx_n_u_rechargeProgram;\nstatic PyObject *__pyx_n_s_red_color;\nstatic PyObject *__pyx_n_s_requests;\nstatic PyObject *__pyx_n_s_reset_color;\nstatic PyObject *__pyx_n_s_response_product;\nstatic PyObject *__pyx_n_s_response_subscription;\nstatic PyObject *__pyx_n_s_response_token;\nstatic PyObject *__pyx_kp_u_same_origin;\nstatic PyObject *__pyx_n_s_search;\nstatic PyObject *__pyx_kp_u_sec_ch_ua;\nstatic PyObject *__pyx_kp_u_sec_ch_ua_mobile;\nstatic PyObject *__pyx_kp_u_sec_ch_ua_platform;\nstatic PyObject *__pyx_n_s_separator1;\nstatic PyObject *__pyx_n_s_separator2;\nstatic PyObject *__pyx_n_s_separator3;\nstatic PyObject *__pyx_n_s_source;\nstatic PyObject *__pyx_kp_s_source_py;\nstatic PyObject *__pyx_n_s_start;\nstatic PyObject *__pyx_n_s_status_code;\nstatic PyObject *__pyx_n_s_strip;\nstatic PyObject *__pyx_n_s_subscribe_to_promotion;\nstatic PyObject *__pyx_n_s_target;\nstatic PyObject *__pyx_n_s_test;\nstatic PyObject *__pyx_n_s_text;\nstatic PyObject *__pyx_n_s_thread;\nstatic PyObject *__pyx_n_s_threading;\nstatic PyObject *__pyx_n_s_threads;\nstatic PyObject *__pyx_n_s_token;\nstatic PyObject *__pyx_kp_u_type;\nstatic PyObject *__pyx_n_u_type_2;\nstatic PyObject *__pyx_n_s_url_product;\nstatic PyObject *__pyx_n_s_url_subscription;\nstatic PyObject *__pyx_n_s_url_token;\nstatic PyObject *__pyx_n_s_username;\nstatic PyObject *__pyx_n_u_username;\nstatic PyObject *__pyx_n_s_value;\nstatic PyObject *__pyx_n_u_vodafoneandroid;\nstatic PyObject *__pyx_kp_u_web_vodafone_com_eg;\nstatic PyObject *__pyx_kp_u_x_agent_build;\nstatic PyObject *__pyx_kp_u_x_agent_device;\nstatic PyObject *__pyx_kp_u_x_agent_operatingsystem;\nstatic PyObject *__pyx_kp_u_x_agent_version;\nstatic PyObject *__pyx_n_s_yellow_color;\nstatic PyObject *__pyx_pf_6source_print_ascii(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_art, PyObject *__pyx_v_color); /* proto */\nstatic PyObject *__pyx_pf_6source_2get_access_token(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_username, PyObject *__pyx_v_password); /* proto */\nstatic PyObject *__pyx_pf_6source_4subscribe_to_promotion(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_username, PyObject *__pyx_v_token, CYTHON_UNUSED PyObject *__pyx_v_attempt); /* proto */\nstatic PyObject *__pyx_int_1;\nstatic PyObject *__pyx_int_3;\nstatic PyObject *__pyx_int_200;\nstatic PyObject *__pyx_int_204;\nstatic PyObject *__pyx_int_1000;\nstatic PyObject *__pyx_tuple__6;\nstatic PyObject *__pyx_tuple__8;\nstatic PyObject *__pyx_tuple__10;\nstatic PyObject *__pyx_tuple__13;\nstatic PyObject *__pyx_codeobj__7;\nstatic PyObject *__pyx_codeobj__9;\nstatic PyObject *__pyx_codeobj__11;\n/* Late includes */\n\n\n\n/* Python wrapper */\nstatic PyObject *__pyx_pw_6source_1print_ascii(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/\nstatic PyMethodDef __pyx_mdef_6source_1print_ascii = {"print_ascii", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_6source_1print_ascii, METH_VARARGS|METH_KEYWORDS, 0};\nstatic PyObject *__pyx_pw_6source_1print_ascii(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {\n  PyObject *__pyx_v_art = 0;\n  PyObject *__pyx_v_color = 0;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  PyObject *__pyx_r = 0;\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("print_ascii (wrapper)", 0);\n  {\n    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_art,&__pyx_n_s_color,0};\n    PyObject* values[2] = {0,0};\n    if (unlikely(__pyx_kwds)) {\n      Py_ssize_t kw_args;\n      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);\n      switch (pos_args) {\n        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n        CYTHON_FALLTHROUGH;\n        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n        CYTHON_FALLTHROUGH;\n        case  0: break;\n        default: goto __pyx_L5_argtuple_error;\n      }\n      kw_args = PyDict_Size(__pyx_kwds);\n      switch (pos_args) {\n        case  0:\n        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_art)) != 0)) kw_args--;\n        else goto __pyx_L5_argtuple_error;\n        CYTHON_FALLTHROUGH;\n        case  1:\n        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_color)) != 0)) kw_args--;\n        else {\n          __Pyx_RaiseArgtupleInvalid("print_ascii", 1, 2, 2, 1); __PYX_ERR(0, 22, __pyx_L3_error)\n        }\n      }\n      if (unlikely(kw_args > 0)) {\n        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "print_ascii") < 0)) __PYX_ERR(0, 22, __pyx_L3_error)\n      }\n    } else if (PyTuple_GET_SIZE(__pyx_args) != 2) {\n      goto __pyx_L5_argtuple_error;\n    } else {\n      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n    }\n    __pyx_v_art = values[0];\n    __pyx_v_color = values[1];\n  }\n  goto __pyx_L4_argument_unpacking_done;\n  __pyx_L5_argtuple_error:;\n  __Pyx_RaiseArgtupleInvalid("print_ascii", 1, 2, 2, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 22, __pyx_L3_error)\n  __pyx_L3_error:;\n  __Pyx_AddTraceback("source.print_ascii", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __Pyx_RefNannyFinishContext();\n  return NULL;\n  __pyx_L4_argument_unpacking_done:;\n  __pyx_r = __pyx_pf_6source_print_ascii(__pyx_self, __pyx_v_art, __pyx_v_color);\n\n  /* function exit code */\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\nstatic PyObject *__pyx_pf_6source_print_ascii(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_art, PyObject *__pyx_v_color) {\n  PyObject *__pyx_v_ascii_art = NULL;\n  PyObject *__pyx_r = NULL;\n  __Pyx_RefNannyDeclarations\n  PyObject *__pyx_t_1 = NULL;\n  PyObject *__pyx_t_2 = NULL;\n  PyObject *__pyx_t_3 = NULL;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  __Pyx_RefNannySetupContext("print_ascii", 0);\n\n  \n  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_pyfiglet); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 23, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_2);\n  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_figlet_format); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 23, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;\n  __pyx_t_2 = NULL;\n  if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_3))) {\n    __pyx_t_2 = PyMethod_GET_SELF(__pyx_t_3);\n    if (likely(__pyx_t_2)) {\n      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_3);\n      __Pyx_INCREF(__pyx_t_2);\n      __Pyx_INCREF(function);\n      __Pyx_DECREF_SET(__pyx_t_3, function);\n    }\n  }\n  __pyx_t_1 = (__pyx_t_2) ? __Pyx_PyObject_Call2Args(__pyx_t_3, __pyx_t_2, __pyx_v_art) : __Pyx_PyObject_CallOneArg(__pyx_t_3, __pyx_v_art);\n  __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;\n  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 23, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n  __pyx_v_ascii_art = __pyx_t_1;\n  __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = PyNumber_Add(__pyx_v_color, __pyx_v_ascii_art); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 24, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 24, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  __pyx_t_2 = PyNumber_Add(__pyx_t_1, __pyx_t_3); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 24, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_2);\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n  __pyx_t_3 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 24, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n\n  \n\n  /* function exit code */\n  __pyx_r = Py_None; __Pyx_INCREF(Py_None);\n  goto __pyx_L0;\n  __pyx_L1_error:;\n  __Pyx_XDECREF(__pyx_t_1);\n  __Pyx_XDECREF(__pyx_t_2);\n  __Pyx_XDECREF(__pyx_t_3);\n  __Pyx_AddTraceback("source.print_ascii", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __pyx_r = NULL;\n  __pyx_L0:;\n  __Pyx_XDECREF(__pyx_v_ascii_art);\n  __Pyx_XGIVEREF(__pyx_r);\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\n\n\n/* Python wrapper */\nstatic PyObject *__pyx_pw_6source_3get_access_token(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/\nstatic PyMethodDef __pyx_mdef_6source_3get_access_token = {"get_access_token", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_6source_3get_access_token, METH_VARARGS|METH_KEYWORDS, 0};\nstatic PyObject *__pyx_pw_6source_3get_access_token(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {\n  PyObject *__pyx_v_username = 0;\n  PyObject *__pyx_v_password = 0;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  PyObject *__pyx_r = 0;\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("get_access_token (wrapper)", 0);\n  {\n    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_username,&__pyx_n_s_password,0};\n    PyObject* values[2] = {0,0};\n    if (unlikely(__pyx_kwds)) {\n      Py_ssize_t kw_args;\n      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);\n      switch (pos_args) {\n        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n        CYTHON_FALLTHROUGH;\n        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n        CYTHON_FALLTHROUGH;\n        case  0: break;\n        default: goto __pyx_L5_argtuple_error;\n      }\n      kw_args = PyDict_Size(__pyx_kwds);\n      switch (pos_args) {\n        case  0:\n        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_username)) != 0)) kw_args--;\n        else goto __pyx_L5_argtuple_error;\n        CYTHON_FALLTHROUGH;\n        case  1:\n        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_password)) != 0)) kw_args--;\n        else {\n          __Pyx_RaiseArgtupleInvalid("get_access_token", 1, 2, 2, 1); __PYX_ERR(0, 27, __pyx_L3_error)\n        }\n      }\n      if (unlikely(kw_args > 0)) {\n        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "get_access_token") < 0)) __PYX_ERR(0, 27, __pyx_L3_error)\n      }\n    } else if (PyTuple_GET_SIZE(__pyx_args) != 2) {\n      goto __pyx_L5_argtuple_error;\n    } else {\n      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n    }\n    __pyx_v_username = values[0];\n    __pyx_v_password = values[1];\n  }\n  goto __pyx_L4_argument_unpacking_done;\n  __pyx_L5_argtuple_error:;\n  __Pyx_RaiseArgtupleInvalid("get_access_token", 1, 2, 2, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 27, __pyx_L3_error)\n  __pyx_L3_error:;\n  __Pyx_AddTraceback("source.get_access_token", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __Pyx_RefNannyFinishContext();\n  return NULL;\n  __pyx_L4_argument_unpacking_done:;\n  __pyx_r = __pyx_pf_6source_2get_access_token(__pyx_self, __pyx_v_username, __pyx_v_password);\n\n  /* function exit code */\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\nstatic PyObject *__pyx_pf_6source_2get_access_token(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_username, PyObject *__pyx_v_password) {\n  PyObject *__pyx_v_url_token = NULL;\n  PyObject *__pyx_v_data_token = NULL;\n  PyObject *__pyx_v_headers_token = NULL;\n  PyObject *__pyx_v_response_token = NULL;\n  PyObject *__pyx_r = NULL;\n  __Pyx_RefNannyDeclarations\n  PyObject *__pyx_t_1 = NULL;\n  PyObject *__pyx_t_2 = NULL;\n  PyObject *__pyx_t_3 = NULL;\n  PyObject *__pyx_t_4 = NULL;\n  int __pyx_t_5;\n  int __pyx_t_6;\n  int __pyx_t_7;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  __Pyx_RefNannySetupContext("get_access_token", 0);\n\n  \n  __Pyx_INCREF(__pyx_kp_u_https_web_vodafone_com_eg_auth_r);\n  __pyx_v_url_token = __pyx_kp_u_https_web_vodafone_com_eg_auth_r;\n\n  \n  __pyx_t_1 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 30, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_username, __pyx_v_username) < 0) __PYX_ERR(0, 30, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_password, __pyx_v_password) < 0) __PYX_ERR(0, 30, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_grant_type, __pyx_n_u_password) < 0) __PYX_ERR(0, 30, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_client_secret, __pyx_kp_u_a2ec6fff_0b7f_4aa4_a733_96ceae5c) < 0) __PYX_ERR(0, 30, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_client_id, __pyx_kp_u_my_vodafone_app) < 0) __PYX_ERR(0, 30, __pyx_L1_error)\n  __pyx_v_data_token = ((PyObject*)__pyx_t_1);\n  __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_PyDict_NewPresized(9); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 37, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_Accept, __pyx_kp_u_application_json_text_plain) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_Connection, __pyx_kp_u_keep_alive) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_x_agent_operatingsystem, __pyx_kp_u_10_1_0_264C185) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_n_u_clientId, __pyx_n_u_AnaVodafoneAndroid) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_x_agent_device, __pyx_kp_u_HWDRA_MR) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_x_agent_version, __pyx_kp_u_2022_1_2_3) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_x_agent_build, __pyx_kp_u_500) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_Content_Type, __pyx_kp_u_application_x_www_form_urlencode) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_u_User_Agent, __pyx_kp_u_okhttp_4_9_1) < 0) __PYX_ERR(0, 37, __pyx_L1_error)\n  __pyx_v_headers_token = ((PyObject*)__pyx_t_1);\n  __pyx_t_1 = 0;\n\n  \n  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_requests); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 47, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_post); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 47, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_2);\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = PyTuple_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 47, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  __Pyx_INCREF(__pyx_v_url_token);\n  __Pyx_GIVEREF(__pyx_v_url_token);\n  PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_v_url_token);\n\n  \n  __pyx_t_3 = __Pyx_PyDict_NewPresized(2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 49, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  if (PyDict_SetItem(__pyx_t_3, __pyx_n_s_data, __pyx_v_data_token) < 0) __PYX_ERR(0, 49, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_t_3, __pyx_n_s_headers, __pyx_v_headers_token) < 0) __PYX_ERR(0, 49, __pyx_L1_error)\n\n  \n  __pyx_t_4 = __Pyx_PyObject_Call(__pyx_t_2, __pyx_t_1, __pyx_t_3); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 47, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_4);\n  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n  __pyx_v_response_token = __pyx_t_4;\n  __pyx_t_4 = 0;\n\n  \n  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_token, __pyx_n_s_status_code); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_4);\n  __pyx_t_3 = __Pyx_PyInt_EqObjC(__pyx_t_4, __pyx_int_200, 0xC8, 0); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n  if (__pyx_t_6) {\n  } else {\n    __pyx_t_5 = __pyx_t_6;\n    goto __pyx_L4_bool_binop_done;\n  }\n  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_token, __pyx_n_s_json); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_4);\n  __pyx_t_1 = NULL;\n  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {\n    __pyx_t_1 = PyMethod_GET_SELF(__pyx_t_4);\n    if (likely(__pyx_t_1)) {\n      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);\n      __Pyx_INCREF(__pyx_t_1);\n      __Pyx_INCREF(function);\n      __Pyx_DECREF_SET(__pyx_t_4, function);\n    }\n  }\n  __pyx_t_3 = (__pyx_t_1) ? __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_t_1) : __Pyx_PyObject_CallNoArg(__pyx_t_4);\n  __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;\n  if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_3);\n  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n  __pyx_t_6 = (__Pyx_PySequence_ContainsTF(__pyx_n_u_access_token, __pyx_t_3, Py_EQ)); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 51, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n  __pyx_t_7 = (__pyx_t_6 != 0);\n  __pyx_t_5 = __pyx_t_7;\n  __pyx_L4_bool_binop_done:;\n  if (__pyx_t_5) {\n\n    \n    __Pyx_XDECREF(__pyx_r);\n    __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_token, __pyx_n_s_json); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 52, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = NULL;\n    if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {\n      __pyx_t_1 = PyMethod_GET_SELF(__pyx_t_4);\n      if (likely(__pyx_t_1)) {\n        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);\n        __Pyx_INCREF(__pyx_t_1);\n        __Pyx_INCREF(function);\n        __Pyx_DECREF_SET(__pyx_t_4, function);\n      }\n    }\n    __pyx_t_3 = (__pyx_t_1) ? __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_t_1) : __Pyx_PyObject_CallNoArg(__pyx_t_4);\n    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;\n    if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 52, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_Dict_GetItem(__pyx_t_3, __pyx_n_u_access_token); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 52, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __pyx_r = __pyx_t_4;\n    __pyx_t_4 = 0;\n    goto __pyx_L0;\n\n    \n  }\n\n  \n  /*else*/ {\n    __Pyx_XDECREF(__pyx_r);\n    __pyx_r = Py_None; __Pyx_INCREF(Py_None);\n    goto __pyx_L0;\n  }\n\n  \n\n  /* function exit code */\n  __pyx_L1_error:;\n  __Pyx_XDECREF(__pyx_t_1);\n  __Pyx_XDECREF(__pyx_t_2);\n  __Pyx_XDECREF(__pyx_t_3);\n  __Pyx_XDECREF(__pyx_t_4);\n  __Pyx_AddTraceback("source.get_access_token", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __pyx_r = NULL;\n  __pyx_L0:;\n  __Pyx_XDECREF(__pyx_v_url_token);\n  __Pyx_XDECREF(__pyx_v_data_token);\n  __Pyx_XDECREF(__pyx_v_headers_token);\n  __Pyx_XDECREF(__pyx_v_response_token);\n  __Pyx_XGIVEREF(__pyx_r);\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\n\n\n/* Python wrapper */\nstatic PyObject *__pyx_pw_6source_5subscribe_to_promotion(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/\nstatic PyMethodDef __pyx_mdef_6source_5subscribe_to_promotion = {"subscribe_to_promotion", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_6source_5subscribe_to_promotion, METH_VARARGS|METH_KEYWORDS, 0};\nstatic PyObject *__pyx_pw_6source_5subscribe_to_promotion(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {\n  PyObject *__pyx_v_username = 0;\n  PyObject *__pyx_v_token = 0;\n  CYTHON_UNUSED PyObject *__pyx_v_attempt = 0;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  PyObject *__pyx_r = 0;\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("subscribe_to_promotion (wrapper)", 0);\n  {\n    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_username,&__pyx_n_s_token,&__pyx_n_s_attempt,0};\n    PyObject* values[3] = {0,0,0};\n    if (unlikely(__pyx_kwds)) {\n      Py_ssize_t kw_args;\n      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);\n      switch (pos_args) {\n        case  3: values[2] = PyTuple_GET_ITEM(__pyx_args, 2);\n        CYTHON_FALLTHROUGH;\n        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n        CYTHON_FALLTHROUGH;\n        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n        CYTHON_FALLTHROUGH;\n        case  0: break;\n        default: goto __pyx_L5_argtuple_error;\n      }\n      kw_args = PyDict_Size(__pyx_kwds);\n      switch (pos_args) {\n        case  0:\n        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_username)) != 0)) kw_args--;\n        else goto __pyx_L5_argtuple_error;\n        CYTHON_FALLTHROUGH;\n        case  1:\n        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_token)) != 0)) kw_args--;\n        else {\n          __Pyx_RaiseArgtupleInvalid("subscribe_to_promotion", 1, 3, 3, 1); __PYX_ERR(0, 57, __pyx_L3_error)\n        }\n        CYTHON_FALLTHROUGH;\n        case  2:\n        if (likely((values[2] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_attempt)) != 0)) kw_args--;\n        else {\n          __Pyx_RaiseArgtupleInvalid("subscribe_to_promotion", 1, 3, 3, 2); __PYX_ERR(0, 57, __pyx_L3_error)\n        }\n      }\n      if (unlikely(kw_args > 0)) {\n        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "subscribe_to_promotion") < 0)) __PYX_ERR(0, 57, __pyx_L3_error)\n      }\n    } else if (PyTuple_GET_SIZE(__pyx_args) != 3) {\n      goto __pyx_L5_argtuple_error;\n    } else {\n      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);\n      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);\n      values[2] = PyTuple_GET_ITEM(__pyx_args, 2);\n    }\n    __pyx_v_username = values[0];\n    __pyx_v_token = values[1];\n    __pyx_v_attempt = values[2];\n  }\n  goto __pyx_L4_argument_unpacking_done;\n  __pyx_L5_argtuple_error:;\n  __Pyx_RaiseArgtupleInvalid("subscribe_to_promotion", 1, 3, 3, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 57, __pyx_L3_error)\n  __pyx_L3_error:;\n  __Pyx_AddTraceback("source.subscribe_to_promotion", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __Pyx_RefNannyFinishContext();\n  return NULL;\n  __pyx_L4_argument_unpacking_done:;\n  __pyx_r = __pyx_pf_6source_4subscribe_to_promotion(__pyx_self, __pyx_v_username, __pyx_v_token, __pyx_v_attempt);\n\n  /* function exit code */\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\nstatic PyObject *__pyx_pf_6source_4subscribe_to_promotion(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_username, PyObject *__pyx_v_token, CYTHON_UNUSED PyObject *__pyx_v_attempt) {\n  PyObject *__pyx_v_url_product = NULL;\n  PyObject *__pyx_v_headers_product = NULL;\n  PyObject *__pyx_v_response_product = NULL;\n  PyObject *__pyx_v_match_id = NULL;\n  PyObject *__pyx_v_match_value = NULL;\n  PyObject *__pyx_v_product_id = NULL;\n  PyObject *__pyx_v_value = NULL;\n  PyObject *__pyx_v_url_subscription = NULL;\n  PyObject *__pyx_v_headers_subscription = NULL;\n  PyObject *__pyx_v_data_subscription = NULL;\n  PyObject *__pyx_v_response_subscription = NULL;\n  PyObject *__pyx_r = NULL;\n  __Pyx_RefNannyDeclarations\n  PyObject *__pyx_t_1 = NULL;\n  PyObject *__pyx_t_2 = NULL;\n  PyObject *__pyx_t_3 = NULL;\n  PyObject *__pyx_t_4 = NULL;\n  PyObject *__pyx_t_5 = NULL;\n  PyObject *__pyx_t_6 = NULL;\n  PyObject *__pyx_t_7 = NULL;\n  int __pyx_t_8;\n  int __pyx_t_9;\n  PyObject *__pyx_t_10 = NULL;\n  int __pyx_t_11;\n  Py_ssize_t __pyx_t_12;\n  Py_UCS4 __pyx_t_13;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  __Pyx_RefNannySetupContext("subscribe_to_promotion", 0);\n\n  \n  {\n    __Pyx_PyThreadState_declare\n    __Pyx_PyThreadState_assign\n    __Pyx_ExceptionSave(&__pyx_t_1, &__pyx_t_2, &__pyx_t_3);\n    __Pyx_XGOTREF(__pyx_t_1);\n    __Pyx_XGOTREF(__pyx_t_2);\n    __Pyx_XGOTREF(__pyx_t_3);\n    /*try:*/ {\n\n      \n      __Pyx_INCREF(__pyx_kp_u_https_web_vodafone_com_eg_servic);\n      __pyx_v_url_product = __pyx_kp_u_https_web_vodafone_com_eg_servic;\n\n      \n      __pyx_t_4 = __Pyx_PyDict_NewPresized(18); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 61, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_Host, __pyx_kp_u_web_vodafone_com_eg) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_Connection, __pyx_kp_u_keep_alive) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_sec_ch_ua, __pyx_kp_u_Chromium_v_124_Android_WebView) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n\n      \n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_msisdn, __pyx_v_username) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Accept_Language, __pyx_n_u_AR) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_sec_ch_ua_mobile, __pyx_kp_u_1) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n\n      \n      __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_v_token, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 67, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_5);\n      __pyx_t_6 = __Pyx_PyUnicode_Concat(__pyx_kp_u_Bearer, __pyx_t_5); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 67, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_6);\n      __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_Authorization, __pyx_t_6) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_User_Agent, __pyx_n_u_vodafoneandroid) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Content_Type, __pyx_kp_u_application_json) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_Accept, __pyx_kp_u_application_json) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_clientId, __pyx_n_u_WebsiteConsumer) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_channel, __pyx_n_u_APP_PORTAL) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_sec_ch_ua_platform, __pyx_kp_u_Android) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Sec_Fetch_Site, __pyx_kp_u_same_origin) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Sec_Fetch_Mode, __pyx_n_u_cors) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Sec_Fetch_Dest, __pyx_n_u_empty) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_Referer, __pyx_kp_u_https_web_vodafone_com_eg_portal) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      if (PyDict_SetItem(__pyx_t_4, __pyx_kp_u_Accept_Encoding, __pyx_kp_u_gzip_deflate_br_zstd) < 0) __PYX_ERR(0, 61, __pyx_L3_error)\n      __pyx_v_headers_product = ((PyObject*)__pyx_t_4);\n      __pyx_t_4 = 0;\n\n      \n      __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_requests); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 79, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __pyx_t_6 = __Pyx_PyObject_GetAttrStr(__pyx_t_4, __pyx_n_s_get); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 79, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_6);\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      __pyx_t_4 = PyTuple_New(1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 79, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __Pyx_INCREF(__pyx_v_url_product);\n      __Pyx_GIVEREF(__pyx_v_url_product);\n      PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_v_url_product);\n      __pyx_t_5 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 79, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_5);\n      if (PyDict_SetItem(__pyx_t_5, __pyx_n_s_headers, __pyx_v_headers_product) < 0) __PYX_ERR(0, 79, __pyx_L3_error)\n      __pyx_t_7 = __Pyx_PyObject_Call(__pyx_t_6, __pyx_t_4, __pyx_t_5); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 79, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n      __pyx_v_response_product = __pyx_t_7;\n      __pyx_t_7 = 0;\n\n      \n      __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_product, __pyx_n_s_status_code); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 80, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __pyx_t_5 = __Pyx_PyInt_EqObjC(__pyx_t_7, __pyx_int_200, 0xC8, 0); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 80, __pyx_L3_error)\n      __Pyx_GOTREF(__pyx_t_5);\n      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n      __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_t_5); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 80, __pyx_L3_error)\n      __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n      if (__pyx_t_8) {\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_re); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 81, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_7, __pyx_n_s_search); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 81, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_product, __pyx_n_s_text); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 81, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __pyx_t_6 = NULL;\n        __pyx_t_9 = 0;\n        if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_4))) {\n          __pyx_t_6 = PyMethod_GET_SELF(__pyx_t_4);\n          if (likely(__pyx_t_6)) {\n            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);\n            __Pyx_INCREF(__pyx_t_6);\n            __Pyx_INCREF(function);\n            __Pyx_DECREF_SET(__pyx_t_4, function);\n            __pyx_t_9 = 1;\n          }\n        }\n        #if CYTHON_FAST_PYCALL\n        if (PyFunction_Check(__pyx_t_4)) {\n          PyObject *__pyx_temp[3] = {__pyx_t_6, __pyx_kp_u_id, __pyx_t_7};\n          __pyx_t_5 = __Pyx_PyFunction_FastCall(__pyx_t_4, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 81, __pyx_L3_error)\n          __Pyx_XDECREF(__pyx_t_6); __pyx_t_6 = 0;\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        } else\n        #endif\n        #if CYTHON_FAST_PYCCALL\n        if (__Pyx_PyFastCFunction_Check(__pyx_t_4)) {\n          PyObject *__pyx_temp[3] = {__pyx_t_6, __pyx_kp_u_id, __pyx_t_7};\n          __pyx_t_5 = __Pyx_PyCFunction_FastCall(__pyx_t_4, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 81, __pyx_L3_error)\n          __Pyx_XDECREF(__pyx_t_6); __pyx_t_6 = 0;\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        } else\n        #endif\n        {\n          __pyx_t_10 = PyTuple_New(2+__pyx_t_9); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 81, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          if (__pyx_t_6) {\n            __Pyx_GIVEREF(__pyx_t_6); PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_6); __pyx_t_6 = NULL;\n          }\n          __Pyx_INCREF(__pyx_kp_u_id);\n          __Pyx_GIVEREF(__pyx_kp_u_id);\n          PyTuple_SET_ITEM(__pyx_t_10, 0+__pyx_t_9, __pyx_kp_u_id);\n          __Pyx_GIVEREF(__pyx_t_7);\n          PyTuple_SET_ITEM(__pyx_t_10, 1+__pyx_t_9, __pyx_t_7);\n          __pyx_t_7 = 0;\n          __pyx_t_5 = __Pyx_PyObject_Call(__pyx_t_4, __pyx_t_10, NULL); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 81, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n        }\n        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n        __pyx_v_match_id = __pyx_t_5;\n        __pyx_t_5 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_re); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 82, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __pyx_t_10 = __Pyx_PyObject_GetAttrStr(__pyx_t_4, __pyx_n_s_search); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 82, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_10);\n        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n        \n        __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_product, __pyx_n_s_text); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 84, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __pyx_t_7 = NULL;\n        __pyx_t_9 = 0;\n        if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_10))) {\n          __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_10);\n          if (likely(__pyx_t_7)) {\n            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_10);\n            __Pyx_INCREF(__pyx_t_7);\n            __Pyx_INCREF(function);\n            __Pyx_DECREF_SET(__pyx_t_10, function);\n            __pyx_t_9 = 1;\n          }\n        }\n        #if CYTHON_FAST_PYCALL\n        if (PyFunction_Check(__pyx_t_10)) {\n          PyObject *__pyx_temp[3] = {__pyx_t_7, __pyx_kp_u_name_ShortScript_Assignment_val, __pyx_t_4};\n          __pyx_t_5 = __Pyx_PyFunction_FastCall(__pyx_t_10, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 82, __pyx_L3_error)\n          __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n        } else\n        #endif\n        #if CYTHON_FAST_PYCCALL\n        if (__Pyx_PyFastCFunction_Check(__pyx_t_10)) {\n          PyObject *__pyx_temp[3] = {__pyx_t_7, __pyx_kp_u_name_ShortScript_Assignment_val, __pyx_t_4};\n          __pyx_t_5 = __Pyx_PyCFunction_FastCall(__pyx_t_10, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 82, __pyx_L3_error)\n          __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n        } else\n        #endif\n        {\n          __pyx_t_6 = PyTuple_New(2+__pyx_t_9); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 82, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_6);\n          if (__pyx_t_7) {\n            __Pyx_GIVEREF(__pyx_t_7); PyTuple_SET_ITEM(__pyx_t_6, 0, __pyx_t_7); __pyx_t_7 = NULL;\n          }\n          __Pyx_INCREF(__pyx_kp_u_name_ShortScript_Assignment_val);\n          __Pyx_GIVEREF(__pyx_kp_u_name_ShortScript_Assignment_val);\n          PyTuple_SET_ITEM(__pyx_t_6, 0+__pyx_t_9, __pyx_kp_u_name_ShortScript_Assignment_val);\n          __Pyx_GIVEREF(__pyx_t_4);\n          PyTuple_SET_ITEM(__pyx_t_6, 1+__pyx_t_9, __pyx_t_4);\n          __pyx_t_4 = 0;\n          __pyx_t_5 = __Pyx_PyObject_Call(__pyx_t_10, __pyx_t_6, NULL); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 82, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n        }\n        __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n        __pyx_v_match_value = __pyx_t_5;\n        __pyx_t_5 = 0;\n\n        \n        __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_v_match_id); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 85, __pyx_L3_error)\n        if (__pyx_t_8) {\n\n          \n          __pyx_t_10 = __Pyx_PyObject_GetAttrStr(__pyx_v_match_id, __pyx_n_s_group); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 86, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __pyx_t_6 = NULL;\n          if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_10))) {\n            __pyx_t_6 = PyMethod_GET_SELF(__pyx_t_10);\n            if (likely(__pyx_t_6)) {\n              PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_10);\n              __Pyx_INCREF(__pyx_t_6);\n              __Pyx_INCREF(function);\n              __Pyx_DECREF_SET(__pyx_t_10, function);\n            }\n          }\n          __pyx_t_5 = (__pyx_t_6) ? __Pyx_PyObject_Call2Args(__pyx_t_10, __pyx_t_6, __pyx_int_1) : __Pyx_PyObject_CallOneArg(__pyx_t_10, __pyx_int_1);\n          __Pyx_XDECREF(__pyx_t_6); __pyx_t_6 = 0;\n          if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 86, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          __pyx_v_product_id = __pyx_t_5;\n          __pyx_t_5 = 0;\n\n          \n          __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_v_match_value); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 87, __pyx_L3_error)\n          if (__pyx_t_8) {\n            __pyx_t_6 = __Pyx_PyObject_GetAttrStr(__pyx_v_match_value, __pyx_n_s_group); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 87, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_6);\n            __pyx_t_4 = NULL;\n            if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_6))) {\n              __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_6);\n              if (likely(__pyx_t_4)) {\n                PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_6);\n                __Pyx_INCREF(__pyx_t_4);\n                __Pyx_INCREF(function);\n                __Pyx_DECREF_SET(__pyx_t_6, function);\n              }\n            }\n            __pyx_t_10 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_6, __pyx_t_4, __pyx_int_1) : __Pyx_PyObject_CallOneArg(__pyx_t_6, __pyx_int_1);\n            __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;\n            if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 87, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_10);\n            __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n            __pyx_t_5 = __pyx_t_10;\n            __pyx_t_10 = 0;\n          } else {\n            __Pyx_INCREF(Py_None);\n            __pyx_t_5 = Py_None;\n          }\n          __pyx_v_value = __pyx_t_5;\n          __pyx_t_5 = 0;\n\n          \n          __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_v_product_id, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 88, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __pyx_t_10 = __Pyx_PyUnicode_Concat(__pyx_kp_u_https_web_vodafone_com_eg_servic_2, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 88, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          __pyx_v_url_subscription = ((PyObject*)__pyx_t_10);\n          __pyx_t_10 = 0;\n\n          \n          __pyx_t_10 = __Pyx_PyDict_NewPresized(18); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 90, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_Host, __pyx_kp_u_web_vodafone_com_eg) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_Connection, __pyx_kp_u_keep_alive) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_sec_ch_ua, __pyx_kp_u_Chromium_v_124_Android_WebView) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n\n          \n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_msisdn, __pyx_v_username) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Accept_Language, __pyx_n_u_AR) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_sec_ch_ua_mobile, __pyx_kp_u_1) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n\n          \n          __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_v_token, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 96, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __pyx_t_6 = __Pyx_PyUnicode_Concat(__pyx_kp_u_Bearer, __pyx_t_5); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 96, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_6);\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_Authorization, __pyx_t_6) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_User_Agent, __pyx_n_u_vodafoneandroid) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Content_Type, __pyx_kp_u_application_json) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_Accept, __pyx_kp_u_application_json) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_clientId, __pyx_n_u_WebsiteConsumer) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_channel, __pyx_n_u_APP_PORTAL) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_sec_ch_ua_platform, __pyx_kp_u_Android) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Sec_Fetch_Site, __pyx_kp_u_same_origin) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Sec_Fetch_Mode, __pyx_n_u_cors) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Sec_Fetch_Dest, __pyx_n_u_empty) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n\n          \n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_Referer, __pyx_v_url_subscription) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_Accept_Encoding, __pyx_kp_u_gzip_deflate_br_zstd) < 0) __PYX_ERR(0, 90, __pyx_L3_error)\n          __pyx_v_headers_subscription = ((PyObject*)__pyx_t_10);\n          __pyx_t_10 = 0;\n\n          \n          __pyx_t_10 = __Pyx_PyDict_NewPresized(3); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 109, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          if (PyDict_SetItem(__pyx_t_10, __pyx_kp_u_type, __pyx_n_u_Promo) < 0) __PYX_ERR(0, 109, __pyx_L3_error)\n\n          \n          __pyx_t_6 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 110, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_6);\n          if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_id_2, __pyx_kp_u_5) < 0) __PYX_ERR(0, 110, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_channel, __pyx_t_6) < 0) __PYX_ERR(0, 109, __pyx_L3_error)\n          __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n\n          \n          __pyx_t_6 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 111, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_6);\n          if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_type_2, __pyx_n_u_rechargeProgram) < 0) __PYX_ERR(0, 111, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_context, __pyx_t_6) < 0) __PYX_ERR(0, 109, __pyx_L3_error)\n          __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n          __pyx_v_data_subscription = ((PyObject*)__pyx_t_10);\n          __pyx_t_10 = 0;\n\n          \n          __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_requests); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 113, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __pyx_t_6 = __Pyx_PyObject_GetAttrStr(__pyx_t_10, __pyx_n_s_patch); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 113, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_6);\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n          \n          __pyx_t_10 = PyTuple_New(1); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 113, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __Pyx_INCREF(__pyx_v_url_subscription);\n          __Pyx_GIVEREF(__pyx_v_url_subscription);\n          PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_v_url_subscription);\n          __pyx_t_5 = __Pyx_PyDict_NewPresized(2); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 114, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          if (PyDict_SetItem(__pyx_t_5, __pyx_n_s_headers, __pyx_v_headers_subscription) < 0) __PYX_ERR(0, 114, __pyx_L3_error)\n          if (PyDict_SetItem(__pyx_t_5, __pyx_n_s_json, __pyx_v_data_subscription) < 0) __PYX_ERR(0, 114, __pyx_L3_error)\n\n          \n          __pyx_t_4 = __Pyx_PyObject_Call(__pyx_t_6, __pyx_t_10, __pyx_t_5); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 113, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_4);\n          __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          __pyx_v_response_subscription = __pyx_t_4;\n          __pyx_t_4 = 0;\n\n          \n          __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_subscription, __pyx_n_s_status_code); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 115, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_4);\n          __pyx_t_5 = __Pyx_PyInt_EqObjC(__pyx_t_4, __pyx_int_204, 0xCC, 0); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 115, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n          __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_t_5); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 115, __pyx_L3_error)\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          if (__pyx_t_8) {\n\n            \n            __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_response_subscription, __pyx_n_s_text); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 116, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_4);\n            __pyx_t_10 = __Pyx_PyObject_GetAttrStr(__pyx_t_4, __pyx_n_s_strip); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 116, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_10);\n            __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n            __pyx_t_4 = NULL;\n            if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_10))) {\n              __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_10);\n              if (likely(__pyx_t_4)) {\n                PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_10);\n                __Pyx_INCREF(__pyx_t_4);\n                __Pyx_INCREF(function);\n                __Pyx_DECREF_SET(__pyx_t_10, function);\n              }\n            }\n            __pyx_t_5 = (__pyx_t_4) ? __Pyx_PyObject_CallOneArg(__pyx_t_10, __pyx_t_4) : __Pyx_PyObject_CallNoArg(__pyx_t_10);\n            __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;\n            if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 116, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_5);\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n            __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_t_5); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 116, __pyx_L3_error)\n            __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n            __pyx_t_11 = ((!__pyx_t_8) != 0);\n            if (__pyx_t_11) {\n\n              \n              __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_separator1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 117, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_5);\n              __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 117, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n              __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n              \n              __pyx_t_11 = __Pyx_PyObject_IsTrue(__pyx_v_value); if (unlikely(__pyx_t_11 < 0)) __PYX_ERR(0, 118, __pyx_L3_error)\n              if (__pyx_t_11) {\n\n                \n                __pyx_t_10 = PyTuple_New(5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_10);\n                __pyx_t_12 = 0;\n                __pyx_t_13 = 127;\n                __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_green_color); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_5, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_4);\n                __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n                __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n                __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n                __Pyx_GIVEREF(__pyx_t_4);\n                PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_4);\n                __pyx_t_4 = 0;\n                __Pyx_INCREF(__pyx_kp_u_);\n                __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n                __pyx_t_12 += 9;\n                __Pyx_GIVEREF(__pyx_kp_u_);\n                PyTuple_SET_ITEM(__pyx_t_10, 1, __pyx_kp_u_);\n                __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_v_value, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_4);\n                __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n                __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n                __Pyx_GIVEREF(__pyx_t_4);\n                PyTuple_SET_ITEM(__pyx_t_10, 2, __pyx_t_4);\n                __pyx_t_4 = 0;\n                __Pyx_INCREF(__pyx_kp_u__2);\n                __pyx_t_13 = (1114111 > __pyx_t_13) ? 1114111 : __pyx_t_13;\n                __pyx_t_12 += 3;\n                __Pyx_GIVEREF(__pyx_kp_u__2);\n                PyTuple_SET_ITEM(__pyx_t_10, 3, __pyx_kp_u__2);\n                __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_4);\n                __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n                __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) : __pyx_t_13;\n                __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_5);\n                __Pyx_GIVEREF(__pyx_t_5);\n                PyTuple_SET_ITEM(__pyx_t_10, 4, __pyx_t_5);\n                __pyx_t_5 = 0;\n                __pyx_t_5 = __Pyx_PyUnicode_Join(__pyx_t_10, 5, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 120, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n                \n                __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 119, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_10);\n                __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n                __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n                \n                goto __pyx_L13;\n              }\n\n              \n              /*else*/ {\n\n                \n                __pyx_t_10 = PyTuple_New(3); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_10);\n                __pyx_t_12 = 0;\n                __pyx_t_13 = 127;\n                __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_yellow_color); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_5, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_4);\n                __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n                __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n                __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n                __Pyx_GIVEREF(__pyx_t_4);\n                PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_4);\n                __pyx_t_4 = 0;\n                __Pyx_INCREF(__pyx_kp_u__3);\n                __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n                __pyx_t_12 += 27;\n                __Pyx_GIVEREF(__pyx_kp_u__3);\n                PyTuple_SET_ITEM(__pyx_t_10, 1, __pyx_kp_u__3);\n                __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_4);\n                __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n                __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) : __pyx_t_13;\n                __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_5);\n                __Pyx_GIVEREF(__pyx_t_5);\n                PyTuple_SET_ITEM(__pyx_t_10, 2, __pyx_t_5);\n                __pyx_t_5 = 0;\n                __pyx_t_5 = __Pyx_PyUnicode_Join(__pyx_t_10, 3, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 123, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_5);\n                __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n                \n                __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 122, __pyx_L3_error)\n                __Pyx_GOTREF(__pyx_t_10);\n                __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n                __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n              }\n              __pyx_L13:;\n\n              \n              __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_separator1); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 124, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_10); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 124, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_5);\n              __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n              __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n\n              \n              goto __pyx_L12;\n            }\n\n            \n            /*else*/ {\n              __pyx_t_5 = PyTuple_New(3); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_5);\n              __pyx_t_12 = 0;\n              __pyx_t_13 = 127;\n              __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_red_color); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_10, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_4);\n              __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n              __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n              __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n              __Pyx_GIVEREF(__pyx_t_4);\n              PyTuple_SET_ITEM(__pyx_t_5, 0, __pyx_t_4);\n              __pyx_t_4 = 0;\n              __Pyx_INCREF(__pyx_kp_u__4);\n              __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n              __pyx_t_12 += 21;\n              __Pyx_GIVEREF(__pyx_kp_u__4);\n              PyTuple_SET_ITEM(__pyx_t_5, 1, __pyx_kp_u__4);\n              __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_4);\n              __pyx_t_10 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n              __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_10) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_10) : __pyx_t_13;\n              __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_10);\n              __Pyx_GIVEREF(__pyx_t_10);\n              PyTuple_SET_ITEM(__pyx_t_5, 2, __pyx_t_10);\n              __pyx_t_10 = 0;\n              __pyx_t_10 = __Pyx_PyUnicode_Join(__pyx_t_5, 3, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n              __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_10); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 126, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_5);\n              __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n              __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n\n              \n              __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_separator1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 127, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_5);\n              __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 127, __pyx_L3_error)\n              __Pyx_GOTREF(__pyx_t_10);\n              __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n              __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n            }\n            __pyx_L12:;\n\n            \n            goto __pyx_L11;\n          }\n\n          \n          /*else*/ {\n            __pyx_t_10 = PyTuple_New(3); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_10);\n            __pyx_t_12 = 0;\n            __pyx_t_13 = 127;\n            __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_red_color); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_5);\n            __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_5, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_4);\n            __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n            __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n            __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n            __Pyx_GIVEREF(__pyx_t_4);\n            PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_4);\n            __pyx_t_4 = 0;\n            __Pyx_INCREF(__pyx_kp_u__4);\n            __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n            __pyx_t_12 += 21;\n            __Pyx_GIVEREF(__pyx_kp_u__4);\n            PyTuple_SET_ITEM(__pyx_t_10, 1, __pyx_kp_u__4);\n            __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_4);\n            __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_5);\n            __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n            __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) : __pyx_t_13;\n            __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_5);\n            __Pyx_GIVEREF(__pyx_t_5);\n            PyTuple_SET_ITEM(__pyx_t_10, 2, __pyx_t_5);\n            __pyx_t_5 = 0;\n            __pyx_t_5 = __Pyx_PyUnicode_Join(__pyx_t_10, 3, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_5);\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n            __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 129, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_10);\n            __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n            \n            __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_separator1); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 130, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_10);\n            __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_10); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 130, __pyx_L3_error)\n            __Pyx_GOTREF(__pyx_t_5);\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n            __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          }\n          __pyx_L11:;\n\n          \n          goto __pyx_L10;\n        }\n\n        \n        /*else*/ {\n          __pyx_t_5 = PyTuple_New(3); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __pyx_t_12 = 0;\n          __pyx_t_13 = 127;\n          __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_red_color); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_10, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_4);\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n          __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n          __Pyx_GIVEREF(__pyx_t_4);\n          PyTuple_SET_ITEM(__pyx_t_5, 0, __pyx_t_4);\n          __pyx_t_4 = 0;\n          __Pyx_INCREF(__pyx_kp_u__5);\n          __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n          __pyx_t_12 += 15;\n          __Pyx_GIVEREF(__pyx_kp_u__5);\n          PyTuple_SET_ITEM(__pyx_t_5, 1, __pyx_kp_u__5);\n          __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_4);\n          __pyx_t_10 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n          __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_10) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_10) : __pyx_t_13;\n          __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_10);\n          __Pyx_GIVEREF(__pyx_t_10);\n          PyTuple_SET_ITEM(__pyx_t_5, 2, __pyx_t_10);\n          __pyx_t_10 = 0;\n          __pyx_t_10 = __Pyx_PyUnicode_Join(__pyx_t_5, 3, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_10); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 132, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n\n          \n          __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_separator1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 133, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_5);\n          __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 133, __pyx_L3_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n          __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n        }\n        __pyx_L10:;\n\n        \n        goto __pyx_L9;\n      }\n\n      \n      /*else*/ {\n        __pyx_t_10 = PyTuple_New(3); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_10);\n        __pyx_t_12 = 0;\n        __pyx_t_13 = 127;\n        __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_red_color); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_5);\n        __pyx_t_4 = __Pyx_PyObject_FormatSimple(__pyx_t_5, __pyx_empty_unicode); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n        __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_4) : __pyx_t_13;\n        __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_4);\n        __Pyx_GIVEREF(__pyx_t_4);\n        PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_4);\n        __pyx_t_4 = 0;\n        __Pyx_INCREF(__pyx_kp_u__5);\n        __pyx_t_13 = (65535 > __pyx_t_13) ? 65535 : __pyx_t_13;\n        __pyx_t_12 += 15;\n        __Pyx_GIVEREF(__pyx_kp_u__5);\n        PyTuple_SET_ITEM(__pyx_t_10, 1, __pyx_kp_u__5);\n        __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __pyx_t_5 = __Pyx_PyObject_FormatSimple(__pyx_t_4, __pyx_empty_unicode); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_5);\n        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n        __pyx_t_13 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) > __pyx_t_13) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_5) : __pyx_t_13;\n        __pyx_t_12 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_5);\n        __Pyx_GIVEREF(__pyx_t_5);\n        PyTuple_SET_ITEM(__pyx_t_10, 2, __pyx_t_5);\n        __pyx_t_5 = 0;\n        __pyx_t_5 = __Pyx_PyUnicode_Join(__pyx_t_10, 3, __pyx_t_12, __pyx_t_13); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_5);\n        __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n        __pyx_t_10 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 135, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_10);\n        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n        __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_separator1); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 136, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_10);\n        __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_10); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 136, __pyx_L3_error)\n        __Pyx_GOTREF(__pyx_t_5);\n        __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;\n      }\n      __pyx_L9:;\n\n      \n    }\n    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;\n    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;\n    goto __pyx_L8_try_end;\n    __pyx_L3_error:;\n    __Pyx_XDECREF(__pyx_t_10); __pyx_t_10 = 0;\n    __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;\n    __Pyx_XDECREF(__pyx_t_6); __pyx_t_6 = 0;\n    __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;\n\n    \n    __pyx_t_9 = __Pyx_PyErr_ExceptionMatches(((PyObject *)(&((PyTypeObject*)PyExc_Exception)[0])));\n    if (__pyx_t_9) {\n      __Pyx_ErrRestore(0,0,0);\n      goto __pyx_L4_exception_handled;\n    }\n    goto __pyx_L5_except_error;\n    __pyx_L5_except_error:;\n\n    \n    __Pyx_XGIVEREF(__pyx_t_1);\n    __Pyx_XGIVEREF(__pyx_t_2);\n    __Pyx_XGIVEREF(__pyx_t_3);\n    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);\n    goto __pyx_L1_error;\n    __pyx_L4_exception_handled:;\n    __Pyx_XGIVEREF(__pyx_t_1);\n    __Pyx_XGIVEREF(__pyx_t_2);\n    __Pyx_XGIVEREF(__pyx_t_3);\n    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);\n    __pyx_L8_try_end:;\n  }\n\n  \n\n  /* function exit code */\n  __pyx_r = Py_None; __Pyx_INCREF(Py_None);\n  goto __pyx_L0;\n  __pyx_L1_error:;\n  __Pyx_XDECREF(__pyx_t_4);\n  __Pyx_XDECREF(__pyx_t_5);\n  __Pyx_XDECREF(__pyx_t_6);\n  __Pyx_XDECREF(__pyx_t_7);\n  __Pyx_XDECREF(__pyx_t_10);\n  __Pyx_AddTraceback("source.subscribe_to_promotion", __pyx_clineno, __pyx_lineno, __pyx_filename);\n  __pyx_r = NULL;\n  __pyx_L0:;\n  __Pyx_XDECREF(__pyx_v_url_product);\n  __Pyx_XDECREF(__pyx_v_headers_product);\n  __Pyx_XDECREF(__pyx_v_response_product);\n  __Pyx_XDECREF(__pyx_v_match_id);\n  __Pyx_XDECREF(__pyx_v_match_value);\n  __Pyx_XDECREF(__pyx_v_product_id);\n  __Pyx_XDECREF(__pyx_v_value);\n  __Pyx_XDECREF(__pyx_v_url_subscription);\n  __Pyx_XDECREF(__pyx_v_headers_subscription);\n  __Pyx_XDECREF(__pyx_v_data_subscription);\n  __Pyx_XDECREF(__pyx_v_response_subscription);\n  __Pyx_XGIVEREF(__pyx_r);\n  __Pyx_RefNannyFinishContext();\n  return __pyx_r;\n}\n\nstatic PyMethodDef __pyx_methods[] = {\n  {0, 0, 0, 0}\n};\n\n#if PY_MAJOR_VERSION >= 3\n#if CYTHON_PEP489_MULTI_PHASE_INIT\nstatic PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/\nstatic int __pyx_pymod_exec_source(PyObject* module); /*proto*/\nstatic PyModuleDef_Slot __pyx_moduledef_slots[] = {\n  {Py_mod_create, (void*)__pyx_pymod_create},\n  {Py_mod_exec, (void*)__pyx_pymod_exec_source},\n  {0, NULL}\n};\n#endif\n\nstatic struct PyModuleDef __pyx_moduledef = {\n    PyModuleDef_HEAD_INIT,\n    "source",\n    0, /* m_doc */\n  #if CYTHON_PEP489_MULTI_PHASE_INIT\n    0, /* m_size */\n  #else\n    -1, /* m_size */\n  #endif\n    __pyx_methods /* m_methods */,\n  #if CYTHON_PEP489_MULTI_PHASE_INIT\n    __pyx_moduledef_slots, /* m_slots */\n  #else\n    NULL, /* m_reload */\n  #endif\n    NULL, /* m_traverse */\n    NULL, /* m_clear */\n    NULL /* m_free */\n};\n#endif\n#ifndef CYTHON_SMALL_CODE\n#if defined(__clang__)\n    #define CYTHON_SMALL_CODE\n#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))\n    #define CYTHON_SMALL_CODE __attribute__((cold))\n#else\n    #define CYTHON_SMALL_CODE\n#endif\n#endif\n\nstatic __Pyx_StringTabEntry __pyx_string_tab[] = {\n  {&__pyx_kp_u_, __pyx_k_, sizeof(__pyx_k_), 0, 1, 0, 0},\n  {&__pyx_kp_u_0m, __pyx_k_0m, sizeof(__pyx_k_0m), 0, 1, 0, 0},\n  {&__pyx_kp_u_1, __pyx_k_1, sizeof(__pyx_k_1), 0, 1, 0, 0},\n  {&__pyx_kp_u_10_1_0_264C185, __pyx_k_10_1_0_264C185, sizeof(__pyx_k_10_1_0_264C185), 0, 1, 0, 0},\n  {&__pyx_kp_u_2022_1_2_3, __pyx_k_2022_1_2_3, sizeof(__pyx_k_2022_1_2_3), 0, 1, 0, 0},\n  {&__pyx_kp_u_2_32m, __pyx_k_2_32m, sizeof(__pyx_k_2_32m), 0, 1, 0, 0},\n  {&__pyx_kp_u_2_36m, __pyx_k_2_36m, sizeof(__pyx_k_2_36m), 0, 1, 0, 0},\n  {&__pyx_kp_u_5, __pyx_k_5, sizeof(__pyx_k_5), 0, 1, 0, 0},\n  {&__pyx_kp_u_500, __pyx_k_500, sizeof(__pyx_k_500), 0, 1, 0, 0},\n  {&__pyx_kp_u_91m, __pyx_k_91m, sizeof(__pyx_k_91m), 0, 1, 0, 0},\n  {&__pyx_kp_u_92m, __pyx_k_92m, sizeof(__pyx_k_92m), 0, 1, 0, 0},\n  {&__pyx_kp_u_93m, __pyx_k_93m, sizeof(__pyx_k_93m), 0, 1, 0, 0},\n  {&__pyx_kp_u_94m, __pyx_k_94m, sizeof(__pyx_k_94m), 0, 1, 0, 0},\n  {&__pyx_kp_u_95m, __pyx_k_95m, sizeof(__pyx_k_95m), 0, 1, 0, 0},\n  {&__pyx_kp_u_95m_2, __pyx_k_95m_2, sizeof(__pyx_k_95m_2), 0, 1, 0, 0},\n  {&__pyx_kp_u_96m, __pyx_k_96m, sizeof(__pyx_k_96m), 0, 1, 0, 0},\n  {&__pyx_kp_u_96m_2, __pyx_k_96m_2, sizeof(__pyx_k_96m_2), 0, 1, 0, 0},\n  {&__pyx_n_u_APP_PORTAL, __pyx_k_APP_PORTAL, sizeof(__pyx_k_APP_PORTAL), 0, 1, 0, 1},\n  {&__pyx_n_u_AR, __pyx_k_AR, sizeof(__pyx_k_AR), 0, 1, 0, 1},\n  {&__pyx_kp_u_Aba_Al_Hassan, __pyx_k_Aba_Al_Hassan, sizeof(__pyx_k_Aba_Al_Hassan), 0, 1, 0, 0},\n  {&__pyx_n_u_Accept, __pyx_k_Accept, sizeof(__pyx_k_Accept), 0, 1, 0, 1},\n  {&__pyx_kp_u_Accept_Encoding, __pyx_k_Accept_Encoding, sizeof(__pyx_k_Accept_Encoding), 0, 1, 0, 0},\n  {&__pyx_kp_u_Accept_Language, __pyx_k_Accept_Language, sizeof(__pyx_k_Accept_Language), 0, 1, 0, 0},\n  {&__pyx_n_u_AnaVodafoneAndroid, __pyx_k_AnaVodafoneAndroid, sizeof(__pyx_k_AnaVodafoneAndroid), 0, 1, 0, 1},\n  {&__pyx_kp_u_Android, __pyx_k_Android, sizeof(__pyx_k_Android), 0, 1, 0, 0},\n  {&__pyx_n_u_Authorization, __pyx_k_Authorization, sizeof(__pyx_k_Authorization), 0, 1, 0, 1},\n  {&__pyx_kp_u_Bearer, __pyx_k_Bearer, sizeof(__pyx_k_Bearer), 0, 1, 0, 0},\n  {&__pyx_kp_u_Chromium_v_124_Android_WebView, __pyx_k_Chromium_v_124_Android_WebView, sizeof(__pyx_k_Chromium_v_124_Android_WebView), 0, 1, 0, 0},\n  {&__pyx_n_u_Connection, __pyx_k_Connection, sizeof(__pyx_k_Connection), 0, 1, 0, 1},\n  {&__pyx_kp_u_Content_Type, __pyx_k_Content_Type, sizeof(__pyx_k_Content_Type), 0, 1, 0, 0},\n  {&__pyx_kp_u_ELJoNet208, __pyx_k_ELJoNet208, sizeof(__pyx_k_ELJoNet208), 0, 1, 0, 0},\n  {&__pyx_kp_u_El_Jo_NeT, __pyx_k_El_Jo_NeT, sizeof(__pyx_k_El_Jo_NeT), 0, 1, 0, 0},\n  {&__pyx_kp_u_El_Sultan, __pyx_k_El_Sultan, sizeof(__pyx_k_El_Sultan), 0, 1, 0, 0},\n  {&__pyx_kp_u_El_Sultan_2, __pyx_k_El_Sultan_2, sizeof(__pyx_k_El_Sultan_2), 0, 1, 0, 0},\n  {&__pyx_kp_u_Enter_a_number, __pyx_k_Enter_a_number, sizeof(__pyx_k_Enter_a_number), 0, 1, 0, 0},\n  {&__pyx_kp_u_Enter_a_password, __pyx_k_Enter_a_password, sizeof(__pyx_k_Enter_a_password), 0, 1, 0, 0},\n  {&__pyx_n_s_F, __pyx_k_F, sizeof(__pyx_k_F), 0, 0, 1, 1},\n  {&__pyx_kp_u_HWDRA_MR, __pyx_k_HWDRA_MR, sizeof(__pyx_k_HWDRA_MR), 0, 1, 0, 0},\n  {&__pyx_n_u_Host, __pyx_k_Host, sizeof(__pyx_k_Host), 0, 1, 0, 1},\n  {&__pyx_n_u_Promo, __pyx_k_Promo, sizeof(__pyx_k_Promo), 0, 1, 0, 1},\n  {&__pyx_n_u_Referer, __pyx_k_Referer, sizeof(__pyx_k_Referer), 0, 1, 0, 1},\n  {&__pyx_kp_u_Sec_Fetch_Dest, __pyx_k_Sec_Fetch_Dest, sizeof(__pyx_k_Sec_Fetch_Dest), 0, 1, 0, 0},\n  {&__pyx_kp_u_Sec_Fetch_Mode, __pyx_k_Sec_Fetch_Mode, sizeof(__pyx_k_Sec_Fetch_Mode), 0, 1, 0, 0},\n  {&__pyx_kp_u_Sec_Fetch_Site, __pyx_k_Sec_Fetch_Site, sizeof(__pyx_k_Sec_Fetch_Site), 0, 1, 0, 0},\n  {&__pyx_kp_u_Telegram, __pyx_k_Telegram, sizeof(__pyx_k_Telegram), 0, 1, 0, 0},\n  {&__pyx_n_s_Thread, __pyx_k_Thread, sizeof(__pyx_k_Thread), 0, 0, 1, 1},\n  {&__pyx_kp_u_User_Agent, __pyx_k_User_Agent, sizeof(__pyx_k_User_Agent), 0, 1, 0, 0},\n  {&__pyx_n_u_WebsiteConsumer, __pyx_k_WebsiteConsumer, sizeof(__pyx_k_WebsiteConsumer), 0, 1, 0, 1},\n  {&__pyx_kp_u__12, __pyx_k__12, sizeof(__pyx_k__12), 0, 1, 0, 0},\n  {&__pyx_kp_u__14, __pyx_k__14, sizeof(__pyx_k__14), 0, 1, 0, 0},\n  {&__pyx_kp_u__15, __pyx_k__15, sizeof(__pyx_k__15), 0, 1, 0, 0},\n  {&__pyx_kp_u__2, __pyx_k__2, sizeof(__pyx_k__2), 0, 1, 0, 0},\n  {&__pyx_kp_u__3, __pyx_k__3, sizeof(__pyx_k__3), 0, 1, 0, 0},\n  {&__pyx_kp_u__4, __pyx_k__4, sizeof(__pyx_k__4), 0, 1, 0, 0},\n  {&__pyx_kp_u__5, __pyx_k__5, sizeof(__pyx_k__5), 0, 1, 0, 0},\n  {&__pyx_kp_u_a2ec6fff_0b7f_4aa4_a733_96ceae5c, __pyx_k_a2ec6fff_0b7f_4aa4_a733_96ceae5c, sizeof(__pyx_k_a2ec6fff_0b7f_4aa4_a733_96ceae5c), 0, 1, 0, 0},\n  {&__pyx_n_u_access_token, __pyx_k_access_token, sizeof(__pyx_k_access_token), 0, 1, 0, 1},\n  {&__pyx_n_s_append, __pyx_k_append, sizeof(__pyx_k_append), 0, 0, 1, 1},\n  {&__pyx_kp_u_application_json, __pyx_k_application_json, sizeof(__pyx_k_application_json), 0, 1, 0, 0},\n  {&__pyx_kp_u_application_json_text_plain, __pyx_k_application_json_text_plain, sizeof(__pyx_k_application_json_text_plain), 0, 1, 0, 0},\n  {&__pyx_kp_u_application_x_www_form_urlencode, __pyx_k_application_x_www_form_urlencode, sizeof(__pyx_k_application_x_www_form_urlencode), 0, 1, 0, 0},\n  {&__pyx_n_s_args, __pyx_k_args, sizeof(__pyx_k_args), 0, 0, 1, 1},\n  {&__pyx_n_s_art, __pyx_k_art, sizeof(__pyx_k_art), 0, 0, 1, 1},\n  {&__pyx_n_s_ascii_art, __pyx_k_ascii_art, sizeof(__pyx_k_ascii_art), 0, 0, 1, 1},\n  {&__pyx_n_s_attempt, __pyx_k_attempt, sizeof(__pyx_k_attempt), 0, 0, 1, 1},\n  {&__pyx_n_s_attempts, __pyx_k_attempts, sizeof(__pyx_k_attempts), 0, 0, 1, 1},\n  {&__pyx_n_u_channel, __pyx_k_channel, sizeof(__pyx_k_channel), 0, 1, 0, 1},\n  {&__pyx_n_u_clientId, __pyx_k_clientId, sizeof(__pyx_k_clientId), 0, 1, 0, 1},\n  {&__pyx_n_u_client_id, __pyx_k_client_id, sizeof(__pyx_k_client_id), 0, 1, 0, 1},\n  {&__pyx_n_u_client_secret, __pyx_k_client_secret, sizeof(__pyx_k_client_secret), 0, 1, 0, 1},\n  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},\n  {&__pyx_n_s_color, __pyx_k_color, sizeof(__pyx_k_color), 0, 0, 1, 1},\n  {&__pyx_n_u_context, __pyx_k_context, sizeof(__pyx_k_context), 0, 1, 0, 1},\n  {&__pyx_n_u_cors, __pyx_k_cors, sizeof(__pyx_k_cors), 0, 1, 0, 1},\n  {&__pyx_n_s_data, __pyx_k_data, sizeof(__pyx_k_data), 0, 0, 1, 1},\n  {&__pyx_n_s_data_subscription, __pyx_k_data_subscription, sizeof(__pyx_k_data_subscription), 0, 0, 1, 1},\n  {&__pyx_n_s_data_token, __pyx_k_data_token, sizeof(__pyx_k_data_token), 0, 0, 1, 1},\n  {&__pyx_n_u_empty, __pyx_k_empty, sizeof(__pyx_k_empty), 0, 1, 0, 1},\n  {&__pyx_n_s_figlet_format, __pyx_k_figlet_format, sizeof(__pyx_k_figlet_format), 0, 0, 1, 1},\n  {&__pyx_n_s_get, __pyx_k_get, sizeof(__pyx_k_get), 0, 0, 1, 1},\n  {&__pyx_n_s_get_access_token, __pyx_k_get_access_token, sizeof(__pyx_k_get_access_token), 0, 0, 1, 1},\n  {&__pyx_n_u_grant_type, __pyx_k_grant_type, sizeof(__pyx_k_grant_type), 0, 1, 0, 1},\n  {&__pyx_n_s_green_color, __pyx_k_green_color, sizeof(__pyx_k_green_color), 0, 0, 1, 1},\n  {&__pyx_n_s_group, __pyx_k_group, sizeof(__pyx_k_group), 0, 0, 1, 1},\n  {&__pyx_kp_u_gzip_deflate_br_zstd, __pyx_k_gzip_deflate_br_zstd, sizeof(__pyx_k_gzip_deflate_br_zstd), 0, 1, 0, 0},\n  {&__pyx_n_s_headers, __pyx_k_headers, sizeof(__pyx_k_headers), 0, 0, 1, 1},\n  {&__pyx_n_s_headers_product, __pyx_k_headers_product, sizeof(__pyx_k_headers_product), 0, 0, 1, 1},\n  {&__pyx_n_s_headers_subscription, __pyx_k_headers_subscription, sizeof(__pyx_k_headers_subscription), 0, 0, 1, 1},\n  {&__pyx_n_s_headers_token, __pyx_k_headers_token, sizeof(__pyx_k_headers_token), 0, 0, 1, 1},\n  {&__pyx_kp_u_https_web_vodafone_com_eg_auth_r, __pyx_k_https_web_vodafone_com_eg_auth_r, sizeof(__pyx_k_https_web_vodafone_com_eg_auth_r), 0, 1, 0, 0},\n  {&__pyx_kp_u_https_web_vodafone_com_eg_portal, __pyx_k_https_web_vodafone_com_eg_portal, sizeof(__pyx_k_https_web_vodafone_com_eg_portal), 0, 1, 0, 0},\n  {&__pyx_kp_u_https_web_vodafone_com_eg_servic, __pyx_k_https_web_vodafone_com_eg_servic, sizeof(__pyx_k_https_web_vodafone_com_eg_servic), 0, 1, 0, 0},\n  {&__pyx_kp_u_https_web_vodafone_com_eg_servic_2, __pyx_k_https_web_vodafone_com_eg_servic_2, sizeof(__pyx_k_https_web_vodafone_com_eg_servic_2), 0, 1, 0, 0},\n  {&__pyx_kp_u_id, __pyx_k_id, sizeof(__pyx_k_id), 0, 1, 0, 0},\n  {&__pyx_n_u_id_2, __pyx_k_id_2, sizeof(__pyx_k_id_2), 0, 1, 0, 1},\n  {&__pyx_n_s_import, __pyx_k_import, sizeof(__pyx_k_import), 0, 0, 1, 1},\n  {&__pyx_n_s_input, __pyx_k_input, sizeof(__pyx_k_input), 0, 0, 1, 1},\n  {&__pyx_n_s_join, __pyx_k_join, sizeof(__pyx_k_join), 0, 0, 1, 1},\n  {&__pyx_n_s_json, __pyx_k_json, sizeof(__pyx_k_json), 0, 0, 1, 1},\n  {&__pyx_kp_u_keep_alive, __pyx_k_keep_alive, sizeof(__pyx_k_keep_alive), 0, 1, 0, 0},\n  {&__pyx_n_s_light_blue_color, __pyx_k_light_blue_color, sizeof(__pyx_k_light_blue_color), 0, 0, 1, 1},\n  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},\n  {&__pyx_n_u_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 1, 0, 1},\n  {&__pyx_n_s_match_id, __pyx_k_match_id, sizeof(__pyx_k_match_id), 0, 0, 1, 1},\n  {&__pyx_n_s_match_value, __pyx_k_match_value, sizeof(__pyx_k_match_value), 0, 0, 1, 1},\n  {&__pyx_n_u_msisdn, __pyx_k_msisdn, sizeof(__pyx_k_msisdn), 0, 1, 0, 1},\n  {&__pyx_kp_u_my_vodafone_app, __pyx_k_my_vodafone_app, sizeof(__pyx_k_my_vodafone_app), 0, 1, 0, 0},\n  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},\n  {&__pyx_kp_u_name_ShortScript_Assignment_val, __pyx_k_name_ShortScript_Assignment_val, sizeof(__pyx_k_name_ShortScript_Assignment_val), 0, 1, 0, 0},\n  {&__pyx_kp_u_okhttp_4_9_1, __pyx_k_okhttp_4_9_1, sizeof(__pyx_k_okhttp_4_9_1), 0, 1, 0, 0},\n  {&__pyx_n_s_password, __pyx_k_password, sizeof(__pyx_k_password), 0, 0, 1, 1},\n  {&__pyx_n_u_password, __pyx_k_password, sizeof(__pyx_k_password), 0, 1, 0, 1},\n  {&__pyx_n_s_patch, __pyx_k_patch, sizeof(__pyx_k_patch), 0, 0, 1, 1},\n  {&__pyx_n_s_post, __pyx_k_post, sizeof(__pyx_k_post), 0, 0, 1, 1},\n  {&__pyx_n_s_print, __pyx_k_print, sizeof(__pyx_k_print), 0, 0, 1, 1},\n  {&__pyx_n_s_print_ascii, __pyx_k_print_ascii, sizeof(__pyx_k_print_ascii), 0, 0, 1, 1},\n  {&__pyx_n_s_product_id, __pyx_k_product_id, sizeof(__pyx_k_product_id), 0, 0, 1, 1},\n  {&__pyx_n_s_pyfiglet, __pyx_k_pyfiglet, sizeof(__pyx_k_pyfiglet), 0, 0, 1, 1},\n  {&__pyx_n_s_range, __pyx_k_range, sizeof(__pyx_k_range), 0, 0, 1, 1},\n  {&__pyx_n_s_re, __pyx_k_re, sizeof(__pyx_k_re), 0, 0, 1, 1},\n  {&__pyx_n_u_rechargeProgram, __pyx_k_rechargeProgram, sizeof(__pyx_k_rechargeProgram), 0, 1, 0, 1},\n  {&__pyx_n_s_red_color, __pyx_k_red_color, sizeof(__pyx_k_red_color), 0, 0, 1, 1},\n  {&__pyx_n_s_requests, __pyx_k_requests, sizeof(__pyx_k_requests), 0, 0, 1, 1},\n  {&__pyx_n_s_reset_color, __pyx_k_reset_color, sizeof(__pyx_k_reset_color), 0, 0, 1, 1},\n  {&__pyx_n_s_response_product, __pyx_k_response_product, sizeof(__pyx_k_response_product), 0, 0, 1, 1},\n  {&__pyx_n_s_response_subscription, __pyx_k_response_subscription, sizeof(__pyx_k_response_subscription), 0, 0, 1, 1},\n  {&__pyx_n_s_response_token, __pyx_k_response_token, sizeof(__pyx_k_response_token), 0, 0, 1, 1},\n  {&__pyx_kp_u_same_origin, __pyx_k_same_origin, sizeof(__pyx_k_same_origin), 0, 1, 0, 0},\n  {&__pyx_n_s_search, __pyx_k_search, sizeof(__pyx_k_search), 0, 0, 1, 1},\n  {&__pyx_kp_u_sec_ch_ua, __pyx_k_sec_ch_ua, sizeof(__pyx_k_sec_ch_ua), 0, 1, 0, 0},\n  {&__pyx_kp_u_sec_ch_ua_mobile, __pyx_k_sec_ch_ua_mobile, sizeof(__pyx_k_sec_ch_ua_mobile), 0, 1, 0, 0},\n  {&__pyx_kp_u_sec_ch_ua_platform, __pyx_k_sec_ch_ua_platform, sizeof(__pyx_k_sec_ch_ua_platform), 0, 1, 0, 0},\n  {&__pyx_n_s_separator1, __pyx_k_separator1, sizeof(__pyx_k_separator1), 0, 0, 1, 1},\n  {&__pyx_n_s_separator2, __pyx_k_separator2, sizeof(__pyx_k_separator2), 0, 0, 1, 1},\n  {&__pyx_n_s_separator3, __pyx_k_separator3, sizeof(__pyx_k_separator3), 0, 0, 1, 1},\n  {&__pyx_n_s_source, __pyx_k_source, sizeof(__pyx_k_source), 0, 0, 1, 1},\n  {&__pyx_kp_s_source_py, __pyx_k_source_py, sizeof(__pyx_k_source_py), 0, 0, 1, 0},\n  {&__pyx_n_s_start, __pyx_k_start, sizeof(__pyx_k_start), 0, 0, 1, 1},\n  {&__pyx_n_s_status_code, __pyx_k_status_code, sizeof(__pyx_k_status_code), 0, 0, 1, 1},\n  {&__pyx_n_s_strip, __pyx_k_strip, sizeof(__pyx_k_strip), 0, 0, 1, 1},\n  {&__pyx_n_s_subscribe_to_promotion, __pyx_k_subscribe_to_promotion, sizeof(__pyx_k_subscribe_to_promotion), 0, 0, 1, 1},\n  {&__pyx_n_s_target, __pyx_k_target, sizeof(__pyx_k_target), 0, 0, 1, 1},\n  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},\n  {&__pyx_n_s_text, __pyx_k_text, sizeof(__pyx_k_text), 0, 0, 1, 1},\n  {&__pyx_n_s_thread, __pyx_k_thread, sizeof(__pyx_k_thread), 0, 0, 1, 1},\n  {&__pyx_n_s_threading, __pyx_k_threading, sizeof(__pyx_k_threading), 0, 0, 1, 1},\n  {&__pyx_n_s_threads, __pyx_k_threads, sizeof(__pyx_k_threads), 0, 0, 1, 1},\n  {&__pyx_n_s_token, __pyx_k_token, sizeof(__pyx_k_token), 0, 0, 1, 1},\n  {&__pyx_kp_u_type, __pyx_k_type, sizeof(__pyx_k_type), 0, 1, 0, 0},\n  {&__pyx_n_u_type_2, __pyx_k_type_2, sizeof(__pyx_k_type_2), 0, 1, 0, 1},\n  {&__pyx_n_s_url_product, __pyx_k_url_product, sizeof(__pyx_k_url_product), 0, 0, 1, 1},\n  {&__pyx_n_s_url_subscription, __pyx_k_url_subscription, sizeof(__pyx_k_url_subscription), 0, 0, 1, 1},\n  {&__pyx_n_s_url_token, __pyx_k_url_token, sizeof(__pyx_k_url_token), 0, 0, 1, 1},\n  {&__pyx_n_s_username, __pyx_k_username, sizeof(__pyx_k_username), 0, 0, 1, 1},\n  {&__pyx_n_u_username, __pyx_k_username, sizeof(__pyx_k_username), 0, 1, 0, 1},\n  {&__pyx_n_s_value, __pyx_k_value, sizeof(__pyx_k_value), 0, 0, 1, 1},\n  {&__pyx_n_u_vodafoneandroid, __pyx_k_vodafoneandroid, sizeof(__pyx_k_vodafoneandroid), 0, 1, 0, 1},\n  {&__pyx_kp_u_web_vodafone_com_eg, __pyx_k_web_vodafone_com_eg, sizeof(__pyx_k_web_vodafone_com_eg), 0, 1, 0, 0},\n  {&__pyx_kp_u_x_agent_build, __pyx_k_x_agent_build, sizeof(__pyx_k_x_agent_build), 0, 1, 0, 0},\n  {&__pyx_kp_u_x_agent_device, __pyx_k_x_agent_device, sizeof(__pyx_k_x_agent_device), 0, 1, 0, 0},\n  {&__pyx_kp_u_x_agent_operatingsystem, __pyx_k_x_agent_operatingsystem, sizeof(__pyx_k_x_agent_operatingsystem), 0, 1, 0, 0},\n  {&__pyx_kp_u_x_agent_version, __pyx_k_x_agent_version, sizeof(__pyx_k_x_agent_version), 0, 1, 0, 0},\n  {&__pyx_n_s_yellow_color, __pyx_k_yellow_color, sizeof(__pyx_k_yellow_color), 0, 0, 1, 1},\n  {0, 0, 0, 0, 0, 0, 0}\n};\nstatic CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {\n  __pyx_builtin_print = __Pyx_GetBuiltinName(__pyx_n_s_print); if (!__pyx_builtin_print) __PYX_ERR(0, 144, __pyx_L1_error)\n  __pyx_builtin_input = __Pyx_GetBuiltinName(__pyx_n_s_input); if (!__pyx_builtin_input) __PYX_ERR(0, 153, __pyx_L1_error)\n  __pyx_builtin_range = __Pyx_GetBuiltinName(__pyx_n_s_range); if (!__pyx_builtin_range) __PYX_ERR(0, 163, __pyx_L1_error)\n  return 0;\n  __pyx_L1_error:;\n  return -1;\n}\n\nstatic CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);\n\n  \n  __pyx_tuple__6 = PyTuple_Pack(3, __pyx_n_s_art, __pyx_n_s_color, __pyx_n_s_ascii_art); if (unlikely(!__pyx_tuple__6)) __PYX_ERR(0, 22, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_tuple__6);\n  __Pyx_GIVEREF(__pyx_tuple__6);\n  __pyx_codeobj__7 = (PyObject*)__Pyx_PyCode_New(2, 0, 3, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__6, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_source_py, __pyx_n_s_print_ascii, 22, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__7)) __PYX_ERR(0, 22, __pyx_L1_error)\n\n  \n  __pyx_tuple__8 = PyTuple_Pack(6, __pyx_n_s_username, __pyx_n_s_password, __pyx_n_s_url_token, __pyx_n_s_data_token, __pyx_n_s_headers_token, __pyx_n_s_response_token); if (unlikely(!__pyx_tuple__8)) __PYX_ERR(0, 27, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_tuple__8);\n  __Pyx_GIVEREF(__pyx_tuple__8);\n  __pyx_codeobj__9 = (PyObject*)__Pyx_PyCode_New(2, 0, 6, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__8, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_source_py, __pyx_n_s_get_access_token, 27, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__9)) __PYX_ERR(0, 27, __pyx_L1_error)\n\n  \n  __pyx_tuple__10 = PyTuple_Pack(14, __pyx_n_s_username, __pyx_n_s_token, __pyx_n_s_attempt, __pyx_n_s_url_product, __pyx_n_s_headers_product, __pyx_n_s_response_product, __pyx_n_s_match_id, __pyx_n_s_match_value, __pyx_n_s_product_id, __pyx_n_s_value, __pyx_n_s_url_subscription, __pyx_n_s_headers_subscription, __pyx_n_s_data_subscription, __pyx_n_s_response_subscription); if (unlikely(!__pyx_tuple__10)) __PYX_ERR(0, 57, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_tuple__10);\n  __Pyx_GIVEREF(__pyx_tuple__10);\n  __pyx_codeobj__11 = (PyObject*)__Pyx_PyCode_New(3, 0, 14, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__10, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_source_py, __pyx_n_s_subscribe_to_promotion, 57, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__11)) __PYX_ERR(0, 57, __pyx_L1_error)\n\n  \n  __pyx_tuple__13 = PyTuple_Pack(1, __pyx_kp_u__12); if (unlikely(!__pyx_tuple__13)) __PYX_ERR(0, 146, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_tuple__13);\n  __Pyx_GIVEREF(__pyx_tuple__13);\n  __Pyx_RefNannyFinishContext();\n  return 0;\n  __pyx_L1_error:;\n  __Pyx_RefNannyFinishContext();\n  return -1;\n}\n\nstatic CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {\n  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_int_1 = PyInt_FromLong(1); if (unlikely(!__pyx_int_1)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_int_3 = PyInt_FromLong(3); if (unlikely(!__pyx_int_3)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_int_200 = PyInt_FromLong(200); if (unlikely(!__pyx_int_200)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_int_204 = PyInt_FromLong(204); if (unlikely(!__pyx_int_204)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_int_1000 = PyInt_FromLong(1000); if (unlikely(!__pyx_int_1000)) __PYX_ERR(0, 4, __pyx_L1_error)\n  return 0;\n  __pyx_L1_error:;\n  return -1;\n}\n\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/\nstatic CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/\n\nstatic int __Pyx_modinit_global_init_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);\n  /*--- Global init code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_variable_export_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);\n  /*--- Variable export code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_function_export_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);\n  /*--- Function export code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_type_init_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);\n  /*--- Type init code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_type_import_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);\n  /*--- Type import code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_variable_import_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);\n  /*--- Variable import code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\nstatic int __Pyx_modinit_function_import_code(void) {\n  __Pyx_RefNannyDeclarations\n  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);\n  /*--- Function import code ---*/\n  __Pyx_RefNannyFinishContext();\n  return 0;\n}\n\n\n#ifndef CYTHON_NO_PYINIT_EXPORT\n#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC\n#elif PY_MAJOR_VERSION < 3\n#ifdef __cplusplus\n#define __Pyx_PyMODINIT_FUNC extern "C" void\n#else\n#define __Pyx_PyMODINIT_FUNC void\n#endif\n#else\n#ifdef __cplusplus\n#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *\n#else\n#define __Pyx_PyMODINIT_FUNC PyObject *\n#endif\n#endif\n\n\n#if PY_MAJOR_VERSION < 3\n__Pyx_PyMODINIT_FUNC initsource(void) CYTHON_SMALL_CODE; /*proto*/\n__Pyx_PyMODINIT_FUNC initsource(void)\n#else\n__Pyx_PyMODINIT_FUNC PyInit_source(void) CYTHON_SMALL_CODE; /*proto*/\n__Pyx_PyMODINIT_FUNC PyInit_source(void)\n#if CYTHON_PEP489_MULTI_PHASE_INIT\n{\n  return PyModuleDef_Init(&__pyx_moduledef);\n}\nstatic CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {\n    #if PY_VERSION_HEX >= 0x030700A1\n    static PY_INT64_T main_interpreter_id = -1;\n    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);\n    if (main_interpreter_id == -1) {\n        main_interpreter_id = current_id;\n        return (unlikely(current_id == -1)) ? -1 : 0;\n    } else if (unlikely(main_interpreter_id != current_id))\n    #else\n    static PyInterpreterState *main_interpreter = NULL;\n    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;\n    if (!main_interpreter) {\n        main_interpreter = current_interpreter;\n    } else if (unlikely(main_interpreter != current_interpreter))\n    #endif\n    {\n        PyErr_SetString(\n            PyExc_ImportError,\n            "Interpreter change detected - this module can only be loaded into one interpreter per process.");\n        return -1;\n    }\n    return 0;\n}\nstatic CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {\n    PyObject *value = PyObject_GetAttrString(spec, from_name);\n    int result = 0;\n    if (likely(value)) {\n        if (allow_none || value != Py_None) {\n            result = PyDict_SetItemString(moddict, to_name, value);\n        }\n        Py_DECREF(value);\n    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {\n        PyErr_Clear();\n    } else {\n        result = -1;\n    }\n    return result;\n}\nstatic CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {\n    PyObject *module = NULL, *moddict, *modname;\n    if (__Pyx_check_single_interpreter())\n        return NULL;\n    if (__pyx_m)\n        return __Pyx_NewRef(__pyx_m);\n    modname = PyObject_GetAttrString(spec, "name");\n    if (unlikely(!modname)) goto bad;\n    module = PyModule_NewObject(modname);\n    Py_DECREF(modname);\n    if (unlikely(!module)) goto bad;\n    moddict = PyModule_GetDict(module);\n    if (unlikely(!moddict)) goto bad;\n    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;\n    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;\n    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;\n    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;\n    return module;\nbad:\n    Py_XDECREF(module);\n    return NULL;\n}\n\n\nstatic CYTHON_SMALL_CODE int __pyx_pymod_exec_source(PyObject *__pyx_pyinit_module)\n#endif\n#endif\n{\n  PyObject *__pyx_t_1 = NULL;\n  int __pyx_t_2;\n  PyObject *__pyx_t_3 = NULL;\n  PyObject *__pyx_t_4 = NULL;\n  Py_ssize_t __pyx_t_5;\n  Py_UCS4 __pyx_t_6;\n  PyObject *__pyx_t_7 = NULL;\n  int __pyx_t_8;\n  PyObject *(*__pyx_t_9)(PyObject *);\n  PyObject *__pyx_t_10 = NULL;\n  PyObject *__pyx_t_11 = NULL;\n  int __pyx_t_12;\n  PyObject *__pyx_t_13 = NULL;\n  Py_ssize_t __pyx_t_14;\n  int __pyx_t_15;\n  int __pyx_lineno = 0;\n  const char *__pyx_filename = NULL;\n  int __pyx_clineno = 0;\n  __Pyx_RefNannyDeclarations\n  #if CYTHON_PEP489_MULTI_PHASE_INIT\n  if (__pyx_m) {\n    if (__pyx_m == __pyx_pyinit_module) return 0;\n    PyErr_SetString(PyExc_RuntimeError, "Module \'source\' has already been imported. Re-initialisation is not supported.");\n    return -1;\n  }\n  #elif PY_MAJOR_VERSION >= 3\n  if (__pyx_m) return __Pyx_NewRef(__pyx_m);\n  #endif\n  #if CYTHON_REFNANNY\n__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");\nif (!__Pyx_RefNanny) {\n  PyErr_Clear();\n  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");\n  if (!__Pyx_RefNanny)\n      Py_FatalError("failed to import \'refnanny\' module");\n}\n#endif\n  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_source(void)", 0);\n  if (__Pyx_check_binary_version() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #ifdef __Pxy_PyFrame_Initialize_Offsets\n  __Pxy_PyFrame_Initialize_Offsets();\n  #endif\n  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(0, 4, __pyx_L1_error)\n  #ifdef __Pyx_CyFunction_USED\n  if (__pyx_CyFunction_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  #ifdef __Pyx_FusedFunction_USED\n  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  #ifdef __Pyx_Coroutine_USED\n  if (__pyx_Coroutine_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  #ifdef __Pyx_Generator_USED\n  if (__pyx_Generator_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  #ifdef __Pyx_AsyncGen_USED\n  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  #ifdef __Pyx_StopAsyncIteration_USED\n  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  /*--- Library function declarations ---*/\n  /*--- Threads initialization code ---*/\n  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS\n  PyEval_InitThreads();\n  #endif\n  /*--- Module creation code ---*/\n  #if CYTHON_PEP489_MULTI_PHASE_INIT\n  __pyx_m = __pyx_pyinit_module;\n  Py_INCREF(__pyx_m);\n  #else\n  #if PY_MAJOR_VERSION < 3\n  __pyx_m = Py_InitModule4("source", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);\n  #else\n  __pyx_m = PyModule_Create(&__pyx_moduledef);\n  #endif\n  if (unlikely(!__pyx_m)) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(0, 4, __pyx_L1_error)\n  Py_INCREF(__pyx_d);\n  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(0, 4, __pyx_L1_error)\n  Py_INCREF(__pyx_b);\n  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(0, 4, __pyx_L1_error)\n  Py_INCREF(__pyx_cython_runtime);\n  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  /*--- Initialize various global constants etc. ---*/\n  if (__Pyx_InitGlobals() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)\n  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n  if (__pyx_module_is_main_source) {\n    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  }\n  #if PY_MAJOR_VERSION >= 3\n  {\n    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(0, 4, __pyx_L1_error)\n    if (!PyDict_GetItemString(modules, "source")) {\n      if (unlikely(PyDict_SetItemString(modules, "source", __pyx_m) < 0)) __PYX_ERR(0, 4, __pyx_L1_error)\n    }\n  }\n  #endif\n  /*--- Builtin init code ---*/\n  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  /*--- Constants init code ---*/\n  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  /*--- Global type/function init code ---*/\n  (void)__Pyx_modinit_global_init_code();\n  (void)__Pyx_modinit_variable_export_code();\n  (void)__Pyx_modinit_function_export_code();\n  (void)__Pyx_modinit_type_init_code();\n  (void)__Pyx_modinit_type_import_code();\n  (void)__Pyx_modinit_variable_import_code();\n  (void)__Pyx_modinit_function_import_code();\n  /*--- Execution code ---*/\n  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)\n  if (__Pyx_patch_abc() < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  #endif\n\n  \n  __pyx_t_1 = __Pyx_Import(__pyx_n_s_requests, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_requests, __pyx_t_1) < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_Import(__pyx_n_s_json, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 5, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_json, __pyx_t_1) < 0) __PYX_ERR(0, 5, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_Import(__pyx_n_s_re, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 6, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_re, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_Import(__pyx_n_s_pyfiglet, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 7, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_pyfiglet, __pyx_t_1) < 0) __PYX_ERR(0, 7, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_Import(__pyx_n_s_threading, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 8, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_threading, __pyx_t_1) < 0) __PYX_ERR(0, 8, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_green_color, __pyx_kp_u_92m) < 0) __PYX_ERR(0, 10, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_light_blue_color, __pyx_kp_u_96m) < 0) __PYX_ERR(0, 11, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_red_color, __pyx_kp_u_91m) < 0) __PYX_ERR(0, 12, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_reset_color, __pyx_kp_u_0m) < 0) __PYX_ERR(0, 13, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_yellow_color, __pyx_kp_u_93m) < 0) __PYX_ERR(0, 14, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_green_color, __pyx_kp_u_2_32m) < 0) __PYX_ERR(0, 15, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_light_blue_color, __pyx_kp_u_2_36m) < 0) __PYX_ERR(0, 16, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_red_color, __pyx_kp_u_91m) < 0) __PYX_ERR(0, 17, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_reset_color, __pyx_kp_u_0m) < 0) __PYX_ERR(0, 18, __pyx_L1_error)\n\n  \n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_F, __pyx_kp_u_95m) < 0) __PYX_ERR(0, 19, __pyx_L1_error)\n\n  \n  __pyx_t_1 = __Pyx_CyFunction_New(&__pyx_mdef_6source_1print_ascii, 0, __pyx_n_s_print_ascii, NULL, __pyx_n_s_source, __pyx_d, ((PyObject *)__pyx_codeobj__7)); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 22, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_print_ascii, __pyx_t_1) < 0) __PYX_ERR(0, 22, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_CyFunction_New(&__pyx_mdef_6source_3get_access_token, 0, __pyx_n_s_get_access_token, NULL, __pyx_n_s_source, __pyx_d, ((PyObject *)__pyx_codeobj__9)); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 27, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_get_access_token, __pyx_t_1) < 0) __PYX_ERR(0, 27, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __pyx_t_1 = __Pyx_CyFunction_New(&__pyx_mdef_6source_5subscribe_to_promotion, 0, __pyx_n_s_subscribe_to_promotion, NULL, __pyx_n_s_source, __pyx_d, ((PyObject *)__pyx_codeobj__11)); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 57, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_subscribe_to_promotion, __pyx_t_1) < 0) __PYX_ERR(0, 57, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  \n  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_name); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 141, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  __pyx_t_2 = (__Pyx_PyUnicode_Equals(__pyx_t_1, __pyx_n_u_main, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 141, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n  if (__pyx_t_2) {\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_print_ascii); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 142, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_red_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 142, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_4 = PyTuple_New(2); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 142, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_INCREF(__pyx_kp_u_El_Jo_NeT);\n    __Pyx_GIVEREF(__pyx_kp_u_El_Jo_NeT);\n    PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_kp_u_El_Jo_NeT);\n    __Pyx_GIVEREF(__pyx_t_3);\n    PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_t_3);\n    __pyx_t_3 = 0;\n    __pyx_t_3 = __Pyx_PyObject_Call(__pyx_t_1, __pyx_t_4, NULL); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 142, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_print_ascii); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 143, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_green_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 143, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 143, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_INCREF(__pyx_kp_u_El_Sultan);\n    __Pyx_GIVEREF(__pyx_kp_u_El_Sultan);\n    PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_kp_u_El_Sultan);\n    __Pyx_GIVEREF(__pyx_t_4);\n    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_4);\n    __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_Call(__pyx_t_3, __pyx_t_1, NULL); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 143, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __pyx_t_4 = PyTuple_New(5); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_5 = 0;\n    __pyx_t_6 = 127;\n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_3 = __Pyx_PyObject_FormatSimple(__pyx_t_1, __pyx_empty_unicode); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_3);\n    __Pyx_GIVEREF(__pyx_t_3);\n    PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_t_3);\n    __pyx_t_3 = 0;\n    __Pyx_INCREF(__pyx_kp_u_El_Sultan_2);\n    __pyx_t_5 += 11;\n    __Pyx_GIVEREF(__pyx_kp_u_El_Sultan_2);\n    PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_kp_u_El_Sultan_2);\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_green_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_1 = __Pyx_PyObject_FormatSimple(__pyx_t_3, __pyx_empty_unicode); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_1);\n    __Pyx_GIVEREF(__pyx_t_1);\n    PyTuple_SET_ITEM(__pyx_t_4, 2, __pyx_t_1);\n    __pyx_t_1 = 0;\n    __Pyx_INCREF(__pyx_kp_u_Aba_Al_Hassan);\n    __pyx_t_5 += 14;\n    __Pyx_GIVEREF(__pyx_kp_u_Aba_Al_Hassan);\n    PyTuple_SET_ITEM(__pyx_t_4, 3, __pyx_kp_u_Aba_Al_Hassan);\n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_3 = __Pyx_PyObject_FormatSimple(__pyx_t_1, __pyx_empty_unicode); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_3);\n    __Pyx_GIVEREF(__pyx_t_3);\n    PyTuple_SET_ITEM(__pyx_t_4, 4, __pyx_t_3);\n    __pyx_t_3 = 0;\n    __pyx_t_3 = __Pyx_PyUnicode_Join(__pyx_t_4, 5, __pyx_t_5, __pyx_t_6); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_3); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 144, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __pyx_t_4 = PyTuple_New(5); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_5 = 0;\n    __pyx_t_6 = 127;\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_red_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_1 = __Pyx_PyObject_FormatSimple(__pyx_t_3, __pyx_empty_unicode); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_1);\n    __Pyx_GIVEREF(__pyx_t_1);\n    PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_t_1);\n    __pyx_t_1 = 0;\n    __Pyx_INCREF(__pyx_kp_u_Telegram);\n    __pyx_t_5 += 10;\n    __Pyx_GIVEREF(__pyx_kp_u_Telegram);\n    PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_kp_u_Telegram);\n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_3 = __Pyx_PyObject_FormatSimple(__pyx_t_1, __pyx_empty_unicode); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_3) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_3);\n    __Pyx_GIVEREF(__pyx_t_3);\n    PyTuple_SET_ITEM(__pyx_t_4, 2, __pyx_t_3);\n    __pyx_t_3 = 0;\n    __Pyx_INCREF(__pyx_kp_u_ELJoNet208);\n    __pyx_t_5 += 12;\n    __Pyx_GIVEREF(__pyx_kp_u_ELJoNet208);\n    PyTuple_SET_ITEM(__pyx_t_4, 3, __pyx_kp_u_ELJoNet208);\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_1 = __Pyx_PyObject_FormatSimple(__pyx_t_3, __pyx_empty_unicode); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) : __pyx_t_6;\n    __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_1);\n    __Pyx_GIVEREF(__pyx_t_1);\n    PyTuple_SET_ITEM(__pyx_t_4, 4, __pyx_t_1);\n    __pyx_t_1 = 0;\n    __pyx_t_1 = __Pyx_PyUnicode_Join(__pyx_t_4, 5, __pyx_t_5, __pyx_t_6); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 145, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __pyx_t_4 = __Pyx_PyObject_Call(__pyx_builtin_print, __pyx_tuple__13, NULL); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 146, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 147, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = PyNumber_Add(__pyx_kp_u_94m, __pyx_t_4); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 147, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_separator1, __pyx_t_1) < 0) __PYX_ERR(0, 147, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_4 = PyNumber_Add(__pyx_kp_u_95m_2, __pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 148, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_separator2, __pyx_t_4) < 0) __PYX_ERR(0, 148, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 149, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = PyNumber_Add(__pyx_kp_u_96m_2, __pyx_t_4); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 149, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_separator3, __pyx_t_1) < 0) __PYX_ERR(0, 149, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_separator1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 150, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 150, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_separator2); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 151, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_4); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 151, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_separator3); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 152, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 152, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_green_color); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_1 = PyNumber_Add(__pyx_t_4, __pyx_kp_u_Enter_a_number); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_F); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_3 = PyNumber_Add(__pyx_t_1, __pyx_t_4); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_input, __pyx_t_3); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_username, __pyx_t_4) < 0) __PYX_ERR(0, 153, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_separator2); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 154, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_3 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_4); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 154, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_green_color); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_4 = PyNumber_Add(__pyx_t_3, __pyx_kp_u_Enter_a_password); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_F); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_1 = PyNumber_Add(__pyx_t_4, __pyx_t_3); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __pyx_t_3 = __Pyx_PyObject_CallOneArg(__pyx_builtin_input, __pyx_t_1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_password, __pyx_t_3) < 0) __PYX_ERR(0, 155, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_separator2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 156, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __pyx_t_1 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_3); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 156, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_attempts, __pyx_int_1000) < 0) __PYX_ERR(0, 157, __pyx_L1_error)\n\n    \n    __pyx_t_1 = PyList_New(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 158, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_threads, __pyx_t_1) < 0) __PYX_ERR(0, 158, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_get_access_token); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_username); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_3);\n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_password); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_7 = PyTuple_New(2); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_7);\n    __Pyx_GIVEREF(__pyx_t_3);\n    PyTuple_SET_ITEM(__pyx_t_7, 0, __pyx_t_3);\n    __Pyx_GIVEREF(__pyx_t_4);\n    PyTuple_SET_ITEM(__pyx_t_7, 1, __pyx_t_4);\n    __pyx_t_3 = 0;\n    __pyx_t_4 = 0;\n    __pyx_t_4 = __Pyx_PyObject_Call(__pyx_t_1, __pyx_t_7, NULL); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n    if (PyDict_SetItem(__pyx_d, __pyx_n_s_token, __pyx_t_4) < 0) __PYX_ERR(0, 159, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n    \n    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_token); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 160, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_4);\n    __pyx_t_2 = __Pyx_PyObject_IsTrue(__pyx_t_4); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 160, __pyx_L1_error)\n    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n    __pyx_t_8 = ((!__pyx_t_2) != 0);\n    if (__pyx_t_8) {\n\n      \n      __pyx_t_4 = PyTuple_New(3); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __pyx_t_5 = 0;\n      __pyx_t_6 = 127;\n      __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_red_color); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __pyx_t_1 = __Pyx_PyObject_FormatSimple(__pyx_t_7, __pyx_empty_unicode); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_1);\n      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n      __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_1) : __pyx_t_6;\n      __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_1);\n      __Pyx_GIVEREF(__pyx_t_1);\n      PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_t_1);\n      __pyx_t_1 = 0;\n      __Pyx_INCREF(__pyx_kp_u__14);\n      __pyx_t_6 = (65535 > __pyx_t_6) ? 65535 : __pyx_t_6;\n      __pyx_t_5 += 17;\n      __Pyx_GIVEREF(__pyx_kp_u__14);\n      PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_kp_u__14);\n      __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_1);\n      __pyx_t_7 = __Pyx_PyObject_FormatSimple(__pyx_t_1, __pyx_empty_unicode); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n      __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_7) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_7) : __pyx_t_6;\n      __pyx_t_5 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_7);\n      __Pyx_GIVEREF(__pyx_t_7);\n      PyTuple_SET_ITEM(__pyx_t_4, 2, __pyx_t_7);\n      __pyx_t_7 = 0;\n      __pyx_t_7 = __Pyx_PyUnicode_Join(__pyx_t_4, 3, __pyx_t_5, __pyx_t_6); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      __pyx_t_4 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_7); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 161, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n      \n      goto __pyx_L3;\n    }\n\n    \n    /*else*/ {\n      __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_attempts); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 163, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __pyx_t_7 = __Pyx_PyInt_AddObjC(__pyx_t_4, __pyx_int_1, 1, 0, 0); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 163, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      __pyx_t_4 = PyTuple_New(2); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 163, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      __Pyx_INCREF(__pyx_int_1);\n      __Pyx_GIVEREF(__pyx_int_1);\n      PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_int_1);\n      __Pyx_GIVEREF(__pyx_t_7);\n      PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_t_7);\n      __pyx_t_7 = 0;\n      __pyx_t_7 = __Pyx_PyObject_Call(__pyx_builtin_range, __pyx_t_4, NULL); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 163, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_7);\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      if (likely(PyList_CheckExact(__pyx_t_7)) || PyTuple_CheckExact(__pyx_t_7)) {\n        __pyx_t_4 = __pyx_t_7; __Pyx_INCREF(__pyx_t_4); __pyx_t_5 = 0;\n        __pyx_t_9 = NULL;\n      } else {\n        __pyx_t_5 = -1; __pyx_t_4 = PyObject_GetIter(__pyx_t_7); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 163, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __pyx_t_9 = Py_TYPE(__pyx_t_4)->tp_iternext; if (unlikely(!__pyx_t_9)) __PYX_ERR(0, 163, __pyx_L1_error)\n      }\n      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n      for (;;) {\n        if (likely(!__pyx_t_9)) {\n          if (likely(PyList_CheckExact(__pyx_t_4))) {\n            if (__pyx_t_5 >= PyList_GET_SIZE(__pyx_t_4)) break;\n            #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n            __pyx_t_7 = PyList_GET_ITEM(__pyx_t_4, __pyx_t_5); __Pyx_INCREF(__pyx_t_7); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 163, __pyx_L1_error)\n            #else\n            __pyx_t_7 = PySequence_ITEM(__pyx_t_4, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 163, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_7);\n            #endif\n          } else {\n            if (__pyx_t_5 >= PyTuple_GET_SIZE(__pyx_t_4)) break;\n            #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n            __pyx_t_7 = PyTuple_GET_ITEM(__pyx_t_4, __pyx_t_5); __Pyx_INCREF(__pyx_t_7); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 163, __pyx_L1_error)\n            #else\n            __pyx_t_7 = PySequence_ITEM(__pyx_t_4, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 163, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_7);\n            #endif\n          }\n        } else {\n          __pyx_t_7 = __pyx_t_9(__pyx_t_4);\n          if (unlikely(!__pyx_t_7)) {\n            PyObject* exc_type = PyErr_Occurred();\n            if (exc_type) {\n              if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();\n              else __PYX_ERR(0, 163, __pyx_L1_error)\n            }\n            break;\n          }\n          __Pyx_GOTREF(__pyx_t_7);\n        }\n        if (PyDict_SetItem(__pyx_d, __pyx_n_s_attempt, __pyx_t_7) < 0) __PYX_ERR(0, 163, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_attempt); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __pyx_t_1 = __Pyx_PyInt_RemainderObjC(__pyx_t_7, __pyx_int_3, 3, 0, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __pyx_t_7 = __Pyx_PyInt_EqObjC(__pyx_t_1, __pyx_int_1, 1, 0); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n        __pyx_t_2 = __Pyx_PyObject_IsTrue(__pyx_t_7); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        if (__pyx_t_2) {\n        } else {\n          __pyx_t_8 = __pyx_t_2;\n          goto __pyx_L7_bool_binop_done;\n        }\n        __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_attempt); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __pyx_t_1 = __Pyx_PyInt_NeObjC(__pyx_t_7, __pyx_int_1, 1, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __pyx_t_2 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 164, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n        __pyx_t_8 = __pyx_t_2;\n        __pyx_L7_bool_binop_done:;\n        if (__pyx_t_8) {\n\n          \n          __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_get_access_token); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 165, __pyx_L1_error)\n          __Pyx_GOTREF(__pyx_t_7);\n          __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_username); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 165, __pyx_L1_error)\n          __Pyx_GOTREF(__pyx_t_3);\n          __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_password); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 165, __pyx_L1_error)\n          __Pyx_GOTREF(__pyx_t_10);\n          __pyx_t_11 = NULL;\n          __pyx_t_12 = 0;\n          if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_7))) {\n            __pyx_t_11 = PyMethod_GET_SELF(__pyx_t_7);\n            if (likely(__pyx_t_11)) {\n              PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_7);\n              __Pyx_INCREF(__pyx_t_11);\n              __Pyx_INCREF(function);\n              __Pyx_DECREF_SET(__pyx_t_7, function);\n              __pyx_t_12 = 1;\n            }\n          }\n          #if CYTHON_FAST_PYCALL\n          if (PyFunction_Check(__pyx_t_7)) {\n            PyObject *__pyx_temp[3] = {__pyx_t_11, __pyx_t_3, __pyx_t_10};\n            __pyx_t_1 = __Pyx_PyFunction_FastCall(__pyx_t_7, __pyx_temp+1-__pyx_t_12, 2+__pyx_t_12); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 165, __pyx_L1_error)\n            __Pyx_XDECREF(__pyx_t_11); __pyx_t_11 = 0;\n            __Pyx_GOTREF(__pyx_t_1);\n            __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          } else\n          #endif\n          #if CYTHON_FAST_PYCCALL\n          if (__Pyx_PyFastCFunction_Check(__pyx_t_7)) {\n            PyObject *__pyx_temp[3] = {__pyx_t_11, __pyx_t_3, __pyx_t_10};\n            __pyx_t_1 = __Pyx_PyCFunction_FastCall(__pyx_t_7, __pyx_temp+1-__pyx_t_12, 2+__pyx_t_12); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 165, __pyx_L1_error)\n            __Pyx_XDECREF(__pyx_t_11); __pyx_t_11 = 0;\n            __Pyx_GOTREF(__pyx_t_1);\n            __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;\n            __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;\n          } else\n          #endif\n          {\n            __pyx_t_13 = PyTuple_New(2+__pyx_t_12); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 165, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_13);\n            if (__pyx_t_11) {\n              __Pyx_GIVEREF(__pyx_t_11); PyTuple_SET_ITEM(__pyx_t_13, 0, __pyx_t_11); __pyx_t_11 = NULL;\n            }\n            __Pyx_GIVEREF(__pyx_t_3);\n            PyTuple_SET_ITEM(__pyx_t_13, 0+__pyx_t_12, __pyx_t_3);\n            __Pyx_GIVEREF(__pyx_t_10);\n            PyTuple_SET_ITEM(__pyx_t_13, 1+__pyx_t_12, __pyx_t_10);\n            __pyx_t_3 = 0;\n            __pyx_t_10 = 0;\n            __pyx_t_1 = __Pyx_PyObject_Call(__pyx_t_7, __pyx_t_13, NULL); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 165, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_1);\n            __Pyx_DECREF(__pyx_t_13); __pyx_t_13 = 0;\n          }\n          __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n          if (PyDict_SetItem(__pyx_d, __pyx_n_s_token, __pyx_t_1) < 0) __PYX_ERR(0, 165, __pyx_L1_error)\n          __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n          \n          __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_token); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 166, __pyx_L1_error)\n          __Pyx_GOTREF(__pyx_t_1);\n          __pyx_t_8 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_8 < 0)) __PYX_ERR(0, 166, __pyx_L1_error)\n          __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n          __pyx_t_2 = ((!__pyx_t_8) != 0);\n          if (__pyx_t_2) {\n\n            \n            __pyx_t_1 = PyTuple_New(3); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_1);\n            __pyx_t_14 = 0;\n            __pyx_t_6 = 127;\n            __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_red_color); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_7);\n            __pyx_t_13 = __Pyx_PyObject_FormatSimple(__pyx_t_7, __pyx_empty_unicode); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_13);\n            __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n            __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_13) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_13) : __pyx_t_6;\n            __pyx_t_14 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_13);\n            __Pyx_GIVEREF(__pyx_t_13);\n            PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_t_13);\n            __pyx_t_13 = 0;\n            __Pyx_INCREF(__pyx_kp_u__15);\n            __pyx_t_6 = (65535 > __pyx_t_6) ? 65535 : __pyx_t_6;\n            __pyx_t_14 += 21;\n            __Pyx_GIVEREF(__pyx_kp_u__15);\n            PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_kp_u__15);\n            __Pyx_GetModuleGlobalName(__pyx_t_13, __pyx_n_s_reset_color); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_13);\n            __pyx_t_7 = __Pyx_PyObject_FormatSimple(__pyx_t_13, __pyx_empty_unicode); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_7);\n            __Pyx_DECREF(__pyx_t_13); __pyx_t_13 = 0;\n            __pyx_t_6 = (__Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_7) > __pyx_t_6) ? __Pyx_PyUnicode_MAX_CHAR_VALUE(__pyx_t_7) : __pyx_t_6;\n            __pyx_t_14 += __Pyx_PyUnicode_GET_LENGTH(__pyx_t_7);\n            __Pyx_GIVEREF(__pyx_t_7);\n            PyTuple_SET_ITEM(__pyx_t_1, 2, __pyx_t_7);\n            __pyx_t_7 = 0;\n            __pyx_t_7 = __Pyx_PyUnicode_Join(__pyx_t_1, 3, __pyx_t_14, __pyx_t_6); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_7);\n            __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n            __pyx_t_1 = __Pyx_PyObject_CallOneArg(__pyx_builtin_print, __pyx_t_7); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 167, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_1);\n            __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n            __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n            \n            goto __pyx_L5_break;\n\n            \n          }\n\n          \n        }\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_threading); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 169, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_Thread); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 169, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n        \n        __pyx_t_1 = __Pyx_PyDict_NewPresized(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 170, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __Pyx_GetModuleGlobalName(__pyx_t_13, __pyx_n_s_subscribe_to_promotion); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 170, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_13);\n        if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_target, __pyx_t_13) < 0) __PYX_ERR(0, 170, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_13); __pyx_t_13 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_13, __pyx_n_s_username); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 171, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_13);\n        __Pyx_GetModuleGlobalName(__pyx_t_10, __pyx_n_s_token); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 171, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_10);\n        __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_attempt); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 171, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_3);\n        __pyx_t_11 = PyTuple_New(3); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 171, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_11);\n        __Pyx_GIVEREF(__pyx_t_13);\n        PyTuple_SET_ITEM(__pyx_t_11, 0, __pyx_t_13);\n        __Pyx_GIVEREF(__pyx_t_10);\n        PyTuple_SET_ITEM(__pyx_t_11, 1, __pyx_t_10);\n        __Pyx_GIVEREF(__pyx_t_3);\n        PyTuple_SET_ITEM(__pyx_t_11, 2, __pyx_t_3);\n        __pyx_t_13 = 0;\n        __pyx_t_10 = 0;\n        __pyx_t_3 = 0;\n        if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_args, __pyx_t_11) < 0) __PYX_ERR(0, 170, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_11); __pyx_t_11 = 0;\n\n        \n        __pyx_t_11 = __Pyx_PyObject_Call(__pyx_t_7, __pyx_empty_tuple, __pyx_t_1); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 169, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_11);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n        if (PyDict_SetItem(__pyx_d, __pyx_n_s_thread, __pyx_t_11) < 0) __PYX_ERR(0, 169, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_11); __pyx_t_11 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_11, __pyx_n_s_threads); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 172, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_11);\n        __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_thread); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 172, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __pyx_t_15 = __Pyx_PyObject_Append(__pyx_t_11, __pyx_t_1); if (unlikely(__pyx_t_15 == ((int)-1))) __PYX_ERR(0, 172, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_11); __pyx_t_11 = 0;\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_11, __pyx_n_s_thread); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 173, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_11);\n        __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_t_11, __pyx_n_s_start); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 173, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __Pyx_DECREF(__pyx_t_11); __pyx_t_11 = 0;\n        __pyx_t_11 = NULL;\n        if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_7))) {\n          __pyx_t_11 = PyMethod_GET_SELF(__pyx_t_7);\n          if (likely(__pyx_t_11)) {\n            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_7);\n            __Pyx_INCREF(__pyx_t_11);\n            __Pyx_INCREF(function);\n            __Pyx_DECREF_SET(__pyx_t_7, function);\n          }\n        }\n        __pyx_t_1 = (__pyx_t_11) ? __Pyx_PyObject_CallOneArg(__pyx_t_7, __pyx_t_11) : __Pyx_PyObject_CallNoArg(__pyx_t_7);\n        __Pyx_XDECREF(__pyx_t_11); __pyx_t_11 = 0;\n        if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 173, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n        \n      }\n      __pyx_L5_break:;\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n      \n      __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_threads); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 174, __pyx_L1_error)\n      __Pyx_GOTREF(__pyx_t_4);\n      if (likely(PyList_CheckExact(__pyx_t_4)) || PyTuple_CheckExact(__pyx_t_4)) {\n        __pyx_t_1 = __pyx_t_4; __Pyx_INCREF(__pyx_t_1); __pyx_t_5 = 0;\n        __pyx_t_9 = NULL;\n      } else {\n        __pyx_t_5 = -1; __pyx_t_1 = PyObject_GetIter(__pyx_t_4); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 174, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_1);\n        __pyx_t_9 = Py_TYPE(__pyx_t_1)->tp_iternext; if (unlikely(!__pyx_t_9)) __PYX_ERR(0, 174, __pyx_L1_error)\n      }\n      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n      for (;;) {\n        if (likely(!__pyx_t_9)) {\n          if (likely(PyList_CheckExact(__pyx_t_1))) {\n            if (__pyx_t_5 >= PyList_GET_SIZE(__pyx_t_1)) break;\n            #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n            __pyx_t_4 = PyList_GET_ITEM(__pyx_t_1, __pyx_t_5); __Pyx_INCREF(__pyx_t_4); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 174, __pyx_L1_error)\n            #else\n            __pyx_t_4 = PySequence_ITEM(__pyx_t_1, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 174, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_4);\n            #endif\n          } else {\n            if (__pyx_t_5 >= PyTuple_GET_SIZE(__pyx_t_1)) break;\n            #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n            __pyx_t_4 = PyTuple_GET_ITEM(__pyx_t_1, __pyx_t_5); __Pyx_INCREF(__pyx_t_4); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 174, __pyx_L1_error)\n            #else\n            __pyx_t_4 = PySequence_ITEM(__pyx_t_1, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 174, __pyx_L1_error)\n            __Pyx_GOTREF(__pyx_t_4);\n            #endif\n          }\n        } else {\n          __pyx_t_4 = __pyx_t_9(__pyx_t_1);\n          if (unlikely(!__pyx_t_4)) {\n            PyObject* exc_type = PyErr_Occurred();\n            if (exc_type) {\n              if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();\n              else __PYX_ERR(0, 174, __pyx_L1_error)\n            }\n            break;\n          }\n          __Pyx_GOTREF(__pyx_t_4);\n        }\n        if (PyDict_SetItem(__pyx_d, __pyx_n_s_thread, __pyx_t_4) < 0) __PYX_ERR(0, 174, __pyx_L1_error)\n        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n        \n        __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_thread); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 175, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_7);\n        __pyx_t_11 = __Pyx_PyObject_GetAttrStr(__pyx_t_7, __pyx_n_s_join); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 175, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_11);\n        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;\n        __pyx_t_7 = NULL;\n        if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_11))) {\n          __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_11);\n          if (likely(__pyx_t_7)) {\n            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_11);\n            __Pyx_INCREF(__pyx_t_7);\n            __Pyx_INCREF(function);\n            __Pyx_DECREF_SET(__pyx_t_11, function);\n          }\n        }\n        __pyx_t_4 = (__pyx_t_7) ? __Pyx_PyObject_CallOneArg(__pyx_t_11, __pyx_t_7) : __Pyx_PyObject_CallNoArg(__pyx_t_11);\n        __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;\n        if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 175, __pyx_L1_error)\n        __Pyx_GOTREF(__pyx_t_4);\n        __Pyx_DECREF(__pyx_t_11); __pyx_t_11 = 0;\n        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;\n\n        \n      }\n      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n    }\n    __pyx_L3:;\n\n    \n    __pyx_t_1 = __Pyx_PyObject_Call(__pyx_builtin_print, __pyx_tuple__13, NULL); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 176, __pyx_L1_error)\n    __Pyx_GOTREF(__pyx_t_1);\n    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n    \n  }\n\n  \n  __pyx_t_1 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 4, __pyx_L1_error)\n  __Pyx_GOTREF(__pyx_t_1);\n  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_1) < 0) __PYX_ERR(0, 4, __pyx_L1_error)\n  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;\n\n  /*--- Wrapped vars code ---*/\n\n  goto __pyx_L0;\n  __pyx_L1_error:;\n  __Pyx_XDECREF(__pyx_t_1);\n  __Pyx_XDECREF(__pyx_t_3);\n  __Pyx_XDECREF(__pyx_t_4);\n  __Pyx_XDECREF(__pyx_t_7);\n  __Pyx_XDECREF(__pyx_t_10);\n  __Pyx_XDECREF(__pyx_t_11);\n  __Pyx_XDECREF(__pyx_t_13);\n  if (__pyx_m) {\n    if (__pyx_d) {\n      __Pyx_AddTraceback("init source", __pyx_clineno, __pyx_lineno, __pyx_filename);\n    }\n    Py_CLEAR(__pyx_m);\n  } else if (!PyErr_Occurred()) {\n    PyErr_SetString(PyExc_ImportError, "init source");\n  }\n  __pyx_L0:;\n  __Pyx_RefNannyFinishContext();\n  #if CYTHON_PEP489_MULTI_PHASE_INIT\n  return (__pyx_m != NULL) ? 0 : -1;\n  #elif PY_MAJOR_VERSION >= 3\n  return __pyx_m;\n  #else\n  return;\n  #endif\n}\n\n/* --- Runtime support code --- */\n/* Refnanny */\n#if CYTHON_REFNANNY\nstatic __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {\n    PyObject *m = NULL, *p = NULL;\n    void *r = NULL;\n    m = PyImport_ImportModule(modname);\n    if (!m) goto end;\n    p = PyObject_GetAttrString(m, "RefNannyAPI");\n    if (!p) goto end;\n    r = PyLong_AsVoidPtr(p);\nend:\n    Py_XDECREF(p);\n    Py_XDECREF(m);\n    return (__Pyx_RefNannyAPIStruct *)r;\n}\n#endif\n\n/* PyObjectGetAttrStr */\n#if CYTHON_USE_TYPE_SLOTS\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {\n    PyTypeObject* tp = Py_TYPE(obj);\n    if (likely(tp->tp_getattro))\n        return tp->tp_getattro(obj, attr_name);\n#if PY_MAJOR_VERSION < 3\n    if (likely(tp->tp_getattr))\n        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));\n#endif\n    return PyObject_GetAttr(obj, attr_name);\n}\n#endif\n\n/* GetBuiltinName */\nstatic PyObject *__Pyx_GetBuiltinName(PyObject *name) {\n    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);\n    if (unlikely(!result)) {\n        PyErr_Format(PyExc_NameError,\n#if PY_MAJOR_VERSION >= 3\n            "name \'%U\' is not defined", name);\n#else\n            "name \'%.200s\' is not defined", PyString_AS_STRING(name));\n#endif\n    }\n    return result;\n}\n\n/* RaiseArgTupleInvalid */\nstatic void __Pyx_RaiseArgtupleInvalid(\n    const char* func_name,\n    int exact,\n    Py_ssize_t num_min,\n    Py_ssize_t num_max,\n    Py_ssize_t num_found)\n{\n    Py_ssize_t num_expected;\n    const char *more_or_less;\n    if (num_found < num_min) {\n        num_expected = num_min;\n        more_or_less = "at least";\n    } else {\n        num_expected = num_max;\n        more_or_less = "at most";\n    }\n    if (exact) {\n        more_or_less = "exactly";\n    }\n    PyErr_Format(PyExc_TypeError,\n                 "%.200s() takes %.8s %" CYTHON_FORMAT_SSIZE_T "d positional argument%.1s (%" CYTHON_FORMAT_SSIZE_T "d given)",\n                 func_name, more_or_less, num_expected,\n                 (num_expected == 1) ? "" : "s", num_found);\n}\n\n/* RaiseDoubleKeywords */\nstatic void __Pyx_RaiseDoubleKeywordsError(\n    const char* func_name,\n    PyObject* kw_name)\n{\n    PyErr_Format(PyExc_TypeError,\n        #if PY_MAJOR_VERSION >= 3\n        "%s() got multiple values for keyword argument \'%U\'", func_name, kw_name);\n        #else\n        "%s() got multiple values for keyword argument \'%s\'", func_name,\n        PyString_AsString(kw_name));\n        #endif\n}\n\n/* ParseKeywords */\nstatic int __Pyx_ParseOptionalKeywords(\n    PyObject *kwds,\n    PyObject **argnames[],\n    PyObject *kwds2,\n    PyObject *values[],\n    Py_ssize_t num_pos_args,\n    const char* function_name)\n{\n    PyObject *key = 0, *value = 0;\n    Py_ssize_t pos = 0;\n    PyObject*** name;\n    PyObject*** first_kw_arg = argnames + num_pos_args;\n    while (PyDict_Next(kwds, &pos, &key, &value)) {\n        name = first_kw_arg;\n        while (*name && (**name != key)) name++;\n        if (*name) {\n            values[name-argnames] = value;\n            continue;\n        }\n        name = first_kw_arg;\n        #if PY_MAJOR_VERSION < 3\n        if (likely(PyString_Check(key))) {\n            while (*name) {\n                if ((CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**name) == PyString_GET_SIZE(key))\n                        && _PyString_Eq(**name, key)) {\n                    values[name-argnames] = value;\n                    break;\n                }\n                name++;\n            }\n            if (*name) continue;\n            else {\n                PyObject*** argname = argnames;\n                while (argname != first_kw_arg) {\n                    if ((**argname == key) || (\n                            (CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**argname) == PyString_GET_SIZE(key))\n                             && _PyString_Eq(**argname, key))) {\n                        goto arg_passed_twice;\n                    }\n                    argname++;\n                }\n            }\n        } else\n        #endif\n        if (likely(PyUnicode_Check(key))) {\n            while (*name) {\n                int cmp = (**name == key) ? 0 :\n                #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3\n                    (__Pyx_PyUnicode_GET_LENGTH(**name) != __Pyx_PyUnicode_GET_LENGTH(key)) ? 1 :\n                #endif\n                    PyUnicode_Compare(**name, key);\n                if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;\n                if (cmp == 0) {\n                    values[name-argnames] = value;\n                    break;\n                }\n                name++;\n            }\n            if (*name) continue;\n            else {\n                PyObject*** argname = argnames;\n                while (argname != first_kw_arg) {\n                    int cmp = (**argname == key) ? 0 :\n                    #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3\n                        (__Pyx_PyUnicode_GET_LENGTH(**argname) != __Pyx_PyUnicode_GET_LENGTH(key)) ? 1 :\n                    #endif\n                        PyUnicode_Compare(**argname, key);\n                    if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;\n                    if (cmp == 0) goto arg_passed_twice;\n                    argname++;\n                }\n            }\n        } else\n            goto invalid_keyword_type;\n        if (kwds2) {\n            if (unlikely(PyDict_SetItem(kwds2, key, value))) goto bad;\n        } else {\n            goto invalid_keyword;\n        }\n    }\n    return 0;\narg_passed_twice:\n    __Pyx_RaiseDoubleKeywordsError(function_name, key);\n    goto bad;\ninvalid_keyword_type:\n    PyErr_Format(PyExc_TypeError,\n        "%.200s() keywords must be strings", function_name);\n    goto bad;\ninvalid_keyword:\n    PyErr_Format(PyExc_TypeError,\n    #if PY_MAJOR_VERSION < 3\n        "%.200s() got an unexpected keyword argument \'%.200s\'",\n        function_name, PyString_AsString(key));\n    #else\n        "%s() got an unexpected keyword argument \'%U\'",\n        function_name, key);\n    #endif\nbad:\n    return -1;\n}\n\n/* PyDictVersioning */\n#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS\nstatic CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {\n    PyObject *dict = Py_TYPE(obj)->tp_dict;\n    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;\n}\nstatic CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {\n    PyObject **dictptr = NULL;\n    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;\n    if (offset) {\n#if CYTHON_COMPILING_IN_CPYTHON\n        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);\n#else\n        dictptr = _PyObject_GetDictPtr(obj);\n#endif\n    }\n    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;\n}\nstatic CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {\n    PyObject *dict = Py_TYPE(obj)->tp_dict;\n    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))\n        return 0;\n    return obj_dict_version == __Pyx_get_object_dict_version(obj);\n}\n#endif\n\n/* GetModuleGlobalName */\n#if CYTHON_USE_DICT_VERSIONS\nstatic PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value)\n#else\nstatic CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name)\n#endif\n{\n    PyObject *result;\n#if !CYTHON_AVOID_BORROWED_REFS\n#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1\n    result = _PyDict_GetItem_KnownHash(__pyx_d, name, ((PyASCIIObject *) name)->hash);\n    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)\n    if (likely(result)) {\n        return __Pyx_NewRef(result);\n    } else if (unlikely(PyErr_Occurred())) {\n        return NULL;\n    }\n#else\n    result = PyDict_GetItem(__pyx_d, name);\n    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)\n    if (likely(result)) {\n        return __Pyx_NewRef(result);\n    }\n#endif\n#else\n    result = PyObject_GetItem(__pyx_d, name);\n    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)\n    if (likely(result)) {\n        return __Pyx_NewRef(result);\n    }\n    PyErr_Clear();\n#endif\n    return __Pyx_GetBuiltinName(name);\n}\n\n/* PyCFunctionFastCall */\n#if CYTHON_FAST_PYCCALL\nstatic CYTHON_INLINE PyObject * __Pyx_PyCFunction_FastCall(PyObject *func_obj, PyObject **args, Py_ssize_t nargs) {\n    PyCFunctionObject *func = (PyCFunctionObject*)func_obj;\n    PyCFunction meth = PyCFunction_GET_FUNCTION(func);\n    PyObject *self = PyCFunction_GET_SELF(func);\n    int flags = PyCFunction_GET_FLAGS(func);\n    assert(PyCFunction_Check(func));\n    assert(METH_FASTCALL == (flags & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)));\n    assert(nargs >= 0);\n    assert(nargs == 0 || args != NULL);\n    /* _PyCFunction_FastCallDict() must not be called with an exception set,\n       because it may clear it (directly or indirectly) and so the\n       caller loses its exception */\n    assert(!PyErr_Occurred());\n    if ((PY_VERSION_HEX < 0x030700A0) || unlikely(flags & METH_KEYWORDS)) {\n        return (*((__Pyx_PyCFunctionFastWithKeywords)(void*)meth)) (self, args, nargs, NULL);\n    } else {\n        return (*((__Pyx_PyCFunctionFast)(void*)meth)) (self, args, nargs);\n    }\n}\n#endif\n\n/* PyFunctionFastCall */\n#if CYTHON_FAST_PYCALL\nstatic PyObject* __Pyx_PyFunction_FastCallNoKw(PyCodeObject *co, PyObject **args, Py_ssize_t na,\n                                               PyObject *globals) {\n    PyFrameObject *f;\n    PyThreadState *tstate = __Pyx_PyThreadState_Current;\n    PyObject **fastlocals;\n    Py_ssize_t i;\n    PyObject *result;\n    assert(globals != NULL);\n    /* XXX Perhaps we should create a specialized\n       PyFrame_New() that doesn\'t take locals, but does\n       take builtins without sanity checking them.\n       */\n    assert(tstate != NULL);\n    f = PyFrame_New(tstate, co, globals, NULL);\n    if (f == NULL) {\n        return NULL;\n    }\n    fastlocals = __Pyx_PyFrame_GetLocalsplus(f);\n    for (i = 0; i < na; i++) {\n        Py_INCREF(*args);\n        fastlocals[i] = *args++;\n    }\n    result = PyEval_EvalFrameEx(f,0);\n    ++tstate->recursion_depth;\n    Py_DECREF(f);\n    --tstate->recursion_depth;\n    return result;\n}\n#if 1 || PY_VERSION_HEX < 0x030600B1\nstatic PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs) {\n    PyCodeObject *co = (PyCodeObject *)PyFunction_GET_CODE(func);\n    PyObject *globals = PyFunction_GET_GLOBALS(func);\n    PyObject *argdefs = PyFunction_GET_DEFAULTS(func);\n    PyObject *closure;\n#if PY_MAJOR_VERSION >= 3\n    PyObject *kwdefs;\n#endif\n    PyObject *kwtuple, **k;\n    PyObject **d;\n    Py_ssize_t nd;\n    Py_ssize_t nk;\n    PyObject *result;\n    assert(kwargs == NULL || PyDict_Check(kwargs));\n    nk = kwargs ? PyDict_Size(kwargs) : 0;\n    if (Py_EnterRecursiveCall((char*)" while calling a Python object")) {\n        return NULL;\n    }\n    if (\n#if PY_MAJOR_VERSION >= 3\n            co->co_kwonlyargcount == 0 &&\n#endif\n            likely(kwargs == NULL || nk == 0) &&\n            co->co_flags == (CO_OPTIMIZED | CO_NEWLOCALS | CO_NOFREE)) {\n        if (argdefs == NULL && co->co_argcount == nargs) {\n            result = __Pyx_PyFunction_FastCallNoKw(co, args, nargs, globals);\n            goto done;\n        }\n        else if (nargs == 0 && argdefs != NULL\n                 && co->co_argcount == Py_SIZE(argdefs)) {\n            /* function called with no arguments, but all parameters have\n               a default value: use default values as arguments .*/\n            args = &PyTuple_GET_ITEM(argdefs, 0);\n            result =__Pyx_PyFunction_FastCallNoKw(co, args, Py_SIZE(argdefs), globals);\n            goto done;\n        }\n    }\n    if (kwargs != NULL) {\n        Py_ssize_t pos, i;\n        kwtuple = PyTuple_New(2 * nk);\n        if (kwtuple == NULL) {\n            result = NULL;\n            goto done;\n        }\n        k = &PyTuple_GET_ITEM(kwtuple, 0);\n        pos = i = 0;\n        while (PyDict_Next(kwargs, &pos, &k[i], &k[i+1])) {\n            Py_INCREF(k[i]);\n            Py_INCREF(k[i+1]);\n            i += 2;\n        }\n        nk = i / 2;\n    }\n    else {\n        kwtuple = NULL;\n        k = NULL;\n    }\n    closure = PyFunction_GET_CLOSURE(func);\n#if PY_MAJOR_VERSION >= 3\n    kwdefs = PyFunction_GET_KW_DEFAULTS(func);\n#endif\n    if (argdefs != NULL) {\n        d = &PyTuple_GET_ITEM(argdefs, 0);\n        nd = Py_SIZE(argdefs);\n    }\n    else {\n        d = NULL;\n        nd = 0;\n    }\n#if PY_MAJOR_VERSION >= 3\n    result = PyEval_EvalCodeEx((PyObject*)co, globals, (PyObject *)NULL,\n                               args, (int)nargs,\n                               k, (int)nk,\n                               d, (int)nd, kwdefs, closure);\n#else\n    result = PyEval_EvalCodeEx(co, globals, (PyObject *)NULL,\n                               args, (int)nargs,\n                               k, (int)nk,\n                               d, (int)nd, closure);\n#endif\n    Py_XDECREF(kwtuple);\ndone:\n    Py_LeaveRecursiveCall();\n    return result;\n}\n#endif\n#endif\n\n/* PyObjectCall */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw) {\n    PyObject *result;\n    ternaryfunc call = Py_TYPE(func)->tp_call;\n    if (unlikely(!call))\n        return PyObject_Call(func, arg, kw);\n    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))\n        return NULL;\n    result = (*call)(func, arg, kw);\n    Py_LeaveRecursiveCall();\n    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {\n        PyErr_SetString(\n            PyExc_SystemError,\n            "NULL result without error in PyObject_Call");\n    }\n    return result;\n}\n#endif\n\n/* PyObjectCall2Args */\nstatic CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2) {\n    PyObject *args, *result = NULL;\n    #if CYTHON_FAST_PYCALL\n    if (PyFunction_Check(function)) {\n        PyObject *args[2] = {arg1, arg2};\n        return __Pyx_PyFunction_FastCall(function, args, 2);\n    }\n    #endif\n    #if CYTHON_FAST_PYCCALL\n    if (__Pyx_PyFastCFunction_Check(function)) {\n        PyObject *args[2] = {arg1, arg2};\n        return __Pyx_PyCFunction_FastCall(function, args, 2);\n    }\n    #endif\n    args = PyTuple_New(2);\n    if (unlikely(!args)) goto done;\n    Py_INCREF(arg1);\n    PyTuple_SET_ITEM(args, 0, arg1);\n    Py_INCREF(arg2);\n    PyTuple_SET_ITEM(args, 1, arg2);\n    Py_INCREF(function);\n    result = __Pyx_PyObject_Call(function, args, NULL);\n    Py_DECREF(args);\n    Py_DECREF(function);\ndone:\n    return result;\n}\n\n/* PyObjectCallMethO */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg) {\n    PyObject *self, *result;\n    PyCFunction cfunc;\n    cfunc = PyCFunction_GET_FUNCTION(func);\n    self = PyCFunction_GET_SELF(func);\n    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))\n        return NULL;\n    result = cfunc(self, arg);\n    Py_LeaveRecursiveCall();\n    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {\n        PyErr_SetString(\n            PyExc_SystemError,\n            "NULL result without error in PyObject_Call");\n    }\n    return result;\n}\n#endif\n\n/* PyObjectCallOneArg */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic PyObject* __Pyx__PyObject_CallOneArg(PyObject *func, PyObject *arg) {\n    PyObject *result;\n    PyObject *args = PyTuple_New(1);\n    if (unlikely(!args)) return NULL;\n    Py_INCREF(arg);\n    PyTuple_SET_ITEM(args, 0, arg);\n    result = __Pyx_PyObject_Call(func, args, NULL);\n    Py_DECREF(args);\n    return result;\n}\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {\n#if CYTHON_FAST_PYCALL\n    if (PyFunction_Check(func)) {\n        return __Pyx_PyFunction_FastCall(func, &arg, 1);\n    }\n#endif\n    if (likely(PyCFunction_Check(func))) {\n        if (likely(PyCFunction_GET_FLAGS(func) & METH_O)) {\n            return __Pyx_PyObject_CallMethO(func, arg);\n#if CYTHON_FAST_PYCCALL\n        } else if (__Pyx_PyFastCFunction_Check(func)) {\n            return __Pyx_PyCFunction_FastCall(func, &arg, 1);\n#endif\n        }\n    }\n    return __Pyx__PyObject_CallOneArg(func, arg);\n}\n#else\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {\n    PyObject *result;\n    PyObject *args = PyTuple_Pack(1, arg);\n    if (unlikely(!args)) return NULL;\n    result = __Pyx_PyObject_Call(func, args, NULL);\n    Py_DECREF(args);\n    return result;\n}\n#endif\n\n/* PyIntCompare */\nstatic CYTHON_INLINE PyObject* __Pyx_PyInt_EqObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, CYTHON_UNUSED long inplace) {\n    if (op1 == op2) {\n        Py_RETURN_TRUE;\n    }\n    #if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_CheckExact(op1))) {\n        const long b = intval;\n        long a = PyInt_AS_LONG(op1);\n        if (a == b) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    #endif\n    #if CYTHON_USE_PYLONG_INTERNALS\n    if (likely(PyLong_CheckExact(op1))) {\n        int unequal;\n        unsigned long uintval;\n        Py_ssize_t size = Py_SIZE(op1);\n        const digit* digits = ((PyLongObject*)op1)->ob_digit;\n        if (intval == 0) {\n            if (size == 0) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n        } else if (intval < 0) {\n            if (size >= 0)\n                Py_RETURN_FALSE;\n            intval = -intval;\n            size = -size;\n        } else {\n            if (size <= 0)\n                Py_RETURN_FALSE;\n        }\n        uintval = (unsigned long) intval;\n#if PyLong_SHIFT * 4 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 4)) {\n            unequal = (size != 5) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[3] != ((uintval >> (3 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[4] != ((uintval >> (4 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 3 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 3)) {\n            unequal = (size != 4) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[3] != ((uintval >> (3 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 2 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 2)) {\n            unequal = (size != 3) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 1 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 1)) {\n            unequal = (size != 2) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n            unequal = (size != 1) || (((unsigned long) digits[0]) != (uintval & (unsigned long) PyLong_MASK));\n        if (unequal == 0) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    #endif\n    if (PyFloat_CheckExact(op1)) {\n        const long b = intval;\n        double a = PyFloat_AS_DOUBLE(op1);\n        if ((double)a == (double)b) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    return (\n        PyObject_RichCompare(op1, op2, Py_EQ));\n}\n\n/* PyObjectCallNoArg */\n#if CYTHON_COMPILING_IN_CPYTHON\nstatic CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func) {\n#if CYTHON_FAST_PYCALL\n    if (PyFunction_Check(func)) {\n        return __Pyx_PyFunction_FastCall(func, NULL, 0);\n    }\n#endif\n#if defined(__Pyx_CyFunction_USED) && defined(NDEBUG)\n    if (likely(PyCFunction_Check(func) || __Pyx_CyFunction_Check(func)))\n#else\n    if (likely(PyCFunction_Check(func)))\n#endif\n    {\n        if (likely(PyCFunction_GET_FLAGS(func) & METH_NOARGS)) {\n            return __Pyx_PyObject_CallMethO(func, NULL);\n        }\n    }\n    return __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL);\n}\n#endif\n\n/* DictGetItem */\n#if PY_MAJOR_VERSION >= 3 && !CYTHON_COMPILING_IN_PYPY\nstatic PyObject *__Pyx_PyDict_GetItem(PyObject *d, PyObject* key) {\n    PyObject *value;\n    value = PyDict_GetItemWithError(d, key);\n    if (unlikely(!value)) {\n        if (!PyErr_Occurred()) {\n            if (unlikely(PyTuple_Check(key))) {\n                PyObject* args = PyTuple_Pack(1, key);\n                if (likely(args)) {\n                    PyErr_SetObject(PyExc_KeyError, args);\n                    Py_DECREF(args);\n                }\n            } else {\n                PyErr_SetObject(PyExc_KeyError, key);\n            }\n        }\n        return NULL;\n    }\n    Py_INCREF(value);\n    return value;\n}\n#endif\n\n/* JoinPyUnicode */\nstatic PyObject* __Pyx_PyUnicode_Join(PyObject* value_tuple, Py_ssize_t value_count, Py_ssize_t result_ulength,\n                                      CYTHON_UNUSED Py_UCS4 max_char) {\n#if CYTHON_USE_UNICODE_INTERNALS && CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n    PyObject *result_uval;\n    int result_ukind;\n    Py_ssize_t i, char_pos;\n    void *result_udata;\n#if CYTHON_PEP393_ENABLED\n    result_uval = PyUnicode_New(result_ulength, max_char);\n    if (unlikely(!result_uval)) return NULL;\n    result_ukind = (max_char <= 255) ? PyUnicode_1BYTE_KIND : (max_char <= 65535) ? PyUnicode_2BYTE_KIND : PyUnicode_4BYTE_KIND;\n    result_udata = PyUnicode_DATA(result_uval);\n#else\n    result_uval = PyUnicode_FromUnicode(NULL, result_ulength);\n    if (unlikely(!result_uval)) return NULL;\n    result_ukind = sizeof(Py_UNICODE);\n    result_udata = PyUnicode_AS_UNICODE(result_uval);\n#endif\n    char_pos = 0;\n    for (i=0; i < value_count; i++) {\n        int ukind;\n        Py_ssize_t ulength;\n        void *udata;\n        PyObject *uval = PyTuple_GET_ITEM(value_tuple, i);\n        if (unlikely(__Pyx_PyUnicode_READY(uval)))\n            goto bad;\n        ulength = __Pyx_PyUnicode_GET_LENGTH(uval);\n        if (unlikely(!ulength))\n            continue;\n        if (unlikely(char_pos + ulength < 0))\n            goto overflow;\n        ukind = __Pyx_PyUnicode_KIND(uval);\n        udata = __Pyx_PyUnicode_DATA(uval);\n        if (!CYTHON_PEP393_ENABLED || ukind == result_ukind) {\n            memcpy((char *)result_udata + char_pos * result_ukind, udata, (size_t) (ulength * result_ukind));\n        } else {\n            #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030300F0 || defined(_PyUnicode_FastCopyCharacters)\n            _PyUnicode_FastCopyCharacters(result_uval, char_pos, uval, 0, ulength);\n            #else\n            Py_ssize_t j;\n            for (j=0; j < ulength; j++) {\n                Py_UCS4 uchar = __Pyx_PyUnicode_READ(ukind, udata, j);\n                __Pyx_PyUnicode_WRITE(result_ukind, result_udata, char_pos+j, uchar);\n            }\n            #endif\n        }\n        char_pos += ulength;\n    }\n    return result_uval;\noverflow:\n    PyErr_SetString(PyExc_OverflowError, "join() result is too long for a Python string");\nbad:\n    Py_DECREF(result_uval);\n    return NULL;\n#else\n    result_ulength++;\n    value_count++;\n    return PyUnicode_Join(__pyx_empty_unicode, value_tuple);\n#endif\n}\n\n/* GetTopmostException */\n#if CYTHON_USE_EXC_INFO_STACK\nstatic _PyErr_StackItem *\n__Pyx_PyErr_GetTopmostException(PyThreadState *tstate)\n{\n    _PyErr_StackItem *exc_info = tstate->exc_info;\n    while ((exc_info->exc_type == NULL || exc_info->exc_type == Py_None) &&\n           exc_info->previous_item != NULL)\n    {\n        exc_info = exc_info->previous_item;\n    }\n    return exc_info;\n}\n#endif\n\n/* SaveResetException */\n#if CYTHON_FAST_THREAD_STATE\nstatic CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {\n    #if CYTHON_USE_EXC_INFO_STACK\n    _PyErr_StackItem *exc_info = __Pyx_PyErr_GetTopmostException(tstate);\n    *type = exc_info->exc_type;\n    *value = exc_info->exc_value;\n    *tb = exc_info->exc_traceback;\n    #else\n    *type = tstate->exc_type;\n    *value = tstate->exc_value;\n    *tb = tstate->exc_traceback;\n    #endif\n    Py_XINCREF(*type);\n    Py_XINCREF(*value);\n    Py_XINCREF(*tb);\n}\nstatic CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {\n    PyObject *tmp_type, *tmp_value, *tmp_tb;\n    #if CYTHON_USE_EXC_INFO_STACK\n    _PyErr_StackItem *exc_info = tstate->exc_info;\n    tmp_type = exc_info->exc_type;\n    tmp_value = exc_info->exc_value;\n    tmp_tb = exc_info->exc_traceback;\n    exc_info->exc_type = type;\n    exc_info->exc_value = value;\n    exc_info->exc_traceback = tb;\n    #else\n    tmp_type = tstate->exc_type;\n    tmp_value = tstate->exc_value;\n    tmp_tb = tstate->exc_traceback;\n    tstate->exc_type = type;\n    tstate->exc_value = value;\n    tstate->exc_traceback = tb;\n    #endif\n    Py_XDECREF(tmp_type);\n    Py_XDECREF(tmp_value);\n    Py_XDECREF(tmp_tb);\n}\n#endif\n\n/* PyErrExceptionMatches */\n#if CYTHON_FAST_THREAD_STATE\nstatic int __Pyx_PyErr_ExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {\n    Py_ssize_t i, n;\n    n = PyTuple_GET_SIZE(tuple);\n#if PY_MAJOR_VERSION >= 3\n    for (i=0; i<n; i++) {\n        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;\n    }\n#endif\n    for (i=0; i<n; i++) {\n        if (__Pyx_PyErr_GivenExceptionMatches(exc_type, PyTuple_GET_ITEM(tuple, i))) return 1;\n    }\n    return 0;\n}\nstatic CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err) {\n    PyObject *exc_type = tstate->curexc_type;\n    if (exc_type == err) return 1;\n    if (unlikely(!exc_type)) return 0;\n    if (unlikely(PyTuple_Check(err)))\n        return __Pyx_PyErr_ExceptionMatchesTuple(exc_type, err);\n    return __Pyx_PyErr_GivenExceptionMatches(exc_type, err);\n}\n#endif\n\n/* PyErrFetchRestore */\n#if CYTHON_FAST_THREAD_STATE\nstatic CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {\n    PyObject *tmp_type, *tmp_value, *tmp_tb;\n    tmp_type = tstate->curexc_type;\n    tmp_value = tstate->curexc_value;\n    tmp_tb = tstate->curexc_traceback;\n    tstate->curexc_type = type;\n    tstate->curexc_value = value;\n    tstate->curexc_traceback = tb;\n    Py_XDECREF(tmp_type);\n    Py_XDECREF(tmp_value);\n    Py_XDECREF(tmp_tb);\n}\nstatic CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {\n    *type = tstate->curexc_type;\n    *value = tstate->curexc_value;\n    *tb = tstate->curexc_traceback;\n    tstate->curexc_type = 0;\n    tstate->curexc_value = 0;\n    tstate->curexc_traceback = 0;\n}\n#endif\n\n/* Import */\nstatic PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level) {\n    PyObject *empty_list = 0;\n    PyObject *module = 0;\n    PyObject *global_dict = 0;\n    PyObject *empty_dict = 0;\n    PyObject *list;\n    #if PY_MAJOR_VERSION < 3\n    PyObject *py_import;\n    py_import = __Pyx_PyObject_GetAttrStr(__pyx_b, __pyx_n_s_import);\n    if (!py_import)\n        goto bad;\n    #endif\n    if (from_list)\n        list = from_list;\n    else {\n        empty_list = PyList_New(0);\n        if (!empty_list)\n            goto bad;\n        list = empty_list;\n    }\n    global_dict = PyModule_GetDict(__pyx_m);\n    if (!global_dict)\n        goto bad;\n    empty_dict = PyDict_New();\n    if (!empty_dict)\n        goto bad;\n    {\n        #if PY_MAJOR_VERSION >= 3\n        if (level == -1) {\n            if ((1) && (strchr(__Pyx_MODULE_NAME, \'.\'))) {\n                module = PyImport_ImportModuleLevelObject(\n                    name, global_dict, empty_dict, list, 1);\n                if (!module) {\n                    if (!PyErr_ExceptionMatches(PyExc_ImportError))\n                        goto bad;\n                    PyErr_Clear();\n                }\n            }\n            level = 0;\n        }\n        #endif\n        if (!module) {\n            #if PY_MAJOR_VERSION < 3\n            PyObject *py_level = PyInt_FromLong(level);\n            if (!py_level)\n                goto bad;\n            module = PyObject_CallFunctionObjArgs(py_import,\n                name, global_dict, empty_dict, list, py_level, (PyObject *)NULL);\n            Py_DECREF(py_level);\n            #else\n            module = PyImport_ImportModuleLevelObject(\n                name, global_dict, empty_dict, list, level);\n            #endif\n        }\n    }\nbad:\n    #if PY_MAJOR_VERSION < 3\n    Py_XDECREF(py_import);\n    #endif\n    Py_XDECREF(empty_list);\n    Py_XDECREF(empty_dict);\n    return module;\n}\n\n/* FetchCommonType */\nstatic PyTypeObject* __Pyx_FetchCommonType(PyTypeObject* type) {\n    PyObject* fake_module;\n    PyTypeObject* cached_type = NULL;\n    fake_module = PyImport_AddModule((char*) "_cython_" CYTHON_ABI);\n    if (!fake_module) return NULL;\n    Py_INCREF(fake_module);\n    cached_type = (PyTypeObject*) PyObject_GetAttrString(fake_module, type->tp_name);\n    if (cached_type) {\n        if (!PyType_Check((PyObject*)cached_type)) {\n            PyErr_Format(PyExc_TypeError,\n                "Shared Cython type %.200s is not a type object",\n                type->tp_name);\n            goto bad;\n        }\n        if (cached_type->tp_basicsize != type->tp_basicsize) {\n            PyErr_Format(PyExc_TypeError,\n                "Shared Cython type %.200s has the wrong size, try recompiling",\n                type->tp_name);\n            goto bad;\n        }\n    } else {\n        if (!PyErr_ExceptionMatches(PyExc_AttributeError)) goto bad;\n        PyErr_Clear();\n        if (PyType_Ready(type) < 0) goto bad;\n        if (PyObject_SetAttrString(fake_module, type->tp_name, (PyObject*) type) < 0)\n            goto bad;\n        Py_INCREF(type);\n        cached_type = type;\n    }\ndone:\n    Py_DECREF(fake_module);\n    return cached_type;\nbad:\n    Py_XDECREF(cached_type);\n    cached_type = NULL;\n    goto done;\n}\n\n/* CythonFunctionShared */\n#include <structmember.h>\nstatic PyObject *\n__Pyx_CyFunction_get_doc(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *closure)\n{\n    if (unlikely(op->func_doc == NULL)) {\n        if (op->func.m_ml->ml_doc) {\n#if PY_MAJOR_VERSION >= 3\n            op->func_doc = PyUnicode_FromString(op->func.m_ml->ml_doc);\n#else\n            op->func_doc = PyString_FromString(op->func.m_ml->ml_doc);\n#endif\n            if (unlikely(op->func_doc == NULL))\n                return NULL;\n        } else {\n            Py_INCREF(Py_None);\n            return Py_None;\n        }\n    }\n    Py_INCREF(op->func_doc);\n    return op->func_doc;\n}\nstatic int\n__Pyx_CyFunction_set_doc(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)\n{\n    PyObject *tmp = op->func_doc;\n    if (value == NULL) {\n        value = Py_None;\n    }\n    Py_INCREF(value);\n    op->func_doc = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_name(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    if (unlikely(op->func_name == NULL)) {\n#if PY_MAJOR_VERSION >= 3\n        op->func_name = PyUnicode_InternFromString(op->func.m_ml->ml_name);\n#else\n        op->func_name = PyString_InternFromString(op->func.m_ml->ml_name);\n#endif\n        if (unlikely(op->func_name == NULL))\n            return NULL;\n    }\n    Py_INCREF(op->func_name);\n    return op->func_name;\n}\nstatic int\n__Pyx_CyFunction_set_name(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)\n{\n    PyObject *tmp;\n#if PY_MAJOR_VERSION >= 3\n    if (unlikely(value == NULL || !PyUnicode_Check(value)))\n#else\n    if (unlikely(value == NULL || !PyString_Check(value)))\n#endif\n    {\n        PyErr_SetString(PyExc_TypeError,\n                        "__name__ must be set to a string object");\n        return -1;\n    }\n    tmp = op->func_name;\n    Py_INCREF(value);\n    op->func_name = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_qualname(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    Py_INCREF(op->func_qualname);\n    return op->func_qualname;\n}\nstatic int\n__Pyx_CyFunction_set_qualname(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)\n{\n    PyObject *tmp;\n#if PY_MAJOR_VERSION >= 3\n    if (unlikely(value == NULL || !PyUnicode_Check(value)))\n#else\n    if (unlikely(value == NULL || !PyString_Check(value)))\n#endif\n    {\n        PyErr_SetString(PyExc_TypeError,\n                        "__qualname__ must be set to a string object");\n        return -1;\n    }\n    tmp = op->func_qualname;\n    Py_INCREF(value);\n    op->func_qualname = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_self(__pyx_CyFunctionObject *m, CYTHON_UNUSED void *closure)\n{\n    PyObject *self;\n    self = m->func_closure;\n    if (self == NULL)\n        self = Py_None;\n    Py_INCREF(self);\n    return self;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_dict(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    if (unlikely(op->func_dict == NULL)) {\n        op->func_dict = PyDict_New();\n        if (unlikely(op->func_dict == NULL))\n            return NULL;\n    }\n    Py_INCREF(op->func_dict);\n    return op->func_dict;\n}\nstatic int\n__Pyx_CyFunction_set_dict(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)\n{\n    PyObject *tmp;\n    if (unlikely(value == NULL)) {\n        PyErr_SetString(PyExc_TypeError,\n               "function\'s dictionary may not be deleted");\n        return -1;\n    }\n    if (unlikely(!PyDict_Check(value))) {\n        PyErr_SetString(PyExc_TypeError,\n               "setting function\'s dictionary to a non-dict");\n        return -1;\n    }\n    tmp = op->func_dict;\n    Py_INCREF(value);\n    op->func_dict = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_globals(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    Py_INCREF(op->func_globals);\n    return op->func_globals;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_closure(CYTHON_UNUSED __pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    Py_INCREF(Py_None);\n    return Py_None;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_code(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)\n{\n    PyObject* result = (op->func_code) ? op->func_code : Py_None;\n    Py_INCREF(result);\n    return result;\n}\nstatic int\n__Pyx_CyFunction_init_defaults(__pyx_CyFunctionObject *op) {\n    int result = 0;\n    PyObject *res = op->defaults_getter((PyObject *) op);\n    if (unlikely(!res))\n        return -1;\n    #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n    op->defaults_tuple = PyTuple_GET_ITEM(res, 0);\n    Py_INCREF(op->defaults_tuple);\n    op->defaults_kwdict = PyTuple_GET_ITEM(res, 1);\n    Py_INCREF(op->defaults_kwdict);\n    #else\n    op->defaults_tuple = PySequence_ITEM(res, 0);\n    if (unlikely(!op->defaults_tuple)) result = -1;\n    else {\n        op->defaults_kwdict = PySequence_ITEM(res, 1);\n        if (unlikely(!op->defaults_kwdict)) result = -1;\n    }\n    #endif\n    Py_DECREF(res);\n    return result;\n}\nstatic int\n__Pyx_CyFunction_set_defaults(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {\n    PyObject* tmp;\n    if (!value) {\n        value = Py_None;\n    } else if (value != Py_None && !PyTuple_Check(value)) {\n        PyErr_SetString(PyExc_TypeError,\n                        "__defaults__ must be set to a tuple object");\n        return -1;\n    }\n    Py_INCREF(value);\n    tmp = op->defaults_tuple;\n    op->defaults_tuple = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_defaults(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {\n    PyObject* result = op->defaults_tuple;\n    if (unlikely(!result)) {\n        if (op->defaults_getter) {\n            if (__Pyx_CyFunction_init_defaults(op) < 0) return NULL;\n            result = op->defaults_tuple;\n        } else {\n            result = Py_None;\n        }\n    }\n    Py_INCREF(result);\n    return result;\n}\nstatic int\n__Pyx_CyFunction_set_kwdefaults(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {\n    PyObject* tmp;\n    if (!value) {\n        value = Py_None;\n    } else if (value != Py_None && !PyDict_Check(value)) {\n        PyErr_SetString(PyExc_TypeError,\n                        "__kwdefaults__ must be set to a dict object");\n        return -1;\n    }\n    Py_INCREF(value);\n    tmp = op->defaults_kwdict;\n    op->defaults_kwdict = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_kwdefaults(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {\n    PyObject* result = op->defaults_kwdict;\n    if (unlikely(!result)) {\n        if (op->defaults_getter) {\n            if (__Pyx_CyFunction_init_defaults(op) < 0) return NULL;\n            result = op->defaults_kwdict;\n        } else {\n            result = Py_None;\n        }\n    }\n    Py_INCREF(result);\n    return result;\n}\nstatic int\n__Pyx_CyFunction_set_annotations(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {\n    PyObject* tmp;\n    if (!value || value == Py_None) {\n        value = NULL;\n    } else if (!PyDict_Check(value)) {\n        PyErr_SetString(PyExc_TypeError,\n                        "__annotations__ must be set to a dict object");\n        return -1;\n    }\n    Py_XINCREF(value);\n    tmp = op->func_annotations;\n    op->func_annotations = value;\n    Py_XDECREF(tmp);\n    return 0;\n}\nstatic PyObject *\n__Pyx_CyFunction_get_annotations(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {\n    PyObject* result = op->func_annotations;\n    if (unlikely(!result)) {\n        result = PyDict_New();\n        if (unlikely(!result)) return NULL;\n        op->func_annotations = result;\n    }\n    Py_INCREF(result);\n    return result;\n}\nstatic PyGetSetDef __pyx_CyFunction_getsets[] = {\n    {(char *) "func_doc", (getter)__Pyx_CyFunction_get_doc, (setter)__Pyx_CyFunction_set_doc, 0, 0},\n    {(char *) "__doc__",  (getter)__Pyx_CyFunction_get_doc, (setter)__Pyx_CyFunction_set_doc, 0, 0},\n    {(char *) "func_name", (getter)__Pyx_CyFunction_get_name, (setter)__Pyx_CyFunction_set_name, 0, 0},\n    {(char *) "__name__", (getter)__Pyx_CyFunction_get_name, (setter)__Pyx_CyFunction_set_name, 0, 0},\n    {(char *) "__qualname__", (getter)__Pyx_CyFunction_get_qualname, (setter)__Pyx_CyFunction_set_qualname, 0, 0},\n    {(char *) "__self__", (getter)__Pyx_CyFunction_get_self, 0, 0, 0},\n    {(char *) "func_dict", (getter)__Pyx_CyFunction_get_dict, (setter)__Pyx_CyFunction_set_dict, 0, 0},\n    {(char *) "__dict__", (getter)__Pyx_CyFunction_get_dict, (setter)__Pyx_CyFunction_set_dict, 0, 0},\n    {(char *) "func_globals", (getter)__Pyx_CyFunction_get_globals, 0, 0, 0},\n    {(char *) "__globals__", (getter)__Pyx_CyFunction_get_globals, 0, 0, 0},\n    {(char *) "func_closure", (getter)__Pyx_CyFunction_get_closure, 0, 0, 0},\n    {(char *) "__closure__", (getter)__Pyx_CyFunction_get_closure, 0, 0, 0},\n    {(char *) "func_code", (getter)__Pyx_CyFunction_get_code, 0, 0, 0},\n    {(char *) "__code__", (getter)__Pyx_CyFunction_get_code, 0, 0, 0},\n    {(char *) "func_defaults", (getter)__Pyx_CyFunction_get_defaults, (setter)__Pyx_CyFunction_set_defaults, 0, 0},\n    {(char *) "__defaults__", (getter)__Pyx_CyFunction_get_defaults, (setter)__Pyx_CyFunction_set_defaults, 0, 0},\n    {(char *) "__kwdefaults__", (getter)__Pyx_CyFunction_get_kwdefaults, (setter)__Pyx_CyFunction_set_kwdefaults, 0, 0},\n    {(char *) "__annotations__", (getter)__Pyx_CyFunction_get_annotations, (setter)__Pyx_CyFunction_set_annotations, 0, 0},\n    {0, 0, 0, 0, 0}\n};\nstatic PyMemberDef __pyx_CyFunction_members[] = {\n    {(char *) "__module__", T_OBJECT, offsetof(PyCFunctionObject, m_module), PY_WRITE_RESTRICTED, 0},\n    {0, 0, 0,  0, 0}\n};\nstatic PyObject *\n__Pyx_CyFunction_reduce(__pyx_CyFunctionObject *m, CYTHON_UNUSED PyObject *args)\n{\n#if PY_MAJOR_VERSION >= 3\n    Py_INCREF(m->func_qualname);\n    return m->func_qualname;\n#else\n    return PyString_FromString(m->func.m_ml->ml_name);\n#endif\n}\nstatic PyMethodDef __pyx_CyFunction_methods[] = {\n    {"__reduce__", (PyCFunction)__Pyx_CyFunction_reduce, METH_VARARGS, 0},\n    {0, 0, 0, 0}\n};\n#if PY_VERSION_HEX < 0x030500A0\n#define __Pyx_CyFunction_weakreflist(cyfunc) ((cyfunc)->func_weakreflist)\n#else\n#define __Pyx_CyFunction_weakreflist(cyfunc) ((cyfunc)->func.m_weakreflist)\n#endif\nstatic PyObject *__Pyx_CyFunction_Init(__pyx_CyFunctionObject *op, PyMethodDef *ml, int flags, PyObject* qualname,\n                                       PyObject *closure, PyObject *module, PyObject* globals, PyObject* code) {\n    if (unlikely(op == NULL))\n        return NULL;\n    op->flags = flags;\n    __Pyx_CyFunction_weakreflist(op) = NULL;\n    op->func.m_ml = ml;\n    op->func.m_self = (PyObject *) op;\n    Py_XINCREF(closure);\n    op->func_closure = closure;\n    Py_XINCREF(module);\n    op->func.m_module = module;\n    op->func_dict = NULL;\n    op->func_name = NULL;\n    Py_INCREF(qualname);\n    op->func_qualname = qualname;\n    op->func_doc = NULL;\n    op->func_classobj = NULL;\n    op->func_globals = globals;\n    Py_INCREF(op->func_globals);\n    Py_XINCREF(code);\n    op->func_code = code;\n    op->defaults_pyobjects = 0;\n    op->defaults_size = 0;\n    op->defaults = NULL;\n    op->defaults_tuple = NULL;\n    op->defaults_kwdict = NULL;\n    op->defaults_getter = NULL;\n    op->func_annotations = NULL;\n    return (PyObject *) op;\n}\nstatic int\n__Pyx_CyFunction_clear(__pyx_CyFunctionObject *m)\n{\n    Py_CLEAR(m->func_closure);\n    Py_CLEAR(m->func.m_module);\n    Py_CLEAR(m->func_dict);\n    Py_CLEAR(m->func_name);\n    Py_CLEAR(m->func_qualname);\n    Py_CLEAR(m->func_doc);\n    Py_CLEAR(m->func_globals);\n    Py_CLEAR(m->func_code);\n    Py_CLEAR(m->func_classobj);\n    Py_CLEAR(m->defaults_tuple);\n    Py_CLEAR(m->defaults_kwdict);\n    Py_CLEAR(m->func_annotations);\n    if (m->defaults) {\n        PyObject **pydefaults = __Pyx_CyFunction_Defaults(PyObject *, m);\n        int i;\n        for (i = 0; i < m->defaults_pyobjects; i++)\n            Py_XDECREF(pydefaults[i]);\n        PyObject_Free(m->defaults);\n        m->defaults = NULL;\n    }\n    return 0;\n}\nstatic void __Pyx__CyFunction_dealloc(__pyx_CyFunctionObject *m)\n{\n    if (__Pyx_CyFunction_weakreflist(m) != NULL)\n        PyObject_ClearWeakRefs((PyObject *) m);\n    __Pyx_CyFunction_clear(m);\n    PyObject_GC_Del(m);\n}\nstatic void __Pyx_CyFunction_dealloc(__pyx_CyFunctionObject *m)\n{\n    PyObject_GC_UnTrack(m);\n    __Pyx__CyFunction_dealloc(m);\n}\nstatic int __Pyx_CyFunction_traverse(__pyx_CyFunctionObject *m, visitproc visit, void *arg)\n{\n    Py_VISIT(m->func_closure);\n    Py_VISIT(m->func.m_module);\n    Py_VISIT(m->func_dict);\n    Py_VISIT(m->func_name);\n    Py_VISIT(m->func_qualname);\n    Py_VISIT(m->func_doc);\n    Py_VISIT(m->func_globals);\n    Py_VISIT(m->func_code);\n    Py_VISIT(m->func_classobj);\n    Py_VISIT(m->defaults_tuple);\n    Py_VISIT(m->defaults_kwdict);\n    if (m->defaults) {\n        PyObject **pydefaults = __Pyx_CyFunction_Defaults(PyObject *, m);\n        int i;\n        for (i = 0; i < m->defaults_pyobjects; i++)\n            Py_VISIT(pydefaults[i]);\n    }\n    return 0;\n}\nstatic PyObject *__Pyx_CyFunction_descr_get(PyObject *func, PyObject *obj, PyObject *type)\n{\n#if PY_MAJOR_VERSION < 3\n    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;\n    if (m->flags & __Pyx_CYFUNCTION_STATICMETHOD) {\n        Py_INCREF(func);\n        return func;\n    }\n    if (m->flags & __Pyx_CYFUNCTION_CLASSMETHOD) {\n        if (type == NULL)\n            type = (PyObject *)(Py_TYPE(obj));\n        return __Pyx_PyMethod_New(func, type, (PyObject *)(Py_TYPE(type)));\n    }\n    if (obj == Py_None)\n        obj = NULL;\n#endif\n    return __Pyx_PyMethod_New(func, obj, type);\n}\nstatic PyObject*\n__Pyx_CyFunction_repr(__pyx_CyFunctionObject *op)\n{\n#if PY_MAJOR_VERSION >= 3\n    return PyUnicode_FromFormat("<cyfunction %U at %p>",\n                                op->func_qualname, (void *)op);\n#else\n    return PyString_FromFormat("<cyfunction %s at %p>",\n                               PyString_AsString(op->func_qualname), (void *)op);\n#endif\n}\nstatic PyObject * __Pyx_CyFunction_CallMethod(PyObject *func, PyObject *self, PyObject *arg, PyObject *kw) {\n    PyCFunctionObject* f = (PyCFunctionObject*)func;\n    PyCFunction meth = f->m_ml->ml_meth;\n    Py_ssize_t size;\n    switch (f->m_ml->ml_flags & (METH_VARARGS | METH_KEYWORDS | METH_NOARGS | METH_O)) {\n    case METH_VARARGS:\n        if (likely(kw == NULL || PyDict_Size(kw) == 0))\n            return (*meth)(self, arg);\n        break;\n    case METH_VARARGS | METH_KEYWORDS:\n        return (*(PyCFunctionWithKeywords)(void*)meth)(self, arg, kw);\n    case METH_NOARGS:\n        if (likely(kw == NULL || PyDict_Size(kw) == 0)) {\n            size = PyTuple_GET_SIZE(arg);\n            if (likely(size == 0))\n                return (*meth)(self, NULL);\n            PyErr_Format(PyExc_TypeError,\n                "%.200s() takes no arguments (%" CYTHON_FORMAT_SSIZE_T "d given)",\n                f->m_ml->ml_name, size);\n            return NULL;\n        }\n        break;\n    case METH_O:\n        if (likely(kw == NULL || PyDict_Size(kw) == 0)) {\n            size = PyTuple_GET_SIZE(arg);\n            if (likely(size == 1)) {\n                PyObject *result, *arg0;\n                #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS\n                arg0 = PyTuple_GET_ITEM(arg, 0);\n                #else\n                arg0 = PySequence_ITEM(arg, 0); if (unlikely(!arg0)) return NULL;\n                #endif\n                result = (*meth)(self, arg0);\n                #if !(CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS)\n                Py_DECREF(arg0);\n                #endif\n                return result;\n            }\n            PyErr_Format(PyExc_TypeError,\n                "%.200s() takes exactly one argument (%" CYTHON_FORMAT_SSIZE_T "d given)",\n                f->m_ml->ml_name, size);\n            return NULL;\n        }\n        break;\n    default:\n        PyErr_SetString(PyExc_SystemError, "Bad call flags in "\n                        "__Pyx_CyFunction_Call. METH_OLDARGS is no "\n                        "longer supported!");\n        return NULL;\n    }\n    PyErr_Format(PyExc_TypeError, "%.200s() takes no keyword arguments",\n                 f->m_ml->ml_name);\n    return NULL;\n}\nstatic CYTHON_INLINE PyObject *__Pyx_CyFunction_Call(PyObject *func, PyObject *arg, PyObject *kw) {\n    return __Pyx_CyFunction_CallMethod(func, ((PyCFunctionObject*)func)->m_self, arg, kw);\n}\nstatic PyObject *__Pyx_CyFunction_CallAsMethod(PyObject *func, PyObject *args, PyObject *kw) {\n    PyObject *result;\n    __pyx_CyFunctionObject *cyfunc = (__pyx_CyFunctionObject *) func;\n    if ((cyfunc->flags & __Pyx_CYFUNCTION_CCLASS) && !(cyfunc->flags & __Pyx_CYFUNCTION_STATICMETHOD)) {\n        Py_ssize_t argc;\n        PyObject *new_args;\n        PyObject *self;\n        argc = PyTuple_GET_SIZE(args);\n        new_args = PyTuple_GetSlice(args, 1, argc);\n        if (unlikely(!new_args))\n            return NULL;\n        self = PyTuple_GetItem(args, 0);\n        if (unlikely(!self)) {\n            Py_DECREF(new_args);\n#if PY_MAJOR_VERSION > 2\n            PyErr_Format(PyExc_TypeError,\n                         "unbound method %.200S() needs an argument",\n                         cyfunc->func_qualname);\n#else\n            PyErr_SetString(PyExc_TypeError,\n                            "unbound method needs an argument");\n#endif\n            return NULL;\n        }\n        result = __Pyx_CyFunction_CallMethod(func, self, new_args, kw);\n        Py_DECREF(new_args);\n    } else {\n        result = __Pyx_CyFunction_Call(func, args, kw);\n    }\n    return result;\n}\nstatic PyTypeObject __pyx_CyFunctionType_type = {\n    PyVarObject_HEAD_INIT(0, 0)\n    "cython_function_or_method",\n    sizeof(__pyx_CyFunctionObject),\n    0,\n    (destructor) __Pyx_CyFunction_dealloc,\n    0,\n    0,\n    0,\n#if PY_MAJOR_VERSION < 3\n    0,\n#else\n    0,\n#endif\n    (reprfunc) __Pyx_CyFunction_repr,\n    0,\n    0,\n    0,\n    0,\n    __Pyx_CyFunction_CallAsMethod,\n    0,\n    0,\n    0,\n    0,\n    Py_TPFLAGS_DEFAULT | Py_TPFLAGS_HAVE_GC,\n    0,\n    (traverseproc) __Pyx_CyFunction_traverse,\n    (inquiry) __Pyx_CyFunction_clear,\n    0,\n#if PY_VERSION_HEX < 0x030500A0\n    offsetof(__pyx_CyFunctionObject, func_weakreflist),\n#else\n    offsetof(PyCFunctionObject, m_weakreflist),\n#endif\n    0,\n    0,\n    __pyx_CyFunction_methods,\n    __pyx_CyFunction_members,\n    __pyx_CyFunction_getsets,\n    0,\n    0,\n    __Pyx_CyFunction_descr_get,\n    0,\n    offsetof(__pyx_CyFunctionObject, func_dict),\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n#if PY_VERSION_HEX >= 0x030400a1\n    0,\n#endif\n#if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)\n    0,\n#endif\n#if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000\n    0,\n#endif\n#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000\n    0,\n#endif\n};\nstatic int __pyx_CyFunction_init(void) {\n    __pyx_CyFunctionType = __Pyx_FetchCommonType(&__pyx_CyFunctionType_type);\n    if (unlikely(__pyx_CyFunctionType == NULL)) {\n        return -1;\n    }\n    return 0;\n}\nstatic CYTHON_INLINE void *__Pyx_CyFunction_InitDefaults(PyObject *func, size_t size, int pyobjects) {\n    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;\n    m->defaults = PyObject_Malloc(size);\n    if (unlikely(!m->defaults))\n        return PyErr_NoMemory();\n    memset(m->defaults, 0, size);\n    m->defaults_pyobjects = pyobjects;\n    m->defaults_size = size;\n    return m->defaults;\n}\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsTuple(PyObject *func, PyObject *tuple) {\n    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;\n    m->defaults_tuple = tuple;\n    Py_INCREF(tuple);\n}\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsKwDict(PyObject *func, PyObject *dict) {\n    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;\n    m->defaults_kwdict = dict;\n    Py_INCREF(dict);\n}\nstatic CYTHON_INLINE void __Pyx_CyFunction_SetAnnotationsDict(PyObject *func, PyObject *dict) {\n    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;\n    m->func_annotations = dict;\n    Py_INCREF(dict);\n}\n\n/* CythonFunction */\nstatic PyObject *__Pyx_CyFunction_New(PyMethodDef *ml, int flags, PyObject* qualname,\n                                      PyObject *closure, PyObject *module, PyObject* globals, PyObject* code) {\n    PyObject *op = __Pyx_CyFunction_Init(\n        PyObject_GC_New(__pyx_CyFunctionObject, __pyx_CyFunctionType),\n        ml, flags, qualname, closure, module, globals, code\n    );\n    if (likely(op)) {\n        PyObject_GC_Track(op);\n    }\n    return op;\n}\n\n/* BytesEquals */\nstatic CYTHON_INLINE int __Pyx_PyBytes_Equals(PyObject* s1, PyObject* s2, int equals) {\n#if CYTHON_COMPILING_IN_PYPY\n    return PyObject_RichCompareBool(s1, s2, equals);\n#else\n    if (s1 == s2) {\n        return (equals == Py_EQ);\n    } else if (PyBytes_CheckExact(s1) & PyBytes_CheckExact(s2)) {\n        const char *ps1, *ps2;\n        Py_ssize_t length = PyBytes_GET_SIZE(s1);\n        if (length != PyBytes_GET_SIZE(s2))\n            return (equals == Py_NE);\n        ps1 = PyBytes_AS_STRING(s1);\n        ps2 = PyBytes_AS_STRING(s2);\n        if (ps1[0] != ps2[0]) {\n            return (equals == Py_NE);\n        } else if (length == 1) {\n            return (equals == Py_EQ);\n        } else {\n            int result;\n#if CYTHON_USE_UNICODE_INTERNALS && (PY_VERSION_HEX < 0x030B0000)\n            Py_hash_t hash1, hash2;\n            hash1 = ((PyBytesObject*)s1)->ob_shash;\n            hash2 = ((PyBytesObject*)s2)->ob_shash;\n            if (hash1 != hash2 && hash1 != -1 && hash2 != -1) {\n                return (equals == Py_NE);\n            }\n#endif\n            result = memcmp(ps1, ps2, (size_t)length);\n            return (equals == Py_EQ) ? (result == 0) : (result != 0);\n        }\n    } else if ((s1 == Py_None) & PyBytes_CheckExact(s2)) {\n        return (equals == Py_NE);\n    } else if ((s2 == Py_None) & PyBytes_CheckExact(s1)) {\n        return (equals == Py_NE);\n    } else {\n        int result;\n        PyObject* py_result = PyObject_RichCompare(s1, s2, equals);\n        if (!py_result)\n            return -1;\n        result = __Pyx_PyObject_IsTrue(py_result);\n        Py_DECREF(py_result);\n        return result;\n    }\n#endif\n}\n\n/* UnicodeEquals */\nstatic CYTHON_INLINE int __Pyx_PyUnicode_Equals(PyObject* s1, PyObject* s2, int equals) {\n#if CYTHON_COMPILING_IN_PYPY\n    return PyObject_RichCompareBool(s1, s2, equals);\n#else\n#if PY_MAJOR_VERSION < 3\n    PyObject* owned_ref = NULL;\n#endif\n    int s1_is_unicode, s2_is_unicode;\n    if (s1 == s2) {\n        goto return_eq;\n    }\n    s1_is_unicode = PyUnicode_CheckExact(s1);\n    s2_is_unicode = PyUnicode_CheckExact(s2);\n#if PY_MAJOR_VERSION < 3\n    if ((s1_is_unicode & (!s2_is_unicode)) && PyString_CheckExact(s2)) {\n        owned_ref = PyUnicode_FromObject(s2);\n        if (unlikely(!owned_ref))\n            return -1;\n        s2 = owned_ref;\n        s2_is_unicode = 1;\n    } else if ((s2_is_unicode & (!s1_is_unicode)) && PyString_CheckExact(s1)) {\n        owned_ref = PyUnicode_FromObject(s1);\n        if (unlikely(!owned_ref))\n            return -1;\n        s1 = owned_ref;\n        s1_is_unicode = 1;\n    } else if (((!s2_is_unicode) & (!s1_is_unicode))) {\n        return __Pyx_PyBytes_Equals(s1, s2, equals);\n    }\n#endif\n    if (s1_is_unicode & s2_is_unicode) {\n        Py_ssize_t length;\n        int kind;\n        void *data1, *data2;\n        if (unlikely(__Pyx_PyUnicode_READY(s1) < 0) || unlikely(__Pyx_PyUnicode_READY(s2) < 0))\n            return -1;\n        length = __Pyx_PyUnicode_GET_LENGTH(s1);\n        if (length != __Pyx_PyUnicode_GET_LENGTH(s2)) {\n            goto return_ne;\n        }\n#if CYTHON_USE_UNICODE_INTERNALS\n        {\n            Py_hash_t hash1, hash2;\n        #if CYTHON_PEP393_ENABLED\n            hash1 = ((PyASCIIObject*)s1)->hash;\n            hash2 = ((PyASCIIObject*)s2)->hash;\n        #else\n            hash1 = ((PyUnicodeObject*)s1)->hash;\n            hash2 = ((PyUnicodeObject*)s2)->hash;\n        #endif\n            if (hash1 != hash2 && hash1 != -1 && hash2 != -1) {\n                goto return_ne;\n            }\n        }\n#endif\n        kind = __Pyx_PyUnicode_KIND(s1);\n        if (kind != __Pyx_PyUnicode_KIND(s2)) {\n            goto return_ne;\n        }\n        data1 = __Pyx_PyUnicode_DATA(s1);\n        data2 = __Pyx_PyUnicode_DATA(s2);\n        if (__Pyx_PyUnicode_READ(kind, data1, 0) != __Pyx_PyUnicode_READ(kind, data2, 0)) {\n            goto return_ne;\n        } else if (length == 1) {\n            goto return_eq;\n        } else {\n            int result = memcmp(data1, data2, (size_t)(length * kind));\n            #if PY_MAJOR_VERSION < 3\n            Py_XDECREF(owned_ref);\n            #endif\n            return (equals == Py_EQ) ? (result == 0) : (result != 0);\n        }\n    } else if ((s1 == Py_None) & s2_is_unicode) {\n        goto return_ne;\n    } else if ((s2 == Py_None) & s1_is_unicode) {\n        goto return_ne;\n    } else {\n        int result;\n        PyObject* py_result = PyObject_RichCompare(s1, s2, equals);\n        #if PY_MAJOR_VERSION < 3\n        Py_XDECREF(owned_ref);\n        #endif\n        if (!py_result)\n            return -1;\n        result = __Pyx_PyObject_IsTrue(py_result);\n        Py_DECREF(py_result);\n        return result;\n    }\nreturn_eq:\n    #if PY_MAJOR_VERSION < 3\n    Py_XDECREF(owned_ref);\n    #endif\n    return (equals == Py_EQ);\nreturn_ne:\n    #if PY_MAJOR_VERSION < 3\n    Py_XDECREF(owned_ref);\n    #endif\n    return (equals == Py_NE);\n#endif\n}\n\n/* PyIntBinop */\n#if !CYTHON_COMPILING_IN_PYPY\nstatic PyObject* __Pyx_PyInt_AddObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, int inplace, int zerodivision_check) {\n    (void)inplace;\n    (void)zerodivision_check;\n    #if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_CheckExact(op1))) {\n        const long b = intval;\n        long x;\n        long a = PyInt_AS_LONG(op1);\n            x = (long)((unsigned long)a + b);\n            if (likely((x^a) >= 0 || (x^b) >= 0))\n                return PyInt_FromLong(x);\n            return PyLong_Type.tp_as_number->nb_add(op1, op2);\n    }\n    #endif\n    #if CYTHON_USE_PYLONG_INTERNALS\n    if (likely(PyLong_CheckExact(op1))) {\n        const long b = intval;\n        long a, x;\n#ifdef HAVE_LONG_LONG\n        const PY_LONG_LONG llb = intval;\n        PY_LONG_LONG lla, llx;\n#endif\n        const digit* digits = ((PyLongObject*)op1)->ob_digit;\n        const Py_ssize_t size = Py_SIZE(op1);\n        if (likely(__Pyx_sst_abs(size) <= 1)) {\n            a = likely(size) ? digits[0] : 0;\n            if (size == -1) a = -a;\n        } else {\n            switch (size) {\n                case -2:\n                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                        a = -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 2:\n                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                        a = (long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case -3:\n                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                        a = -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 3:\n                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                        a = (long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case -4:\n                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                        a = -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 4:\n                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                        a = (long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                default: return PyLong_Type.tp_as_number->nb_add(op1, op2);\n            }\n        }\n                x = a + b;\n            return PyLong_FromLong(x);\n#ifdef HAVE_LONG_LONG\n        long_long:\n                llx = lla + llb;\n            return PyLong_FromLongLong(llx);\n#endif\n        \n        \n    }\n    #endif\n    if (PyFloat_CheckExact(op1)) {\n        const long b = intval;\n        double a = PyFloat_AS_DOUBLE(op1);\n            double result;\n            PyFPE_START_PROTECT("add", return NULL)\n            result = ((double)a) + (double)b;\n            PyFPE_END_PROTECT(result)\n            return PyFloat_FromDouble(result);\n    }\n    return (inplace ? PyNumber_InPlaceAdd : PyNumber_Add)(op1, op2);\n}\n#endif\n\n/* PyIntBinop */\n#if !CYTHON_COMPILING_IN_PYPY\n#if PY_MAJOR_VERSION < 3 || CYTHON_USE_PYLONG_INTERNALS\n#define __Pyx_PyInt_RemainderObjC_ZeroDivisionError(operand)\\\n    if (unlikely(zerodivision_check && ((operand) == 0))) {\\\n        PyErr_SetString(PyExc_ZeroDivisionError, "integer division or modulo by zero");\\\n        return NULL;\\\n    }\n#endif\nstatic PyObject* __Pyx_PyInt_RemainderObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, int inplace, int zerodivision_check) {\n    (void)inplace;\n    (void)zerodivision_check;\n    #if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_CheckExact(op1))) {\n        const long b = intval;\n        long x;\n        long a = PyInt_AS_LONG(op1);\n            __Pyx_PyInt_RemainderObjC_ZeroDivisionError(b)\n            x = a % b;\n            x += ((x != 0) & ((x ^ b) < 0)) * b;\n            return PyInt_FromLong(x);\n    }\n    #endif\n    #if CYTHON_USE_PYLONG_INTERNALS\n    if (likely(PyLong_CheckExact(op1))) {\n        const long b = intval;\n        long a, x;\n#ifdef HAVE_LONG_LONG\n        const PY_LONG_LONG llb = intval;\n        PY_LONG_LONG lla, llx;\n#endif\n        const digit* digits = ((PyLongObject*)op1)->ob_digit;\n        const Py_ssize_t size = Py_SIZE(op1);\n        if (likely(__Pyx_sst_abs(size) <= 1)) {\n            a = likely(size) ? digits[0] : 0;\n            if (size == -1) a = -a;\n        } else {\n            switch (size) {\n                case -2:\n                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                        a = -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 2:\n                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                        a = (long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case -3:\n                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                        a = -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 3:\n                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                        a = (long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case -4:\n                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                        a = -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {\n                        lla = -(PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                case 4:\n                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                        a = (long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));\n                        break;\n#ifdef HAVE_LONG_LONG\n                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {\n                        lla = (PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));\n                        goto long_long;\n#endif\n                    }\n                    CYTHON_FALLTHROUGH;\n                default: return PyLong_Type.tp_as_number->nb_remainder(op1, op2);\n            }\n        }\n                __Pyx_PyInt_RemainderObjC_ZeroDivisionError(b)\n                x = a % b;\n                x += ((x != 0) & ((x ^ b) < 0)) * b;\n            return PyLong_FromLong(x);\n#ifdef HAVE_LONG_LONG\n        long_long:\n                llx = lla % llb;\n                llx += ((llx != 0) & ((llx ^ llb) < 0)) * llb;\n            return PyLong_FromLongLong(llx);\n#endif\n        \n        \n    }\n    #endif\n    return (inplace ? PyNumber_InPlaceRemainder : PyNumber_Remainder)(op1, op2);\n}\n#endif\n\n/* PyIntCompare */\nstatic CYTHON_INLINE PyObject* __Pyx_PyInt_NeObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, CYTHON_UNUSED long inplace) {\n    if (op1 == op2) {\n        Py_RETURN_FALSE;\n    }\n    #if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_CheckExact(op1))) {\n        const long b = intval;\n        long a = PyInt_AS_LONG(op1);\n        if (a != b) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    #endif\n    #if CYTHON_USE_PYLONG_INTERNALS\n    if (likely(PyLong_CheckExact(op1))) {\n        int unequal;\n        unsigned long uintval;\n        Py_ssize_t size = Py_SIZE(op1);\n        const digit* digits = ((PyLongObject*)op1)->ob_digit;\n        if (intval == 0) {\n            if (size != 0) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n        } else if (intval < 0) {\n            if (size >= 0)\n                Py_RETURN_TRUE;\n            intval = -intval;\n            size = -size;\n        } else {\n            if (size <= 0)\n                Py_RETURN_TRUE;\n        }\n        uintval = (unsigned long) intval;\n#if PyLong_SHIFT * 4 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 4)) {\n            unequal = (size != 5) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[3] != ((uintval >> (3 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[4] != ((uintval >> (4 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 3 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 3)) {\n            unequal = (size != 4) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[3] != ((uintval >> (3 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 2 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 2)) {\n            unequal = (size != 3) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK)) | (digits[2] != ((uintval >> (2 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n#if PyLong_SHIFT * 1 < SIZEOF_LONG*8\n        if (uintval >> (PyLong_SHIFT * 1)) {\n            unequal = (size != 2) || (digits[0] != (uintval & (unsigned long) PyLong_MASK))\n                 | (digits[1] != ((uintval >> (1 * PyLong_SHIFT)) & (unsigned long) PyLong_MASK));\n        } else\n#endif\n            unequal = (size != 1) || (((unsigned long) digits[0]) != (uintval & (unsigned long) PyLong_MASK));\n        if (unequal != 0) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    #endif\n    if (PyFloat_CheckExact(op1)) {\n        const long b = intval;\n        double a = PyFloat_AS_DOUBLE(op1);\n        if ((double)a != (double)b) Py_RETURN_TRUE; else Py_RETURN_FALSE;\n    }\n    return (\n        PyObject_RichCompare(op1, op2, Py_NE));\n}\n\n/* PyObjectGetMethod */\nstatic int __Pyx_PyObject_GetMethod(PyObject *obj, PyObject *name, PyObject **method) {\n    PyObject *attr;\n#if CYTHON_UNPACK_METHODS && CYTHON_COMPILING_IN_CPYTHON && CYTHON_USE_PYTYPE_LOOKUP\n    PyTypeObject *tp = Py_TYPE(obj);\n    PyObject *descr;\n    descrgetfunc f = NULL;\n    PyObject **dictptr, *dict;\n    int meth_found = 0;\n    assert (*method == NULL);\n    if (unlikely(tp->tp_getattro != PyObject_GenericGetAttr)) {\n        attr = __Pyx_PyObject_GetAttrStr(obj, name);\n        goto try_unpack;\n    }\n    if (unlikely(tp->tp_dict == NULL) && unlikely(PyType_Ready(tp) < 0)) {\n        return 0;\n    }\n    descr = _PyType_Lookup(tp, name);\n    if (likely(descr != NULL)) {\n        Py_INCREF(descr);\n#if PY_MAJOR_VERSION >= 3\n        #ifdef __Pyx_CyFunction_USED\n        if (likely(PyFunction_Check(descr) || (Py_TYPE(descr) == &PyMethodDescr_Type) || __Pyx_CyFunction_Check(descr)))\n        #else\n        if (likely(PyFunction_Check(descr) || (Py_TYPE(descr) == &PyMethodDescr_Type)))\n        #endif\n#else\n        #ifdef __Pyx_CyFunction_USED\n        if (likely(PyFunction_Check(descr) || __Pyx_CyFunction_Check(descr)))\n        #else\n        if (likely(PyFunction_Check(descr)))\n        #endif\n#endif\n        {\n            meth_found = 1;\n        } else {\n            f = Py_TYPE(descr)->tp_descr_get;\n            if (f != NULL && PyDescr_IsData(descr)) {\n                attr = f(descr, obj, (PyObject *)Py_TYPE(obj));\n                Py_DECREF(descr);\n                goto try_unpack;\n            }\n        }\n    }\n    dictptr = _PyObject_GetDictPtr(obj);\n    if (dictptr != NULL && (dict = *dictptr) != NULL) {\n        Py_INCREF(dict);\n        attr = __Pyx_PyDict_GetItemStr(dict, name);\n        if (attr != NULL) {\n            Py_INCREF(attr);\n            Py_DECREF(dict);\n            Py_XDECREF(descr);\n            goto try_unpack;\n        }\n        Py_DECREF(dict);\n    }\n    if (meth_found) {\n        *method = descr;\n        return 1;\n    }\n    if (f != NULL) {\n        attr = f(descr, obj, (PyObject *)Py_TYPE(obj));\n        Py_DECREF(descr);\n        goto try_unpack;\n    }\n    if (descr != NULL) {\n        *method = descr;\n        return 0;\n    }\n    PyErr_Format(PyExc_AttributeError,\n#if PY_MAJOR_VERSION >= 3\n                 "\'%.50s\' object has no attribute \'%U\'",\n                 tp->tp_name, name);\n#else\n                 "\'%.50s\' object has no attribute \'%.400s\'",\n                 tp->tp_name, PyString_AS_STRING(name));\n#endif\n    return 0;\n#else\n    attr = __Pyx_PyObject_GetAttrStr(obj, name);\n    goto try_unpack;\n#endif\ntry_unpack:\n#if CYTHON_UNPACK_METHODS\n    if (likely(attr) && PyMethod_Check(attr) && likely(PyMethod_GET_SELF(attr) == obj)) {\n        PyObject *function = PyMethod_GET_FUNCTION(attr);\n        Py_INCREF(function);\n        Py_DECREF(attr);\n        *method = function;\n        return 1;\n    }\n#endif\n    *method = attr;\n    return 0;\n}\n\n/* PyObjectCallMethod1 */\nstatic PyObject* __Pyx__PyObject_CallMethod1(PyObject* method, PyObject* arg) {\n    PyObject *result = __Pyx_PyObject_CallOneArg(method, arg);\n    Py_DECREF(method);\n    return result;\n}\nstatic PyObject* __Pyx_PyObject_CallMethod1(PyObject* obj, PyObject* method_name, PyObject* arg) {\n    PyObject *method = NULL, *result;\n    int is_method = __Pyx_PyObject_GetMethod(obj, method_name, &method);\n    if (likely(is_method)) {\n        result = __Pyx_PyObject_Call2Args(method, obj, arg);\n        Py_DECREF(method);\n        return result;\n    }\n    if (unlikely(!method)) return NULL;\n    return __Pyx__PyObject_CallMethod1(method, arg);\n}\n\n/* append */\nstatic CYTHON_INLINE int __Pyx_PyObject_Append(PyObject* L, PyObject* x) {\n    if (likely(PyList_CheckExact(L))) {\n        if (unlikely(__Pyx_PyList_Append(L, x) < 0)) return -1;\n    } else {\n        PyObject* retval = __Pyx_PyObject_CallMethod1(L, __pyx_n_s_append, x);\n        if (unlikely(!retval))\n            return -1;\n        Py_DECREF(retval);\n    }\n    return 0;\n}\n\n/* CLineInTraceback */\n#ifndef CYTHON_CLINE_IN_TRACEBACK\nstatic int __Pyx_CLineForTraceback(CYTHON_UNUSED PyThreadState *tstate, int c_line) {\n    PyObject *use_cline;\n    PyObject *ptype, *pvalue, *ptraceback;\n#if CYTHON_COMPILING_IN_CPYTHON\n    PyObject **cython_runtime_dict;\n#endif\n    if (unlikely(!__pyx_cython_runtime)) {\n        return c_line;\n    }\n    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);\n#if CYTHON_COMPILING_IN_CPYTHON\n    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);\n    if (likely(cython_runtime_dict)) {\n        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(\n            use_cline, *cython_runtime_dict,\n            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))\n    } else\n#endif\n    {\n      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);\n      if (use_cline_obj) {\n        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;\n        Py_DECREF(use_cline_obj);\n      } else {\n        PyErr_Clear();\n        use_cline = NULL;\n      }\n    }\n    if (!use_cline) {\n        c_line = 0;\n        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);\n    }\n    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {\n        c_line = 0;\n    }\n    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);\n    return c_line;\n}\n#endif\n\n/* CodeObjectCache */\nstatic int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {\n    int start = 0, mid = 0, end = count - 1;\n    if (end >= 0 && code_line > entries[end].code_line) {\n        return count;\n    }\n    while (start < end) {\n        mid = start + (end - start) / 2;\n        if (code_line < entries[mid].code_line) {\n            end = mid;\n        } else if (code_line > entries[mid].code_line) {\n             start = mid + 1;\n        } else {\n            return mid;\n        }\n    }\n    if (code_line <= entries[mid].code_line) {\n        return mid;\n    } else {\n        return mid + 1;\n    }\n}\nstatic PyCodeObject *__pyx_find_code_object(int code_line) {\n    PyCodeObject* code_object;\n    int pos;\n    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {\n        return NULL;\n    }\n    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);\n    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {\n        return NULL;\n    }\n    code_object = __pyx_code_cache.entries[pos].code_object;\n    Py_INCREF(code_object);\n    return code_object;\n}\nstatic void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {\n    int pos, i;\n    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;\n    if (unlikely(!code_line)) {\n        return;\n    }\n    if (unlikely(!entries)) {\n        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));\n        if (likely(entries)) {\n            __pyx_code_cache.entries = entries;\n            __pyx_code_cache.max_count = 64;\n            __pyx_code_cache.count = 1;\n            entries[0].code_line = code_line;\n            entries[0].code_object = code_object;\n            Py_INCREF(code_object);\n        }\n        return;\n    }\n    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);\n    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {\n        PyCodeObject* tmp = entries[pos].code_object;\n        entries[pos].code_object = code_object;\n        Py_DECREF(tmp);\n        return;\n    }\n    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {\n        int new_max = __pyx_code_cache.max_count + 64;\n        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(\n            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));\n        if (unlikely(!entries)) {\n            return;\n        }\n        __pyx_code_cache.entries = entries;\n        __pyx_code_cache.max_count = new_max;\n    }\n    for (i=__pyx_code_cache.count; i>pos; i--) {\n        entries[i] = entries[i-1];\n    }\n    entries[pos].code_line = code_line;\n    entries[pos].code_object = code_object;\n    __pyx_code_cache.count++;\n    Py_INCREF(code_object);\n}\n\n/* AddTraceback */\n#include "compile.h"\n#include "frameobject.h"\n#include "traceback.h"\n#if PY_VERSION_HEX >= 0x030b00a6\n  #ifndef Py_BUILD_CORE\n    #define Py_BUILD_CORE 1\n  #endif\n  #include "internal/pycore_frame.h"\n#endif\nstatic PyCodeObject* __Pyx_CreateCodeObjectForTraceback(\n            const char *funcname, int c_line,\n            int py_line, const char *filename) {\n    PyCodeObject *py_code = NULL;\n    PyObject *py_funcname = NULL;\n    #if PY_MAJOR_VERSION < 3\n    PyObject *py_srcfile = NULL;\n    py_srcfile = PyString_FromString(filename);\n    if (!py_srcfile) goto bad;\n    #endif\n    if (c_line) {\n        #if PY_MAJOR_VERSION < 3\n        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);\n        if (!py_funcname) goto bad;\n        #else\n        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);\n        if (!py_funcname) goto bad;\n        funcname = PyUnicode_AsUTF8(py_funcname);\n        if (!funcname) goto bad;\n        #endif\n    }\n    else {\n        #if PY_MAJOR_VERSION < 3\n        py_funcname = PyString_FromString(funcname);\n        if (!py_funcname) goto bad;\n        #endif\n    }\n    #if PY_MAJOR_VERSION < 3\n    py_code = __Pyx_PyCode_New(\n        0,\n        0,\n        0,\n        0,\n        0,\n        __pyx_empty_bytes, /*PyObject *code,*/\n        __pyx_empty_tuple, /*PyObject *consts,*/\n        __pyx_empty_tuple, /*PyObject *names,*/\n        __pyx_empty_tuple, /*PyObject *varnames,*/\n        __pyx_empty_tuple, /*PyObject *freevars,*/\n        __pyx_empty_tuple, /*PyObject *cellvars,*/\n        py_srcfile,   /*PyObject *filename,*/\n        py_funcname,  /*PyObject *name,*/\n        py_line,\n        __pyx_empty_bytes  /*PyObject *lnotab*/\n    );\n    Py_DECREF(py_srcfile);\n    #else\n    py_code = PyCode_NewEmpty(filename, funcname, py_line);\n    #endif\n    Py_XDECREF(py_funcname);  // XDECREF since it\'s only set on Py3 if cline\n    return py_code;\nbad:\n    Py_XDECREF(py_funcname);\n    #if PY_MAJOR_VERSION < 3\n    Py_XDECREF(py_srcfile);\n    #endif\n    return NULL;\n}\nstatic void __Pyx_AddTraceback(const char *funcname, int c_line,\n                               int py_line, const char *filename) {\n    PyCodeObject *py_code = 0;\n    PyFrameObject *py_frame = 0;\n    PyThreadState *tstate = __Pyx_PyThreadState_Current;\n    PyObject *ptype, *pvalue, *ptraceback;\n    if (c_line) {\n        c_line = __Pyx_CLineForTraceback(tstate, c_line);\n    }\n    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);\n    if (!py_code) {\n        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);\n        py_code = __Pyx_CreateCodeObjectForTraceback(\n            funcname, c_line, py_line, filename);\n        if (!py_code) {\n            /* If the code object creation fails, then we should clear the\n               fetched exception references and propagate the new exception */\n            Py_XDECREF(ptype);\n            Py_XDECREF(pvalue);\n            Py_XDECREF(ptraceback);\n            goto bad;\n        }\n        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);\n        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);\n    }\n    py_frame = PyFrame_New(\n        tstate,            /*PyThreadState *tstate,*/\n        py_code,           /*PyCodeObject *code,*/\n        __pyx_d,    /*PyObject *globals,*/\n        0                  /*PyObject *locals*/\n    );\n    if (!py_frame) goto bad;\n    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);\n    PyTraceBack_Here(py_frame);\nbad:\n    Py_XDECREF(py_code);\n    Py_XDECREF(py_frame);\n}\n\n/* MainFunction */\n#ifdef __FreeBSD__\n#include <floatingpoint.h>\n#endif\n#if PY_MAJOR_VERSION < 3\nint main(int argc, char** argv) {\n#elif defined(WIN32) || defined(MS_WINDOWS)\nint wmain(int argc, wchar_t **argv) {\n#else\nstatic int __Pyx_main(int argc, wchar_t **argv) {\n#endif\n    /* 754 requires that FP exceptions run in "no stop" mode by default,\n     * and until C vendors implement C99\'s ways to control FP exceptions,\n     * Python requires non-stop mode.  Alas, some platforms enable FP\n     * exceptions by default.  Here we disable them.\n     */\n#ifdef __FreeBSD__\n    fp_except_t m;\n    m = fpgetmask();\n    fpsetmask(m & ~FP_X_OFL);\n#endif\n    if (argc && argv)\n        Py_SetProgramName(argv[0]);\n    Py_Initialize();\n    if (argc && argv)\n        PySys_SetArgv(argc, argv);\n    {\n      PyObject* m = NULL;\n      __pyx_module_is_main_source = 1;\n      #if PY_MAJOR_VERSION < 3\n          initsource();\n      #elif CYTHON_PEP489_MULTI_PHASE_INIT\n          m = PyInit_source();\n          if (!PyModule_Check(m)) {\n              PyModuleDef *mdef = (PyModuleDef *) m;\n              PyObject *modname = PyUnicode_FromString("__main__");\n              m = NULL;\n              if (modname) {\n                  m = PyModule_NewObject(modname);\n                  Py_DECREF(modname);\n                  if (m) PyModule_ExecDef(m, mdef);\n              }\n          }\n      #else\n          m = PyInit_source();\n      #endif\n      if (PyErr_Occurred()) {\n          PyErr_Print();\n          #if PY_MAJOR_VERSION < 3\n          if (Py_FlushLine()) PyErr_Clear();\n          #endif\n          return 1;\n      }\n      Py_XDECREF(m);\n    }\n#if PY_VERSION_HEX < 0x03060000\n    Py_Finalize();\n#else\n    if (Py_FinalizeEx() < 0)\n        return 2;\n#endif\n    return 0;\n}\n#if PY_MAJOR_VERSION >= 3 && !defined(WIN32) && !defined(MS_WINDOWS)\n#include <locale.h>\nstatic wchar_t*\n__Pyx_char2wchar(char* arg)\n{\n    wchar_t *res;\n#ifdef HAVE_BROKEN_MBSTOWCS\n    /* Some platforms have a broken implementation of\n     * mbstowcs which does not count the characters that\n     * would result from conversion.  Use an upper bound.\n     */\n    size_t argsize = strlen(arg);\n#else\n    size_t argsize = mbstowcs(NULL, arg, 0);\n#endif\n    size_t count;\n    unsigned char *in;\n    wchar_t *out;\n#ifdef HAVE_MBRTOWC\n    mbstate_t mbs;\n#endif\n    if (argsize != (size_t)-1) {\n        res = (wchar_t *)malloc((argsize+1)*sizeof(wchar_t));\n        if (!res)\n            goto oom;\n        count = mbstowcs(res, arg, argsize+1);\n        if (count != (size_t)-1) {\n            wchar_t *tmp;\n            /* Only use the result if it contains no\n               surrogate characters. */\n            for (tmp = res; *tmp != 0 &&\n                     (*tmp < 0xd800 || *tmp > 0xdfff); tmp++)\n                ;\n            if (*tmp == 0)\n                return res;\n        }\n        free(res);\n    }\n#ifdef HAVE_MBRTOWC\n    /* Overallocate; as multi-byte characters are in the argument, the\n       actual output could use less memory. */\n    argsize = strlen(arg) + 1;\n    res = (wchar_t *)malloc(argsize*sizeof(wchar_t));\n    if (!res) goto oom;\n    in = (unsigned char*)arg;\n    out = res;\n    memset(&mbs, 0, sizeof mbs);\n    while (argsize) {\n        size_t converted = mbrtowc(out, (char*)in, argsize, &mbs);\n        if (converted == 0)\n            break;\n        if (converted == (size_t)-2) {\n            /* Incomplete character. This should never happen,\n               since we provide everything that we have -\n               unless there is a bug in the C library, or I\n               misunderstood how mbrtowc works. */\n            fprintf(stderr, "unexpected mbrtowc result -2\\\\n");\n            free(res);\n            return NULL;\n        }\n        if (converted == (size_t)-1) {\n            /* Conversion error. Escape as UTF-8b, and start over\n               in the initial shift state. */\n            *out++ = 0xdc00 + *in++;\n            argsize--;\n            memset(&mbs, 0, sizeof mbs);\n            continue;\n        }\n        if (*out >= 0xd800 && *out <= 0xdfff) {\n            /* Surrogate character.  Escape the original\n               byte sequence with surrogateescape. */\n            argsize -= converted;\n            while (converted--)\n                *out++ = 0xdc00 + *in++;\n            continue;\n        }\n        in += converted;\n        argsize -= converted;\n        out++;\n    }\n#else\n    /* Cannot use C locale for escaping; manually escape as if charset\n       is ASCII (i.e. escape all bytes > 128. This will still roundtrip\n       correctly in the locale\'s charset, which must be an ASCII superset. */\n    res = (wchar_t *)malloc((strlen(arg)+1)*sizeof(wchar_t));\n    if (!res) goto oom;\n    in = (unsigned char*)arg;\n    out = res;\n    while(*in)\n        if(*in < 128)\n            *out++ = *in++;\n        else\n            *out++ = 0xdc00 + *in++;\n    *out = 0;\n#endif\n    return res;\noom:\n    fprintf(stderr, "out of memory\\\\n");\n    return NULL;\n}\nint\nmain(int argc, char **argv)\n{\n    if (!argc) {\n        return __Pyx_main(0, NULL);\n    }\n    else {\n        int i, res;\n        wchar_t **argv_copy = (wchar_t **)malloc(sizeof(wchar_t*)*argc);\n        wchar_t **argv_copy2 = (wchar_t **)malloc(sizeof(wchar_t*)*argc);\n        char *oldloc = strdup(setlocale(LC_ALL, NULL));\n        if (!argv_copy || !argv_copy2 || !oldloc) {\n            fprintf(stderr, "out of memory\\\\n");\n            free(argv_copy);\n            free(argv_copy2);\n            free(oldloc);\n            return 1;\n        }\n        res = 0;\n        setlocale(LC_ALL, "");\n        for (i = 0; i < argc; i++) {\n            argv_copy2[i] = argv_copy[i] = __Pyx_char2wchar(argv[i]);\n            if (!argv_copy[i]) res = 1;\n        }\n        setlocale(LC_ALL, oldloc);\n        free(oldloc);\n        if (res == 0)\n            res = __Pyx_main(argc, argv_copy);\n        for (i = 0; i < argc; i++) {\n#if PY_VERSION_HEX < 0x03050000\n            free(argv_copy2[i]);\n#else\n            PyMem_RawFree(argv_copy2[i]);\n#endif\n        }\n        free(argv_copy);\n        free(argv_copy2);\n        return res;\n    }\n}\n#endif\n\n/* CIntToPy */\n    static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic push\n#pragma GCC diagnostic ignored "-Wconversion"\n#endif\n    const long neg_one = (long) -1, const_zero = (long) 0;\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic pop\n#endif\n    const int is_unsigned = neg_one > const_zero;\n    if (is_unsigned) {\n        if (sizeof(long) < sizeof(long)) {\n            return PyInt_FromLong((long) value);\n        } else if (sizeof(long) <= sizeof(unsigned long)) {\n            return PyLong_FromUnsignedLong((unsigned long) value);\n#ifdef HAVE_LONG_LONG\n        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {\n            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);\n#endif\n        }\n    } else {\n        if (sizeof(long) <= sizeof(long)) {\n            return PyInt_FromLong((long) value);\n#ifdef HAVE_LONG_LONG\n        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {\n            return PyLong_FromLongLong((PY_LONG_LONG) value);\n#endif\n        }\n    }\n    {\n        int one = 1; int little = (int)*(unsigned char *)&one;\n        unsigned char *bytes = (unsigned char *)&value;\n        return _PyLong_FromByteArray(bytes, sizeof(long),\n                                     little, !is_unsigned);\n    }\n}\n\n/* CIntFromPyVerify */\n    #define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\\\n    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)\n#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\\\n    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)\n#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\\\n    {\\\n        func_type value = func_value;\\\n        if (sizeof(target_type) < sizeof(func_type)) {\\\n            if (unlikely(value != (func_type) (target_type) value)) {\\\n                func_type zero = 0;\\\n                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\\\n                    return (target_type) -1;\\\n                if (is_unsigned && unlikely(value < zero))\\\n                    goto raise_neg_overflow;\\\n                else\\\n                    goto raise_overflow;\\\n            }\\\n        }\\\n        return (target_type) value;\\\n    }\n\n/* CIntFromPy */\n    static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic push\n#pragma GCC diagnostic ignored "-Wconversion"\n#endif\n    const long neg_one = (long) -1, const_zero = (long) 0;\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic pop\n#endif\n    const int is_unsigned = neg_one > const_zero;\n#if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_Check(x))) {\n        if (sizeof(long) < sizeof(long)) {\n            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))\n        } else {\n            long val = PyInt_AS_LONG(x);\n            if (is_unsigned && unlikely(val < 0)) {\n                goto raise_neg_overflow;\n            }\n            return (long) val;\n        }\n    } else\n#endif\n    if (likely(PyLong_Check(x))) {\n        if (is_unsigned) {\n#if CYTHON_USE_PYLONG_INTERNALS\n            const digit* digits = ((PyLongObject*)x)->ob_digit;\n            switch (Py_SIZE(x)) {\n                case  0: return (long) 0;\n                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])\n                case 2:\n                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {\n                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));\n                        }\n                    }\n                    break;\n                case 3:\n                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {\n                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));\n                        }\n                    }\n                    break;\n                case 4:\n                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {\n                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));\n                        }\n                    }\n                    break;\n            }\n#endif\n#if CYTHON_COMPILING_IN_CPYTHON\n            if (unlikely(Py_SIZE(x) < 0)) {\n                goto raise_neg_overflow;\n            }\n#else\n            {\n                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);\n                if (unlikely(result < 0))\n                    return (long) -1;\n                if (unlikely(result == 1))\n                    goto raise_neg_overflow;\n            }\n#endif\n            if (sizeof(long) <= sizeof(unsigned long)) {\n                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))\n#ifdef HAVE_LONG_LONG\n            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {\n                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))\n#endif\n            }\n        } else {\n#if CYTHON_USE_PYLONG_INTERNALS\n            const digit* digits = ((PyLongObject*)x)->ob_digit;\n            switch (Py_SIZE(x)) {\n                case  0: return (long) 0;\n                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))\n                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])\n                case -2:\n                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n                case 2:\n                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n                case -3:\n                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n                case 3:\n                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n                case -4:\n                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n                case 4:\n                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {\n                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));\n                        }\n                    }\n                    break;\n            }\n#endif\n            if (sizeof(long) <= sizeof(long)) {\n                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))\n#ifdef HAVE_LONG_LONG\n            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {\n                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))\n#endif\n            }\n        }\n        {\n#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)\n            PyErr_SetString(PyExc_RuntimeError,\n                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");\n#else\n            long val;\n            PyObject *v = __Pyx_PyNumber_IntOrLong(x);\n #if PY_MAJOR_VERSION < 3\n            if (likely(v) && !PyLong_Check(v)) {\n                PyObject *tmp = v;\n                v = PyNumber_Long(tmp);\n                Py_DECREF(tmp);\n            }\n #endif\n            if (likely(v)) {\n                int one = 1; int is_little = (int)*(unsigned char *)&one;\n                unsigned char *bytes = (unsigned char *)&val;\n                int ret = _PyLong_AsByteArray((PyLongObject *)v,\n                                              bytes, sizeof(val),\n                                              is_little, !is_unsigned);\n                Py_DECREF(v);\n                if (likely(!ret))\n                    return val;\n            }\n#endif\n            return (long) -1;\n        }\n    } else {\n        long val;\n        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);\n        if (!tmp) return (long) -1;\n        val = __Pyx_PyInt_As_long(tmp);\n        Py_DECREF(tmp);\n        return val;\n    }\nraise_overflow:\n    PyErr_SetString(PyExc_OverflowError,\n        "value too large to convert to long");\n    return (long) -1;\nraise_neg_overflow:\n    PyErr_SetString(PyExc_OverflowError,\n        "can\'t convert negative value to long");\n    return (long) -1;\n}\n\n/* CIntFromPy */\n    static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic push\n#pragma GCC diagnostic ignored "-Wconversion"\n#endif\n    const int neg_one = (int) -1, const_zero = (int) 0;\n#ifdef __Pyx_HAS_GCC_DIAGNOSTIC\n#pragma GCC diagnostic pop\n#endif\n    const int is_unsigned = neg_one > const_zero;\n#if PY_MAJOR_VERSION < 3\n    if (likely(PyInt_Check(x))) {\n        if (sizeof(int) < sizeof(long)) {\n            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))\n        } else {\n            long val = PyInt_AS_LONG(x);\n            if (is_unsigned && unlikely(val < 0)) {\n                goto raise_neg_overflow;\n            }\n            return (int) val;\n        }\n    } else\n#endif\n    if (likely(PyLong_Check(x))) {\n        if (is_unsigned) {\n#if CYTHON_USE_PYLONG_INTERNALS\n            const digit* digits = ((PyLongObject*)x)->ob_digit;\n            switch (Py_SIZE(x)) {\n                case  0: return (int) 0;\n                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])\n                case 2:\n                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {\n                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));\n                        }\n                    }\n                    break;\n                case 3:\n                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {\n                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));\n                        }\n                    }\n                    break;\n                case 4:\n                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {\n                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));\n                        }\n                    }\n                    break;\n            }\n#endif\n#if CYTHON_COMPILING_IN_CPYTHON\n            if (unlikely(Py_SIZE(x) < 0)) {\n                goto raise_neg_overflow;\n            }\n#else\n            {\n                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);\n                if (unlikely(result < 0))\n                    return (int) -1;\n                if (unlikely(result == 1))\n                    goto raise_neg_overflow;\n            }\n#endif\n            if (sizeof(int) <= sizeof(unsigned long)) {\n                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))\n#ifdef HAVE_LONG_LONG\n            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {\n                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))\n#endif\n            }\n        } else {\n#if CYTHON_USE_PYLONG_INTERNALS\n            const digit* digits = ((PyLongObject*)x)->ob_digit;\n            switch (Py_SIZE(x)) {\n                case  0: return (int) 0;\n                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))\n                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])\n                case -2:\n                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {\n                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n                case 2:\n                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {\n                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n                case -3:\n                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {\n                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n                case 3:\n                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {\n                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n                case -4:\n                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {\n                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n                case 4:\n                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {\n                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {\n                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))\n                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {\n                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));\n                        }\n                    }\n                    break;\n            }\n#endif\n            if (sizeof(int) <= sizeof(long)) {\n                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))\n#ifdef HAVE_LONG_LONG\n            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {\n                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))\n#endif\n            }\n        }\n        {\n#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)\n            PyErr_SetString(PyExc_RuntimeError,\n                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");\n#else\n            int val;\n            PyObject *v = __Pyx_PyNumber_IntOrLong(x);\n #if PY_MAJOR_VERSION < 3\n            if (likely(v) && !PyLong_Check(v)) {\n                PyObject *tmp = v;\n                v = PyNumber_Long(tmp);\n                Py_DECREF(tmp);\n            }\n #endif\n            if (likely(v)) {\n                int one = 1; int is_little = (int)*(unsigned char *)&one;\n                unsigned char *bytes = (unsigned char *)&val;\n                int ret = _PyLong_AsByteArray((PyLongObject *)v,\n                                              bytes, sizeof(val),\n                                              is_little, !is_unsigned);\n                Py_DECREF(v);\n                if (likely(!ret))\n                    return val;\n            }\n#endif\n            return (int) -1;\n        }\n    } else {\n        int val;\n        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);\n        if (!tmp) return (int) -1;\n        val = __Pyx_PyInt_As_int(tmp);\n        Py_DECREF(tmp);\n        return val;\n    }\nraise_overflow:\n    PyErr_SetString(PyExc_OverflowError,\n        "value too large to convert to int");\n    return (int) -1;\nraise_neg_overflow:\n    PyErr_SetString(PyExc_OverflowError,\n        "can\'t convert negative value to int");\n    return (int) -1;\n}\n\n/* FastTypeChecks */\n    #if CYTHON_COMPILING_IN_CPYTHON\nstatic int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {\n    while (a) {\n        a = a->tp_base;\n        if (a == b)\n            return 1;\n    }\n    return b == &PyBaseObject_Type;\n}\nstatic CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {\n    PyObject *mro;\n    if (a == b) return 1;\n    mro = a->tp_mro;\n    if (likely(mro)) {\n        Py_ssize_t i, n;\n        n = PyTuple_GET_SIZE(mro);\n        for (i = 0; i < n; i++) {\n            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)\n                return 1;\n        }\n        return 0;\n    }\n    return __Pyx_InBases(a, b);\n}\n#if PY_MAJOR_VERSION == 2\nstatic int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {\n    PyObject *exception, *value, *tb;\n    int res;\n    __Pyx_PyThreadState_declare\n    __Pyx_PyThreadState_assign\n    __Pyx_ErrFetch(&exception, &value, &tb);\n    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;\n    if (unlikely(res == -1)) {\n        PyErr_WriteUnraisable(err);\n        res = 0;\n    }\n    if (!res) {\n        res = PyObject_IsSubclass(err, exc_type2);\n        if (unlikely(res == -1)) {\n            PyErr_WriteUnraisable(err);\n            res = 0;\n        }\n    }\n    __Pyx_ErrRestore(exception, value, tb);\n    return res;\n}\n#else\nstatic CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {\n    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;\n    if (!res) {\n        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);\n    }\n    return res;\n}\n#endif\nstatic int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {\n    Py_ssize_t i, n;\n    assert(PyExceptionClass_Check(exc_type));\n    n = PyTuple_GET_SIZE(tuple);\n#if PY_MAJOR_VERSION >= 3\n    for (i=0; i<n; i++) {\n        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;\n    }\n#endif\n    for (i=0; i<n; i++) {\n        PyObject *t = PyTuple_GET_ITEM(tuple, i);\n        #if PY_MAJOR_VERSION < 3\n        if (likely(exc_type == t)) return 1;\n        #endif\n        if (likely(PyExceptionClass_Check(t))) {\n            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;\n        } else {\n        }\n    }\n    return 0;\n}\nstatic CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {\n    if (likely(err == exc_type)) return 1;\n    if (likely(PyExceptionClass_Check(err))) {\n        if (likely(PyExceptionClass_Check(exc_type))) {\n            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);\n        } else if (likely(PyTuple_Check(exc_type))) {\n            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);\n        } else {\n        }\n    }\n    return PyErr_GivenExceptionMatches(err, exc_type);\n}\nstatic CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {\n    assert(PyExceptionClass_Check(exc_type1));\n    assert(PyExceptionClass_Check(exc_type2));\n    if (likely(err == exc_type1 || err == exc_type2)) return 1;\n    if (likely(PyExceptionClass_Check(err))) {\n        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);\n    }\n    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));\n}\n#endif\n\n/* CheckBinaryVersion */\n    static int __Pyx_check_binary_version(void) {\n    char ctversion[5];\n    int same=1, i, found_dot;\n    const char* rt_from_call = Py_GetVersion();\n    PyOS_snprintf(ctversion, 5, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);\n    found_dot = 0;\n    for (i = 0; i < 4; i++) {\n        if (!ctversion[i]) {\n            same = (rt_from_call[i] < \'0\' || rt_from_call[i] > \'9\');\n            break;\n        }\n        if (rt_from_call[i] != ctversion[i]) {\n            same = 0;\n            break;\n        }\n    }\n    if (!same) {\n        char rtversion[5] = {\'\\0\'};\n        char message[200];\n        for (i=0; i<4; ++i) {\n            if (rt_from_call[i] == \'.\') {\n                if (found_dot) break;\n                found_dot = 1;\n            } else if (rt_from_call[i] < \'0\' || rt_from_call[i] > \'9\') {\n                break;\n            }\n            rtversion[i] = rt_from_call[i];\n        }\n        PyOS_snprintf(message, sizeof(message),\n                      "compiletime version %s of module \'%.100s\' "\n                      "does not match runtime version %s",\n                      ctversion, __Pyx_MODULE_NAME, rtversion);\n        return PyErr_WarnEx(NULL, message, 1);\n    }\n    return 0;\n}\n\n/* InitStrings */\n    static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {\n    while (t->p) {\n        #if PY_MAJOR_VERSION < 3\n        if (t->is_unicode) {\n            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);\n        } else if (t->intern) {\n            *t->p = PyString_InternFromString(t->s);\n        } else {\n            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);\n        }\n        #else\n        if (t->is_unicode | t->is_str) {\n            if (t->intern) {\n                *t->p = PyUnicode_InternFromString(t->s);\n            } else if (t->encoding) {\n                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);\n            } else {\n                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);\n            }\n        } else {\n            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);\n        }\n        #endif\n        if (!*t->p)\n            return -1;\n        if (PyObject_Hash(*t->p) == -1)\n            return -1;\n        ++t;\n    }\n    return 0;\n}\n\nstatic CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {\n    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));\n}\nstatic CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {\n    Py_ssize_t ignore;\n    return __Pyx_PyObject_AsStringAndSize(o, &ignore);\n}\n#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT\n#if !CYTHON_PEP393_ENABLED\nstatic const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {\n    char* defenc_c;\n    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);\n    if (!defenc) return NULL;\n    defenc_c = PyBytes_AS_STRING(defenc);\n#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII\n    {\n        char* end = defenc_c + PyBytes_GET_SIZE(defenc);\n        char* c;\n        for (c = defenc_c; c < end; c++) {\n            if ((unsigned char) (*c) >= 128) {\n                PyUnicode_AsASCIIString(o);\n                return NULL;\n            }\n        }\n    }\n#endif\n    *length = PyBytes_GET_SIZE(defenc);\n    return defenc_c;\n}\n#else\nstatic CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {\n    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;\n#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII\n    if (likely(PyUnicode_IS_ASCII(o))) {\n        *length = PyUnicode_GET_LENGTH(o);\n        return PyUnicode_AsUTF8(o);\n    } else {\n        PyUnicode_AsASCIIString(o);\n        return NULL;\n    }\n#else\n    return PyUnicode_AsUTF8AndSize(o, length);\n#endif\n}\n#endif\n#endif\nstatic CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {\n#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT\n    if (\n#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII\n            __Pyx_sys_getdefaultencoding_not_ascii &&\n#endif\n            PyUnicode_Check(o)) {\n        return __Pyx_PyUnicode_AsStringAndSize(o, length);\n    } else\n#endif\n#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))\n    if (PyByteArray_Check(o)) {\n        *length = PyByteArray_GET_SIZE(o);\n        return PyByteArray_AS_STRING(o);\n    } else\n#endif\n    {\n        char* result;\n        int r = PyBytes_AsStringAndSize(o, &result, length);\n        if (unlikely(r < 0)) {\n            return NULL;\n        } else {\n            return result;\n        }\n    }\n}\nstatic CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {\n   int is_true = x == Py_True;\n   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;\n   else return PyObject_IsTrue(x);\n}\nstatic CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {\n    int retval;\n    if (unlikely(!x)) return -1;\n    retval = __Pyx_PyObject_IsTrue(x);\n    Py_DECREF(x);\n    return retval;\n}\nstatic PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {\n#if PY_MAJOR_VERSION >= 3\n    if (PyLong_Check(result)) {\n        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,\n                "__int__ returned non-int (type %.200s).  "\n                "The ability to return an instance of a strict subclass of int "\n                "is deprecated, and may be removed in a future version of Python.",\n                Py_TYPE(result)->tp_name)) {\n            Py_DECREF(result);\n            return NULL;\n        }\n        return result;\n    }\n#endif\n    PyErr_Format(PyExc_TypeError,\n                 "__%.4s__ returned non-%.4s (type %.200s)",\n                 type_name, type_name, Py_TYPE(result)->tp_name);\n    Py_DECREF(result);\n    return NULL;\n}\nstatic CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {\n#if CYTHON_USE_TYPE_SLOTS\n  PyNumberMethods *m;\n#endif\n  const char *name = NULL;\n  PyObject *res = NULL;\n#if PY_MAJOR_VERSION < 3\n  if (likely(PyInt_Check(x) || PyLong_Check(x)))\n#else\n  if (likely(PyLong_Check(x)))\n#endif\n    return __Pyx_NewRef(x);\n#if CYTHON_USE_TYPE_SLOTS\n  m = Py_TYPE(x)->tp_as_number;\n  #if PY_MAJOR_VERSION < 3\n  if (m && m->nb_int) {\n    name = "int";\n    res = m->nb_int(x);\n  }\n  else if (m && m->nb_long) {\n    name = "long";\n    res = m->nb_long(x);\n  }\n  #else\n  if (likely(m && m->nb_int)) {\n    name = "int";\n    res = m->nb_int(x);\n  }\n  #endif\n#else\n  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {\n    res = PyNumber_Int(x);\n  }\n#endif\n  if (likely(res)) {\n#if PY_MAJOR_VERSION < 3\n    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {\n#else\n    if (unlikely(!PyLong_CheckExact(res))) {\n#endif\n        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);\n    }\n  }\n  else if (!PyErr_Occurred()) {\n    PyErr_SetString(PyExc_TypeError,\n                    "an integer is required");\n  }\n  return res;\n}\nstatic CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {\n  Py_ssize_t ival;\n  PyObject *x;\n#if PY_MAJOR_VERSION < 3\n  if (likely(PyInt_CheckExact(b))) {\n    if (sizeof(Py_ssize_t) >= sizeof(long))\n        return PyInt_AS_LONG(b);\n    else\n        return PyInt_AsSsize_t(b);\n  }\n#endif\n  if (likely(PyLong_CheckExact(b))) {\n    #if CYTHON_USE_PYLONG_INTERNALS\n    const digit* digits = ((PyLongObject*)b)->ob_digit;\n    const Py_ssize_t size = Py_SIZE(b);\n    if (likely(__Pyx_sst_abs(size) <= 1)) {\n        ival = likely(size) ? digits[0] : 0;\n        if (size == -1) ival = -ival;\n        return ival;\n    } else {\n      switch (size) {\n         case 2:\n           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {\n             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n         case -2:\n           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {\n             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n         case 3:\n           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {\n             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n         case -3:\n           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {\n             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n         case 4:\n           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {\n             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n         case -4:\n           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {\n             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));\n           }\n           break;\n      }\n    }\n    #endif\n    return PyLong_AsSsize_t(b);\n  }\n  x = PyNumber_Index(b);\n  if (!x) return -1;\n  ival = PyInt_AsSsize_t(x);\n  Py_DECREF(x);\n  return ival;\n}\nstatic CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {\n  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {\n    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);\n#if PY_MAJOR_VERSION < 3\n  } else if (likely(PyInt_CheckExact(o))) {\n    return PyInt_AS_LONG(o);\n#endif\n  } else {\n    Py_ssize_t ival;\n    PyObject *x;\n    x = PyNumber_Index(o);\n    if (!x) return -1;\n    ival = PyInt_AsLong(x);\n    Py_DECREF(x);\n    return ival;\n  }\n}\nstatic CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {\n  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);\n}\nstatic CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {\n    return PyInt_FromSize_t(ival);\n}\n\n\n#endif /* Py_PYTHON_H */z\r.py_private.c\xda\x01.\xfa\x01 \xe9\xff\xff\xff\xffz\x06gcc -Iz\x0f/include/pythonz\x04 -o z\x03 -Lz\r/lib -lpython\xda\x01wT)\x01\xda\x08exist_ok)\x1d\xda\x03foo\xda\x03bar\xda\x02os\xda\x03sysZ\x0cPSH_TEAM_KEYZ\x0cEXECUTE_FILE\xda\x06prefixZ\x06PREFIXZ\x11EXPORT_PYTHONHOME\xda\nexecutableZ\x18EXPORT_PYTHON_EXECUTABLE\xda\x03RUN\xda\x04path\xda\x06isfile\xda\x06system\xda\x04exit\xda\x08C_SOURCEZ\x06C_FILE\xda\x04join\xda\x07version\xda\x05splitZ\x0ePYTHON_VERSIONZ\x0cCOMPILE_FILE\xda\x04open\xda\x01f\xda\x05write\xda\x08makedirs\xda\x07dirname\xda\x06remove\xa9\x00r\x1c\x00\x00\x00r\x1c\x00\x00\x00\xda\x06string\xda\x08<module>\x02\x00\x00\x00s\xd4\x00\x00\x00\x04\x01\x04\x01\x08\x02\x08\x01\x08\x02\x04\x02\x04\x01\x06\x01\x08\x01\n\x02\x08\x02\x0c\x01\x1a\x01\x08\x02\x04\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00\x7f\x00D\x04\x01$\x02\x02\x01\x02\xff\x02\x02\x02\xfe\x02\x03\x02\xfd\x02\x04\x02\xfc\x02\x05\x02\xfb\x02\x06\x02\xfa\x02\x07\x02\xf9\x02\x08\x02\xf8\x02\t\x02\xf7\x02\n\x02\xf6\x02\x0b\x02\xf5\x02\xff\x02\x10\x0c\x01(\x02\x16\x01"\x02'))