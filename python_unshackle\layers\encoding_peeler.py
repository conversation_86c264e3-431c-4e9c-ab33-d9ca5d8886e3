"""
Encoding Peeler for PyThon-Unshackle

This module implements the encoding layer peeler that handles various text-based
encoding obfuscation techniques including Base64, hexadecimal, URL encoding,
ROT13, and other character encoding schemes.
"""

import base64
import binascii
import urllib.parse
import codecs
import re
import time
from typing import Union, Tuple, List, Dict, Any

from .base_layer import BaseLayer, LayerOutput, LayerResult, LayerMetadata


class EncodingPeeler(BaseLayer):
    """
    Encoding layer peeler for text-based obfuscation techniques.
    
    Handles:
    - Base64 encoding/decoding
    - Hexadecimal encoding/decoding
    - URL encoding/decoding
    - ROT13 and other rotation ciphers
    - Various character encodings
    """
    
    def __init__(self):
        super().__init__("Encoding Peeler", priority=20)
        self._init_patterns()
    
    def _init_patterns(self) -> None:
        """Initialize regex patterns for encoding detection."""
        self.patterns = {
            'base64_function': r'base64\.b64decode\s*\(',
            'base64_string': r'[A-Za-z0-9+/]{20,}={0,2}',
            'hex_function': r'(?:binascii\.unhexlify|bytes\.fromhex)\s*\(',
            'hex_string': r'[0-9a-fA-F]{40,}',
            'url_function': r'urllib\.parse\.unquote',
            'url_encoded': r'%[0-9a-fA-F]{2}',
            'rot13': r'\.encode\s*\(\s*[\'"]rot[_-]?13[\'"]',
        }
    
    def can_handle(self, data: Union[str, bytes]) -> Tuple[bool, float]:
        """
        Determine if this layer can handle the given data.
        
        Args:
            data: Input data to analyze
            
        Returns:
            Tuple of (can_handle: bool, confidence: float)
        """
        if isinstance(data, bytes):
            try:
                text_data = data.decode('utf-8', errors='ignore')
            except:
                return False, 0.0
        else:
            text_data = data
        
        confidence = 0.0
        evidence_count = 0
        
        # Check for encoding function calls
        for pattern_name, pattern in self.patterns.items():
            if re.search(pattern, text_data, re.IGNORECASE):
                confidence += 0.3
                evidence_count += 1
        
        # Check for base64-like strings
        base64_matches = re.findall(self.patterns['base64_string'], text_data)
        for match in base64_matches[:3]:  # Check up to 3 matches
            if self._is_valid_base64(match):
                confidence += 0.4
                evidence_count += 1
        
        # Check for hex strings
        hex_matches = re.findall(self.patterns['hex_string'], text_data)
        for match in hex_matches[:3]:  # Check up to 3 matches
            if self._is_valid_hex(match):
                confidence += 0.3
                evidence_count += 1
        
        # Check for URL encoded content
        if '%' in text_data and re.search(self.patterns['url_encoded'], text_data):
            confidence += 0.2
            evidence_count += 1
        
        return evidence_count > 0, min(confidence, 1.0)
    
    def process(self, data: Union[str, bytes]) -> LayerOutput:
        """
        Process the input data and attempt to decode it.
        
        Args:
            data: Encoded data to decode
            
        Returns:
            LayerOutput containing the decoded data and metadata
        """
        start_time = time.time()
        
        if isinstance(data, bytes):
            try:
                text_data = data.decode('utf-8')
            except UnicodeDecodeError:
                return self._create_failed_output("Cannot decode bytes to text", start_time, len(data))
        else:
            text_data = data
        
        input_size = len(text_data.encode('utf-8'))
        
        # Try different decoding methods in order of likelihood
        decoding_methods = [
            self._decode_base64_functions,
            self._decode_hex_functions,
            self._decode_url_encoding,
            self._decode_base64_strings,
            self._decode_hex_strings,
            self._decode_rot13,
        ]
        
        for method in decoding_methods:
            try:
                result = method(text_data)
                if result:
                    processing_time = time.time() - start_time
                    self._update_statistics(True, input_size)
                    
                    return LayerOutput(
                        data=result,
                        metadata=self._create_metadata(
                            LayerResult.SUCCESS,
                            "encoded_text",
                            "decoded_text",
                            input_size,
                            processing_time,
                            0.8,
                            {"decoding_method": method.__name__}
                        ),
                        success=True
                    )
            except Exception as e:
                continue
        
        # If no method worked
        processing_time = time.time() - start_time
        self._update_statistics(False, input_size)
        
        return self._create_failed_output("No encoding patterns could be decoded", start_time, input_size)
    
    def _decode_base64_functions(self, text: str) -> Union[str, bytes, None]:
        """Decode base64 using function calls found in the code."""
        # Look for base64.b64decode() calls
        pattern = r'base64\.b64decode\s*\(\s*[\'"]([A-Za-z0-9+/=]+)[\'"]\s*\)'
        matches = re.findall(pattern, text)
        
        for match in matches:
            try:
                decoded = base64.b64decode(match)
                # Try to decode as UTF-8, return bytes if it fails
                try:
                    return decoded.decode('utf-8')
                except UnicodeDecodeError:
                    return decoded
            except:
                continue
        
        return None
    
    def _decode_hex_functions(self, text: str) -> Union[str, bytes, None]:
        """Decode hex using function calls found in the code."""
        # Look for binascii.unhexlify() or bytes.fromhex() calls
        patterns = [
            r'binascii\.unhexlify\s*\(\s*[\'"]([0-9a-fA-F]+)[\'"]\s*\)',
            r'bytes\.fromhex\s*\(\s*[\'"]([0-9a-fA-F]+)[\'"]\s*\)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    decoded = binascii.unhexlify(match)
                    try:
                        return decoded.decode('utf-8')
                    except UnicodeDecodeError:
                        return decoded
                except:
                    continue
        
        return None
    
    def _decode_url_encoding(self, text: str) -> Union[str, None]:
        """Decode URL encoded strings."""
        # Look for urllib.parse.unquote() calls
        pattern = r'urllib\.parse\.unquote\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*\)'
        matches = re.findall(pattern, text)
        
        for match in matches:
            try:
                decoded = urllib.parse.unquote(match)
                if decoded != match:  # Only return if actually decoded something
                    return decoded
            except:
                continue
        
        # Also try to decode any URL-encoded looking strings
        if '%' in text:
            try:
                decoded = urllib.parse.unquote(text)
                if decoded != text:
                    return decoded
            except:
                pass
        
        return None
    
    def _decode_base64_strings(self, text: str) -> Union[str, bytes, None]:
        """Decode standalone base64 strings."""
        base64_matches = re.findall(self.patterns['base64_string'], text)
        
        for match in base64_matches:
            if self._is_valid_base64(match) and len(match) > 20:
                try:
                    decoded = base64.b64decode(match)
                    if len(decoded) > 10:  # Reasonable size
                        try:
                            return decoded.decode('utf-8')
                        except UnicodeDecodeError:
                            return decoded
                except:
                    continue
        
        return None
    
    def _decode_hex_strings(self, text: str) -> Union[str, bytes, None]:
        """Decode standalone hex strings."""
        hex_matches = re.findall(self.patterns['hex_string'], text)
        
        for match in hex_matches:
            if self._is_valid_hex(match) and len(match) > 20:
                try:
                    decoded = binascii.unhexlify(match)
                    if len(decoded) > 10:  # Reasonable size
                        try:
                            return decoded.decode('utf-8')
                        except UnicodeDecodeError:
                            return decoded
                except:
                    continue
        
        return None
    
    def _decode_rot13(self, text: str) -> Union[str, None]:
        """Decode ROT13 encoded strings."""
        # Look for .encode('rot13') or .decode('rot13') patterns
        if 'rot' in text.lower():
            try:
                # Try ROT13 decoding
                decoded = codecs.decode(text, 'rot13')
                if decoded != text:
                    return decoded
            except:
                pass
        
        return None
    
    def _is_valid_base64(self, s: str) -> bool:
        """Check if a string is valid base64."""
        try:
            # Check if it's valid base64
            if len(s) % 4 == 0:
                base64.b64decode(s, validate=True)
                return True
        except:
            pass
        return False
    
    def _is_valid_hex(self, s: str) -> bool:
        """Check if a string is valid hexadecimal."""
        try:
            if len(s) % 2 == 0:
                binascii.unhexlify(s)
                return True
        except:
            pass
        return False
    
    def _create_failed_output(self, error_msg: str, start_time: float, input_size: int) -> LayerOutput:
        """Create a failed LayerOutput."""
        processing_time = time.time() - start_time
        
        return LayerOutput(
            data=None,
            metadata=self._create_metadata(
                LayerResult.FAILED,
                "encoded_text",
                "none",
                input_size,
                processing_time,
                0.0,
                {"error": error_msg}
            ),
            success=False,
            error_message=error_msg
        )
