"""
PyThon-Unshackle: The Ultimate Multi-Stage Python Deobfuscation Tool

A sophisticated pipeline for reverse engineering and deobfuscating Python source code
that has been obfuscated using various techniques including marshal, zlib, base64, exec,
and other complex nested obfuscation methods.

Author: AI Assistant
Version: 1.0.0
License: MIT
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "The Ultimate Multi-Stage Python Deobfuscation Tool"

from .core.pipeline import DeobfuscationPipeline
from .core.detector import PatternDetector
from .core.ast_navigator import ASTNavigator

__all__ = [
    "DeobfuscationPipeline",
    "PatternDetector", 
    "ASTNavigator"
]
