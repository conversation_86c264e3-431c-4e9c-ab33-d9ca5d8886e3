"""
Marshal Decompiler for PyThon-Unshackle

This module specializes in decompiling Python code objects that have been
serialized using marshal. It's a critical component for converting bytecode
back into readable Python source code.
"""

import types
import sys
from typing import Optional, Dict, Any
from io import StringIO


class MarshalDecompiler:
    """
    Specialized decompiler for marshal code objects.
    
    Converts Python code objects back into readable source code using
    various decompilation strategies and fallback mechanisms.
    """
    
    def __init__(self):
        self.decompilation_errors = []
        self.last_decompiled_info = {}
    
    def decompile_code_object(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Decompile a Python code object back to source code.
        
        Args:
            code_obj: Python code object to decompile
            
        Returns:
            Decompiled Python source code if successful, None otherwise
        """
        self.decompilation_errors.clear()
        self.last_decompiled_info = self._extract_code_info(code_obj)
        
        # Try different decompilation methods in order of preference
        decompilation_methods = [
            self._decompile_with_uncompyle6,
            self._decompile_with_decompyle3,
            self._decompile_with_dis_analysis,
            self._create_stub_from_metadata,
        ]
        
        for method in decompilation_methods:
            try:
                result = method(code_obj)
                if result and len(result.strip()) > 0:
                    return self._clean_decompiled_code(result)
            except Exception as e:
                self.decompilation_errors.append(f"{method.__name__}: {e}")
                continue
        
        # If all methods failed, return error information
        return self._create_error_report(code_obj)
    
    def _decompile_with_uncompyle6(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Decompile using uncompyle6 library.
        
        Args:
            code_obj: Code object to decompile
            
        Returns:
            Decompiled source code or None
        """
        try:
            import uncompyle6
            from uncompyle6.main import decompile
            
            output = StringIO()
            
            # Use uncompyle6 to decompile the code object
            decompile(
                co=code_obj,
                out=output,
                showasm=False,
                showast=False,
                timestamp=None,
                showgrammar=False
            )
            
            result = output.getvalue()
            output.close()
            
            return result
            
        except ImportError:
            raise ImportError("uncompyle6 library not available")
        except Exception as e:
            raise Exception(f"uncompyle6 decompilation failed: {e}")
    
    def _decompile_with_decompyle3(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Decompile using decompyle3 library (alternative to uncompyle6).
        
        Args:
            code_obj: Code object to decompile
            
        Returns:
            Decompiled source code or None
        """
        try:
            import decompyle3
            
            output = StringIO()
            decompyle3.main.decompile(
                co=code_obj,
                out=output,
                showasm=False,
                showast=False
            )
            
            result = output.getvalue()
            output.close()
            
            return result
            
        except ImportError:
            raise ImportError("decompyle3 library not available")
        except Exception as e:
            raise Exception(f"decompyle3 decompilation failed: {e}")
    
    def _decompile_with_dis_analysis(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Create a readable representation using disassembly analysis.
        
        This method analyzes the bytecode and attempts to reconstruct
        a simplified version of the original code.
        
        Args:
            code_obj: Code object to analyze
            
        Returns:
            Reconstructed code or None
        """
        import dis
        
        try:
            # Get disassembly
            output = StringIO()
            dis.dis(code_obj, file=output)
            disassembly = output.getvalue()
            output.close()
            
            # Analyze the bytecode to extract meaningful information
            analysis = self._analyze_bytecode(code_obj, disassembly)
            
            # Generate a stub based on analysis
            return self._generate_code_stub(code_obj, analysis)
            
        except Exception as e:
            raise Exception(f"Bytecode analysis failed: {e}")
    
    def _analyze_bytecode(self, code_obj: types.CodeType, disassembly: str) -> Dict[str, Any]:
        """
        Analyze bytecode to extract structural information.
        
        Args:
            code_obj: Code object to analyze
            disassembly: Disassembly string
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            'has_loops': 'FOR_ITER' in disassembly or 'WHILE_LOOP' in disassembly,
            'has_conditionals': 'POP_JUMP_IF' in disassembly or 'JUMP_IF' in disassembly,
            'has_function_calls': 'CALL_FUNCTION' in disassembly,
            'has_imports': 'IMPORT_NAME' in disassembly,
            'has_print': 'print' in str(code_obj.co_names) if code_obj.co_names else False,
            'constants': list(code_obj.co_consts) if code_obj.co_consts else [],
            'names': list(code_obj.co_names) if code_obj.co_names else [],
            'varnames': list(code_obj.co_varnames) if code_obj.co_varnames else [],
        }
        
        return analysis
    
    def _generate_code_stub(self, code_obj: types.CodeType, analysis: Dict[str, Any]) -> str:
        """
        Generate a code stub based on analysis.
        
        Args:
            code_obj: Code object
            analysis: Bytecode analysis results
            
        Returns:
            Generated code stub
        """
        lines = []
        
        # Add header comment
        lines.append(f"# Decompiled from code object: {code_obj.co_name}")
        lines.append(f"# Original filename: {code_obj.co_filename}")
        lines.append("")
        
        # Add imports if detected
        if analysis['has_imports']:
            lines.append("# Imports detected in bytecode")
            for name in analysis['names']:
                if name in ['print', 'len', 'str', 'int', 'float']:
                    continue  # Skip builtins
                lines.append(f"# import {name}")
            lines.append("")
        
        # Generate function definition
        if code_obj.co_name != '<module>':
            # It's a function
            args = analysis['varnames'][:code_obj.co_argcount]
            args_str = ', '.join(args) if args else ''
            lines.append(f"def {code_obj.co_name}({args_str}):")
            indent = "    "
        else:
            # It's module-level code
            indent = ""
        
        # Add docstring if available
        if analysis['constants'] and isinstance(analysis['constants'][0], str):
            lines.append(f'{indent}"""')
            lines.append(f'{indent}{analysis["constants"][0]}')
            lines.append(f'{indent}"""')
        
        # Add code structure based on analysis
        if analysis['has_conditionals']:
            lines.append(f"{indent}# Conditional logic detected")
            lines.append(f"{indent}if True:  # Placeholder for conditional")
            lines.append(f"{indent}    pass")
        
        if analysis['has_loops']:
            lines.append(f"{indent}# Loop structure detected")
            lines.append(f"{indent}for item in []:  # Placeholder for loop")
            lines.append(f"{indent}    pass")
        
        if analysis['has_function_calls']:
            lines.append(f"{indent}# Function calls detected")
            for name in analysis['names']:
                if name == 'print':
                    lines.append(f"{indent}print('Decompiled output')")
                    break
        
        # Add constants as comments
        if analysis['constants']:
            lines.append(f"{indent}# Constants found: {analysis['constants']}")
        
        # If no specific structure detected, add a generic placeholder
        if not any([analysis['has_conditionals'], analysis['has_loops'], analysis['has_function_calls']]):
            lines.append(f"{indent}# Code structure could not be determined")
            lines.append(f"{indent}pass")
        
        return '\n'.join(lines)
    
    def _create_stub_from_metadata(self, code_obj: types.CodeType) -> str:
        """
        Create a basic stub from code object metadata.
        
        Args:
            code_obj: Code object to create stub for
            
        Returns:
            Basic code stub
        """
        lines = []
        
        lines.append(f"# Code object stub: {code_obj.co_name}")
        lines.append(f"# Filename: {code_obj.co_filename}")
        lines.append(f"# Arguments: {code_obj.co_argcount}")
        lines.append(f"# Local variables: {code_obj.co_nlocals}")
        lines.append("")
        
        if code_obj.co_name != '<module>':
            args = ['arg' + str(i) for i in range(code_obj.co_argcount)]
            lines.append(f"def {code_obj.co_name}({', '.join(args)}):")
            lines.append("    '''")
            lines.append("    This function was decompiled from bytecode.")
            lines.append("    Original implementation could not be recovered.")
            lines.append("    '''")
            lines.append("    pass")
        else:
            lines.append("# Module-level code")
            lines.append("# Original implementation could not be recovered")
            lines.append("pass")
        
        return '\n'.join(lines)
    
    def _extract_code_info(self, code_obj: types.CodeType) -> Dict[str, Any]:
        """Extract metadata from code object."""
        return {
            'name': code_obj.co_name,
            'filename': code_obj.co_filename,
            'argcount': code_obj.co_argcount,
            'nlocals': code_obj.co_nlocals,
            'stacksize': code_obj.co_stacksize,
            'flags': code_obj.co_flags,
            'code_size': len(code_obj.co_code),
            'constants': code_obj.co_consts,
            'names': code_obj.co_names,
            'varnames': code_obj.co_varnames,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}"
        }
    
    def _clean_decompiled_code(self, code: str) -> str:
        """Clean and format decompiled code."""
        # Remove excessive blank lines
        lines = code.split('\n')
        cleaned_lines = []
        prev_blank = False
        
        for line in lines:
            is_blank = len(line.strip()) == 0
            if is_blank and prev_blank:
                continue  # Skip consecutive blank lines
            cleaned_lines.append(line)
            prev_blank = is_blank
        
        return '\n'.join(cleaned_lines).strip()
    
    def _create_error_report(self, code_obj: types.CodeType) -> str:
        """Create an error report when decompilation fails."""
        lines = []
        
        lines.append("# DECOMPILATION FAILED")
        lines.append("# PyThon-Unshackle could not decompile this code object")
        lines.append("")
        lines.append("# Code Object Information:")
        for key, value in self.last_decompiled_info.items():
            lines.append(f"# {key}: {value}")
        lines.append("")
        lines.append("# Decompilation Errors:")
        for error in self.decompilation_errors:
            lines.append(f"# {error}")
        lines.append("")
        lines.append("# You may need to:")
        lines.append("# 1. Install uncompyle6: pip install uncompyle6")
        lines.append("# 2. Try a different Python version")
        lines.append("# 3. Manually analyze the bytecode")
        
        return '\n'.join(lines)
    
    def get_last_decompilation_info(self) -> Dict[str, Any]:
        """Get information about the last decompilation attempt."""
        return self.last_decompiled_info.copy()
    
    def get_last_errors(self) -> list:
        """Get errors from the last decompilation attempt."""
        return self.decompilation_errors.copy()
