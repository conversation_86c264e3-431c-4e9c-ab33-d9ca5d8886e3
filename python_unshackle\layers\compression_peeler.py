"""
Compression Peeler for PyThon-Unshackle

This module implements the compression layer peeler that handles various
compression-based obfuscation techniques including zlib, gzip, bz2, and lzma.
"""

import zlib
import gzip
import bz2
import lzma
import re
import time
from typing import Union, Tuple, List, Dict, Any
from io import BytesIO

from .base_layer import BaseLayer, LayerOutput, LayerResult, LayerMetadata


class CompressionPeeler(BaseLayer):
    """
    Compression layer peeler for compression-based obfuscation techniques.
    
    Handles:
    - zlib compression/decompression
    - gzip compression/decompression
    - bz2 compression/decompression
    - lzma/xz compression/decompression
    """
    
    def __init__(self):
        super().__init__("Compression Peeler", priority=30)
        self._init_patterns()
        self._init_magic_bytes()
    
    def _init_patterns(self) -> None:
        """Initialize regex patterns for compression detection."""
        self.patterns = {
            'zlib_function': r'zlib\.decompress\s*\(',
            'gzip_function': r'gzip\.decompress\s*\(',
            'bz2_function': r'bz2\.decompress\s*\(',
            'lzma_function': r'lzma\.decompress\s*\(',
        }
    
    def _init_magic_bytes(self) -> None:
        """Initialize magic byte signatures for different compression formats."""
        self.magic_bytes = {
            'zlib': [b'\x78\x01', b'\x78\x9c', b'\x78\xda'],  # Different zlib compression levels
            'gzip': [b'\x1f\x8b'],  # gzip magic number
            'bz2': [b'BZ'],  # bz2 magic number
            'lzma': [b'\xfd7zXZ\x00'],  # xz/lzma magic number
        }
    
    def can_handle(self, data: Union[str, bytes]) -> Tuple[bool, float]:
        """
        Determine if this layer can handle the given data.
        
        Args:
            data: Input data to analyze
            
        Returns:
            Tuple of (can_handle: bool, confidence: float)
        """
        confidence = 0.0
        evidence_count = 0
        
        # Convert string to bytes if necessary
        if isinstance(data, str):
            try:
                byte_data = data.encode('utf-8')
            except:
                byte_data = data.encode('latin-1', errors='ignore')
            
            # Check for compression function calls in text
            for pattern_name, pattern in self.patterns.items():
                if re.search(pattern, data, re.IGNORECASE):
                    confidence += 0.4
                    evidence_count += 1
        else:
            byte_data = data
        
        # Check for compression magic bytes
        for comp_type, magic_list in self.magic_bytes.items():
            for magic in magic_list:
                if byte_data.startswith(magic):
                    confidence += 0.8
                    evidence_count += 1
                    break
        
        # Try to decompress to verify
        if isinstance(data, bytes) and len(data) > 10:
            for method in [self._try_zlib, self._try_gzip, self._try_bz2, self._try_lzma]:
                try:
                    result = method(data)
                    if result:
                        confidence += 0.6
                        evidence_count += 1
                        break
                except:
                    continue
        
        return evidence_count > 0, min(confidence, 1.0)
    
    def process(self, data: Union[str, bytes]) -> LayerOutput:
        """
        Process the input data and attempt to decompress it.
        
        Args:
            data: Compressed data to decompress
            
        Returns:
            LayerOutput containing the decompressed data and metadata
        """
        start_time = time.time()
        
        # Convert string to bytes if necessary
        if isinstance(data, str):
            try:
                byte_data = data.encode('utf-8')
            except:
                byte_data = data.encode('latin-1', errors='ignore')
        else:
            byte_data = data
        
        input_size = len(byte_data)
        
        # Try different decompression methods
        decompression_methods = [
            ('zlib', self._decompress_zlib),
            ('gzip', self._decompress_gzip),
            ('bz2', self._decompress_bz2),
            ('lzma', self._decompress_lzma),
        ]
        
        for method_name, method in decompression_methods:
            try:
                result = method(byte_data)
                if result:
                    processing_time = time.time() - start_time
                    self._update_statistics(True, input_size)
                    
                    # Try to decode as UTF-8, return bytes if it fails
                    if isinstance(result, bytes):
                        try:
                            result = result.decode('utf-8')
                        except UnicodeDecodeError:
                            pass  # Keep as bytes
                    
                    return LayerOutput(
                        data=result,
                        metadata=self._create_metadata(
                            LayerResult.SUCCESS,
                            "compressed_data",
                            "decompressed_data",
                            input_size,
                            processing_time,
                            0.9,
                            {
                                "compression_method": method_name,
                                "compression_ratio": len(byte_data) / len(result) if isinstance(result, (str, bytes)) else 1.0
                            }
                        ),
                        success=True
                    )
            except Exception as e:
                continue
        
        # If no method worked
        processing_time = time.time() - start_time
        self._update_statistics(False, input_size)
        
        return self._create_failed_output("No compression format could be decompressed", start_time, input_size)
    
    def _decompress_zlib(self, data: bytes) -> Union[bytes, None]:
        """Attempt to decompress using zlib."""
        try:
            # Try different wbits values for zlib
            for wbits in [15, -15, 15 | 16, 15 | 32]:
                try:
                    result = zlib.decompress(data, wbits)
                    if len(result) > 0:
                        return result
                except:
                    continue
        except:
            pass
        return None
    
    def _decompress_gzip(self, data: bytes) -> Union[bytes, None]:
        """Attempt to decompress using gzip."""
        try:
            # Method 1: Direct gzip.decompress
            result = gzip.decompress(data)
            if len(result) > 0:
                return result
        except:
            pass
        
        try:
            # Method 2: Using GzipFile with BytesIO
            with gzip.GzipFile(fileobj=BytesIO(data), mode='rb') as f:
                result = f.read()
                if len(result) > 0:
                    return result
        except:
            pass
        
        return None
    
    def _decompress_bz2(self, data: bytes) -> Union[bytes, None]:
        """Attempt to decompress using bz2."""
        try:
            result = bz2.decompress(data)
            if len(result) > 0:
                return result
        except:
            pass
        return None
    
    def _decompress_lzma(self, data: bytes) -> Union[bytes, None]:
        """Attempt to decompress using lzma."""
        try:
            result = lzma.decompress(data)
            if len(result) > 0:
                return result
        except:
            pass
        return None
    
    def _try_zlib(self, data: bytes) -> bool:
        """Test if data can be decompressed with zlib."""
        try:
            zlib.decompress(data)
            return True
        except:
            return False
    
    def _try_gzip(self, data: bytes) -> bool:
        """Test if data can be decompressed with gzip."""
        try:
            gzip.decompress(data)
            return True
        except:
            return False
    
    def _try_bz2(self, data: bytes) -> bool:
        """Test if data can be decompressed with bz2."""
        try:
            bz2.decompress(data)
            return True
        except:
            return False
    
    def _try_lzma(self, data: bytes) -> bool:
        """Test if data can be decompressed with lzma."""
        try:
            lzma.decompress(data)
            return True
        except:
            return False
    
    def _extract_compressed_data_from_code(self, text: str) -> List[bytes]:
        """Extract compressed data from function calls in code."""
        compressed_data = []
        
        # Look for compression function calls with byte strings
        patterns = [
            r'zlib\.decompress\s*\(\s*b[\'"]([^\'\"]+)[\'"]\s*\)',
            r'gzip\.decompress\s*\(\s*b[\'"]([^\'\"]+)[\'"]\s*\)',
            r'bz2\.decompress\s*\(\s*b[\'"]([^\'\"]+)[\'"]\s*\)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    # Convert escape sequences to actual bytes
                    byte_data = bytes(match, 'utf-8').decode('unicode_escape').encode('latin-1')
                    compressed_data.append(byte_data)
                except:
                    continue
        
        return compressed_data
    
    def _create_failed_output(self, error_msg: str, start_time: float, input_size: int) -> LayerOutput:
        """Create a failed LayerOutput."""
        processing_time = time.time() - start_time
        
        return LayerOutput(
            data=None,
            metadata=self._create_metadata(
                LayerResult.FAILED,
                "compressed_data",
                "none",
                input_size,
                processing_time,
                0.0,
                {"error": error_msg}
            ),
            success=False,
            error_message=error_msg
        )
