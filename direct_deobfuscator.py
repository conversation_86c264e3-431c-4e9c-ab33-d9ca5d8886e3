#!/usr/bin/env python3
"""
Direct deobfuscator for test.py
This script directly extracts and decompiles the marshal data from test.py
"""

import marshal
import sys
import re
import ast
from io import StringIO


def extract_marshal_data_from_file(filename):
    """Extract marshal data from the obfuscated file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for marshal.loads with binary data
        pattern = r'marshal\.loads\s*\(\s*b[\'"]([^\'"]*)[\'"]'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            binary_str = match.group(1)
            print(f"Found marshal data, length: {len(binary_str)}")
            
            # Convert escape sequences to actual bytes
            try:
                binary_data = bytes(binary_str, 'utf-8').decode('unicode_escape').encode('latin-1')
                print(f"Converted to binary, length: {len(binary_data)}")
                return binary_data
            except Exception as e:
                print(f"Error converting binary string: {e}")
                return None
        else:
            print("No marshal.loads pattern found")
            return None
            
    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def decompile_marshal_data(binary_data):
    """Decompile marshal data to Python code."""
    try:
        # Load the marshal data
        code_obj = marshal.loads(binary_data)
        print(f"Loaded marshal object: {type(code_obj)}")
        
        if hasattr(code_obj, 'co_code'):
            print("Found code object, attempting decompilation...")
            
            # Try uncompyle6 first
            try:
                import uncompyle6
                output = StringIO()
                uncompyle6.decompile(code_obj, output)
                result = output.getvalue()
                output.close()
                return result
            except ImportError:
                print("uncompyle6 not available, trying manual decompilation...")
                return manual_decompile(code_obj)
            except Exception as e:
                print(f"uncompyle6 failed: {e}, trying manual decompilation...")
                return manual_decompile(code_obj)
        else:
            print(f"Not a code object, returning as string: {code_obj}")
            return str(code_obj)
            
    except Exception as e:
        print(f"Error decompiling marshal data: {e}")
        return None


def manual_decompile(code_obj):
    """Manual decompilation fallback."""
    import dis
    
    lines = []
    lines.append(f"# Manual decompilation of: {code_obj.co_name}")
    lines.append(f"# Original file: {code_obj.co_filename}")
    lines.append("")
    
    # Extract constants that look like code
    if code_obj.co_consts:
        for i, const in enumerate(code_obj.co_consts):
            if isinstance(const, str) and len(const) > 50:
                # Check if it looks like Python code
                if any(keyword in const for keyword in ['def ', 'class ', 'import ', 'print(', 'if ', 'for ']):
                    lines.append(f"# Extracted code string {i+1}:")
                    try:
                        # Try to format it as Python code
                        cleaned = const.replace('\\n', '\n').replace('\\t', '\t')
                        lines.append(cleaned)
                        lines.append("")
                    except:
                        lines.append(f"# Raw string: {repr(const)}")
                        lines.append("")
    
    # Add disassembly for reference
    lines.append("# BYTECODE DISASSEMBLY:")
    try:
        output = StringIO()
        dis.dis(code_obj, file=output)
        disassembly = output.getvalue()
        output.close()
        
        for line in disassembly.split('\n'):
            if line.strip():
                lines.append(f"# {line}")
    except Exception as e:
        lines.append(f"# Disassembly failed: {e}")
    
    return '\n'.join(lines)


def analyze_code_object(code_obj):
    """Analyze code object structure."""
    print("\n=== CODE OBJECT ANALYSIS ===")
    print(f"Name: {code_obj.co_name}")
    print(f"Filename: {code_obj.co_filename}")
    print(f"Arguments: {code_obj.co_argcount}")
    print(f"Local variables: {code_obj.co_nlocals}")
    print(f"Stack size: {code_obj.co_stacksize}")
    print(f"Flags: {code_obj.co_flags}")
    print(f"Code size: {len(code_obj.co_code)}")
    
    if code_obj.co_consts:
        print(f"Constants ({len(code_obj.co_consts)}):")
        for i, const in enumerate(code_obj.co_consts[:10]):  # Show first 10
            if isinstance(const, str) and len(const) > 100:
                print(f"  {i}: <long string, length {len(const)}>")
            else:
                print(f"  {i}: {repr(const)}")
    
    if code_obj.co_names:
        print(f"Names: {code_obj.co_names}")
    
    if code_obj.co_varnames:
        print(f"Variable names: {code_obj.co_varnames}")


def main():
    """Main function."""
    filename = "test.py"
    
    print(f"Direct deobfuscation of {filename}")
    print("=" * 50)
    
    # Extract marshal data
    binary_data = extract_marshal_data_from_file(filename)
    if not binary_data:
        print("Failed to extract marshal data")
        return 1
    
    # Load and analyze the code object
    try:
        code_obj = marshal.loads(binary_data)
        if hasattr(code_obj, 'co_code'):
            analyze_code_object(code_obj)
    except Exception as e:
        print(f"Error analyzing code object: {e}")
    
    # Decompile
    print("\n=== DECOMPILATION ===")
    decompiled = decompile_marshal_data(binary_data)
    
    if decompiled:
        print("SUCCESS! Decompiled code:")
        print("-" * 50)
        print(decompiled)
        print("-" * 50)
        
        # Save to file
        with open("test_decompiled.py", "w", encoding="utf-8") as f:
            f.write(decompiled)
        print("Saved to test_decompiled.py")
        
        return 0
    else:
        print("FAILED to decompile")
        return 1


if __name__ == "__main__":
    sys.exit(main())
