"""
PYC Decompiler for PyThon-Unshackle

This module provides decompilation capabilities for Python bytecode files (.pyc)
and code objects using various decompilation libraries.
"""

import types
import sys
from typing import Optional, Dict, Any
from io import StringIO


class PycDecompiler:
    """
    General-purpose Python bytecode decompiler.
    
    Provides a unified interface for decompiling Python bytecode using
    various decompilation libraries like uncompyle6, decompyle3, etc.
    """
    
    def __init__(self):
        self.decompilation_errors = []
        self.available_decompilers = self._check_available_decompilers()
    
    def _check_available_decompilers(self) -> Dict[str, bool]:
        """Check which decompilation libraries are available."""
        available = {}
        
        # Check uncompyle6
        try:
            import uncompyle6
            available['uncompyle6'] = True
        except ImportError:
            available['uncompyle6'] = False
        
        # Check decompyle3
        try:
            import decompyle3
            available['decompyle3'] = True
        except ImportError:
            available['decompyle3'] = False
        
        # Check xdis (for bytecode analysis)
        try:
            import xdis
            available['xdis'] = True
        except ImportError:
            available['xdis'] = False
        
        return available
    
    def decompile_file(self, pyc_path: str) -> Optional[str]:
        """
        Decompile a .pyc file to Python source code.
        
        Args:
            pyc_path: Path to the .pyc file
            
        Returns:
            Decompiled Python source code if successful, None otherwise
        """
        self.decompilation_errors.clear()
        
        try:
            # Try different decompilation methods
            if self.available_decompilers.get('uncompyle6'):
                result = self._decompile_file_with_uncompyle6(pyc_path)
                if result:
                    return result
            
            if self.available_decompilers.get('decompyle3'):
                result = self._decompile_file_with_decompyle3(pyc_path)
                if result:
                    return result
            
            # If no decompiler worked
            self.decompilation_errors.append("No suitable decompiler available")
            return None
            
        except Exception as e:
            self.decompilation_errors.append(f"Decompilation failed: {e}")
            return None
    
    def decompile_code_object(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Decompile a code object to Python source code.
        
        Args:
            code_obj: Python code object to decompile
            
        Returns:
            Decompiled Python source code if successful, None otherwise
        """
        self.decompilation_errors.clear()
        
        try:
            # Try different decompilation methods
            if self.available_decompilers.get('uncompyle6'):
                result = self._decompile_code_with_uncompyle6(code_obj)
                if result:
                    return result
            
            if self.available_decompilers.get('decompyle3'):
                result = self._decompile_code_with_decompyle3(code_obj)
                if result:
                    return result
            
            # Fallback to disassembly
            return self._create_disassembly_fallback(code_obj)
            
        except Exception as e:
            self.decompilation_errors.append(f"Code object decompilation failed: {e}")
            return self._create_disassembly_fallback(code_obj)
    
    def _decompile_file_with_uncompyle6(self, pyc_path: str) -> Optional[str]:
        """Decompile file using uncompyle6."""
        try:
            import uncompyle6
            from uncompyle6.main import decompile_file
            
            output = StringIO()
            decompile_file(pyc_path, output)
            result = output.getvalue()
            output.close()
            
            return result
            
        except Exception as e:
            self.decompilation_errors.append(f"uncompyle6 file decompilation failed: {e}")
            return None
    
    def _decompile_file_with_decompyle3(self, pyc_path: str) -> Optional[str]:
        """Decompile file using decompyle3."""
        try:
            import decompyle3
            from decompyle3.main import decompile_file
            
            output = StringIO()
            decompile_file(pyc_path, output)
            result = output.getvalue()
            output.close()
            
            return result
            
        except Exception as e:
            self.decompilation_errors.append(f"decompyle3 file decompilation failed: {e}")
            return None
    
    def _decompile_code_with_uncompyle6(self, code_obj: types.CodeType) -> Optional[str]:
        """Decompile code object using uncompyle6."""
        try:
            import uncompyle6
            from uncompyle6.main import decompile
            
            output = StringIO()
            decompile(
                co=code_obj,
                out=output,
                showasm=False,
                showast=False,
                timestamp=None,
                showgrammar=False
            )
            
            result = output.getvalue()
            output.close()
            
            return result
            
        except Exception as e:
            self.decompilation_errors.append(f"uncompyle6 code decompilation failed: {e}")
            return None
    
    def _decompile_code_with_decompyle3(self, code_obj: types.CodeType) -> Optional[str]:
        """Decompile code object using decompyle3."""
        try:
            import decompyle3
            from decompyle3.main import decompile
            
            output = StringIO()
            decompile(
                co=code_obj,
                out=output,
                showasm=False,
                showast=False
            )
            
            result = output.getvalue()
            output.close()
            
            return result
            
        except Exception as e:
            self.decompilation_errors.append(f"decompyle3 code decompilation failed: {e}")
            return None
    
    def _create_disassembly_fallback(self, code_obj: types.CodeType) -> str:
        """Create a disassembly-based fallback when decompilation fails."""
        import dis
        
        try:
            output = StringIO()
            dis.dis(code_obj, file=output)
            disassembly = output.getvalue()
            output.close()
            
            # Create a commented version of the disassembly
            lines = [
                "# DECOMPILATION FAILED - SHOWING DISASSEMBLY",
                "# Install uncompyle6 for better decompilation: pip install uncompyle6",
                "",
                f"# Code object: {code_obj.co_name}",
                f"# Filename: {code_obj.co_filename}",
                f"# Arguments: {code_obj.co_argcount}",
                "",
                "# BYTECODE DISASSEMBLY:",
            ]
            
            # Add disassembly as comments
            for line in disassembly.split('\n'):
                if line.strip():
                    lines.append(f"# {line}")
            
            lines.extend([
                "",
                "# Placeholder function based on metadata:",
                f"def {code_obj.co_name if code_obj.co_name != '<module>' else 'unknown_function'}():",
                "    '''",
                "    This function could not be decompiled.",
                "    See disassembly above for bytecode details.",
                "    '''",
                "    pass"
            ])
            
            return '\n'.join(lines)
            
        except Exception as e:
            return f"# Disassembly also failed: {e}\n# Code object could not be analyzed"
    
    def get_available_decompilers(self) -> Dict[str, bool]:
        """Get information about available decompilers."""
        return self.available_decompilers.copy()
    
    def get_last_errors(self) -> list:
        """Get errors from the last decompilation attempt."""
        return self.decompilation_errors.copy()
    
    def analyze_bytecode(self, code_obj: types.CodeType) -> Dict[str, Any]:
        """
        Analyze a code object and extract metadata.
        
        Args:
            code_obj: Code object to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            'name': code_obj.co_name,
            'filename': code_obj.co_filename,
            'argcount': code_obj.co_argcount,
            'nlocals': code_obj.co_nlocals,
            'stacksize': code_obj.co_stacksize,
            'flags': code_obj.co_flags,
            'code_size': len(code_obj.co_code),
            'constants': code_obj.co_consts,
            'names': code_obj.co_names,
            'varnames': code_obj.co_varnames,
            'freevars': code_obj.co_freevars,
            'cellvars': code_obj.co_cellvars,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}"
        }
        
        # Add flag analysis
        flags = []
        if code_obj.co_flags & 0x04:
            flags.append("*args")
        if code_obj.co_flags & 0x08:
            flags.append("**kwargs")
        if code_obj.co_flags & 0x20:
            flags.append("generator")
        if code_obj.co_flags & 0x40:
            flags.append("coroutine")
        if code_obj.co_flags & 0x80:
            flags.append("async_generator")
        
        analysis['flag_descriptions'] = flags
        
        return analysis
