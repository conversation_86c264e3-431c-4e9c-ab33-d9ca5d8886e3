"""
Serialization Peeler for PyThon-Unshackle

This module implements the serialization layer peeler that handles marshal
and pickle serialized objects. This is a critical component as marshal is
commonly used to serialize Python code objects for obfuscation.

SECURITY NOTE: This module handles potentially dangerous serialized data.
It uses safe practices and never executes the deserialized code objects.
"""

import marshal
import pickle
import types
import re
import time
import sys
from typing import Union, Tuple, List, Dict, Any, Optional

from .base_layer import BaseLayer, LayerOutput, LayerResult, LayerMetadata


class SerializationPeeler(BaseLayer):
    """
    Serialization layer peeler for marshal and pickle objects.
    
    Handles:
    - marshal.loads() deserialization (primary focus for code objects)
    - pickle.loads() deserialization (with safety considerations)
    - Code object analysis and extraction
    - Safe handling of potentially malicious serialized data
    """
    
    def __init__(self):
        super().__init__("Serialization Peeler", priority=40)
        self._init_patterns()
        self._init_magic_bytes()
    
    def _init_patterns(self) -> None:
        """Initialize regex patterns for serialization detection."""
        self.patterns = {
            'marshal_function': r'marshal\.loads?\s*\(',
            'pickle_function': r'(?:pickle|cPickle)\.loads?\s*\(',
            'marshal_import': r'import\s+marshal',
            'pickle_import': r'import\s+(?:pickle|cPickle)',
        }
    
    def _init_magic_bytes(self) -> None:
        """Initialize magic byte signatures for serialization formats."""
        # Marshal magic bytes vary by Python version
        self.marshal_magic_bytes = [
            b'\xe3\x00\x00\x00',  # Python 3.6+
            b'\xf3\x00\x00\x00',  # Python 3.7+
            b'\x03\xf3\x00\x00',  # Python 3.8+
        ]
        
        # Pickle protocol magic bytes
        self.pickle_magic_bytes = [
            b'\x80\x02',  # Pickle protocol 2
            b'\x80\x03',  # Pickle protocol 3
            b'\x80\x04',  # Pickle protocol 4
            b'\x80\x05',  # Pickle protocol 5
            b'(',         # Pickle protocol 0 (text)
            b']',         # Pickle list
            b'}',         # Pickle dict
        ]
    
    def can_handle(self, data: Union[str, bytes]) -> Tuple[bool, float]:
        """
        Determine if this layer can handle the given data.
        
        Args:
            data: Input data to analyze
            
        Returns:
            Tuple of (can_handle: bool, confidence: float)
        """
        confidence = 0.0
        evidence_count = 0
        
        # Convert string to bytes if necessary for magic byte checking
        if isinstance(data, str):
            try:
                byte_data = data.encode('latin-1')
            except:
                byte_data = data.encode('utf-8', errors='ignore')
            
            # Check for serialization function calls in text
            for pattern_name, pattern in self.patterns.items():
                if re.search(pattern, data, re.IGNORECASE):
                    confidence += 0.4
                    evidence_count += 1
        else:
            byte_data = data
        
        # Check for marshal magic bytes
        for magic in self.marshal_magic_bytes:
            if byte_data.startswith(magic) or magic in byte_data[:20]:
                confidence += 0.8
                evidence_count += 1
                break
        
        # Check for pickle magic bytes
        for magic in self.pickle_magic_bytes:
            if byte_data.startswith(magic):
                confidence += 0.6
                evidence_count += 1
                break
        
        # Try to actually deserialize to verify
        if isinstance(data, bytes) and len(data) > 4:
            # Test marshal
            if self._test_marshal_loads(data):
                confidence += 0.9
                evidence_count += 1
            
            # Test pickle (with caution)
            elif self._test_pickle_loads(data):
                confidence += 0.7
                evidence_count += 1
        
        return evidence_count > 0, min(confidence, 1.0)
    
    def process(self, data: Union[str, bytes]) -> LayerOutput:
        """
        Process the input data and attempt to deserialize it.
        
        Args:
            data: Serialized data to deserialize
            
        Returns:
            LayerOutput containing the deserialized data and metadata
        """
        start_time = time.time()
        
        # Convert string to bytes if necessary
        if isinstance(data, str):
            try:
                byte_data = data.encode('latin-1')
            except:
                byte_data = data.encode('utf-8', errors='ignore')
        else:
            byte_data = data
        
        input_size = len(byte_data)
        
        # Try marshal first (most common for code obfuscation)
        marshal_result = self._process_marshal(byte_data)
        if marshal_result:
            processing_time = time.time() - start_time
            self._update_statistics(True, input_size)
            return marshal_result
        
        # Try pickle second
        pickle_result = self._process_pickle(byte_data)
        if pickle_result:
            processing_time = time.time() - start_time
            self._update_statistics(True, input_size)
            return pickle_result
        
        # If no method worked
        processing_time = time.time() - start_time
        self._update_statistics(False, input_size)
        
        return self._create_failed_output("No serialization format could be deserialized", start_time, input_size)
    
    def _process_marshal(self, data: bytes) -> Optional[LayerOutput]:
        """
        Process marshal serialized data.
        
        Args:
            data: Marshal serialized bytes
            
        Returns:
            LayerOutput if successful, None otherwise
        """
        try:
            # Attempt to load marshal data
            obj = marshal.loads(data)
            
            # Analyze what we got
            obj_info = self._analyze_marshal_object(obj)
            
            # If it's a code object, we need to decompile it
            if isinstance(obj, types.CodeType):
                # This is the critical path - we have a code object
                decompiled_code = self._decompile_code_object(obj)
                if decompiled_code:
                    return LayerOutput(
                        data=decompiled_code,
                        metadata=self._create_metadata(
                            LayerResult.SUCCESS,
                            "marshal_data",
                            "python_code",
                            len(data),
                            0.0,  # Will be set by caller
                            1.0,
                            {
                                "serialization_method": "marshal",
                                "object_type": "code_object",
                                "code_info": obj_info
                            }
                        ),
                        success=True
                    )
            
            # If it's not a code object, return the object as string
            else:
                return LayerOutput(
                    data=str(obj),
                    metadata=self._create_metadata(
                        LayerResult.SUCCESS,
                        "marshal_data",
                        "python_object",
                        len(data),
                        0.0,
                        0.8,
                        {
                            "serialization_method": "marshal",
                            "object_type": type(obj).__name__,
                            "object_info": obj_info
                        }
                    ),
                    success=True
                )
        
        except Exception as e:
            return None
    
    def _process_pickle(self, data: bytes) -> Optional[LayerOutput]:
        """
        Process pickle serialized data with safety considerations.
        
        Args:
            data: Pickle serialized bytes
            
        Returns:
            LayerOutput if successful, None otherwise
        """
        try:
            # SECURITY: Pickle can execute arbitrary code during deserialization
            # We use a restricted unpickler for safety
            obj = self._safe_pickle_loads(data)
            
            if obj is not None:
                obj_info = self._analyze_pickle_object(obj)
                
                return LayerOutput(
                    data=str(obj),
                    metadata=self._create_metadata(
                        LayerResult.SUCCESS,
                        "pickle_data",
                        "python_object",
                        len(data),
                        0.0,
                        0.7,
                        {
                            "serialization_method": "pickle",
                            "object_type": type(obj).__name__,
                            "object_info": obj_info
                        }
                    ),
                    success=True
                )
        
        except Exception as e:
            return None
    
    def _decompile_code_object(self, code_obj: types.CodeType) -> Optional[str]:
        """
        Decompile a code object back to Python source code.
        
        Args:
            code_obj: Python code object to decompile
            
        Returns:
            Decompiled Python source code if successful, None otherwise
        """
        try:
            # Import decompiler here to avoid circular imports
            from ..decompiler.marshal_decompiler import MarshalDecompiler
            
            decompiler = MarshalDecompiler()
            return decompiler.decompile_code_object(code_obj)
            
        except ImportError:
            # Fallback: try to use uncompyle6 directly
            try:
                import uncompyle6
                from io import StringIO
                
                output = StringIO()
                uncompyle6.decompile(code_obj, output)
                return output.getvalue()
                
            except Exception as e:
                # Last resort: return disassembly
                import dis
                from io import StringIO
                
                output = StringIO()
                dis.dis(code_obj, file=output)
                disassembly = output.getvalue()
                
                return f"# Could not decompile to source code\n# Disassembly:\n# {disassembly.replace(chr(10), chr(10) + '# ')}"
    
    def _analyze_marshal_object(self, obj: Any) -> Dict[str, Any]:
        """Analyze a marshal object and extract metadata."""
        info = {
            "type": type(obj).__name__,
            "size": sys.getsizeof(obj)
        }
        
        if isinstance(obj, types.CodeType):
            info.update({
                "filename": obj.co_filename,
                "name": obj.co_name,
                "argcount": obj.co_argcount,
                "nlocals": obj.co_nlocals,
                "stacksize": obj.co_stacksize,
                "flags": obj.co_flags,
                "code_size": len(obj.co_code),
                "constants_count": len(obj.co_consts) if obj.co_consts else 0,
                "names_count": len(obj.co_names) if obj.co_names else 0,
            })
        
        return info
    
    def _analyze_pickle_object(self, obj: Any) -> Dict[str, Any]:
        """Analyze a pickle object and extract metadata."""
        return {
            "type": type(obj).__name__,
            "size": sys.getsizeof(obj),
            "repr_preview": str(obj)[:100] + "..." if len(str(obj)) > 100 else str(obj)
        }
    
    def _test_marshal_loads(self, data: bytes) -> bool:
        """Test if data can be loaded with marshal."""
        try:
            marshal.loads(data)
            return True
        except:
            return False
    
    def _test_pickle_loads(self, data: bytes) -> bool:
        """Test if data can be loaded with pickle (safely)."""
        try:
            # Use a very restrictive test
            if len(data) > 1000000:  # Don't test very large pickle files
                return False
            
            # Quick magic byte check first
            if not any(data.startswith(magic) for magic in self.pickle_magic_bytes):
                return False
            
            # Try to load with restricted unpickler
            obj = self._safe_pickle_loads(data)
            return obj is not None
        except:
            return False
    
    def _safe_pickle_loads(self, data: bytes) -> Any:
        """
        Safely load pickle data with restrictions.
        
        This method attempts to load pickle data while preventing
        execution of dangerous operations.
        """
        # For now, we'll be very conservative and not load pickle at all
        # In a production environment, you'd want to implement a proper
        # restricted unpickler that only allows safe types
        
        # TODO: Implement RestrictedUnpickler class
        # For safety, we'll skip pickle loading for now
        return None
    
    def _create_failed_output(self, error_msg: str, start_time: float, input_size: int) -> LayerOutput:
        """Create a failed LayerOutput."""
        processing_time = time.time() - start_time
        
        return LayerOutput(
            data=None,
            metadata=self._create_metadata(
                LayerResult.FAILED,
                "serialized_data",
                "none",
                input_size,
                processing_time,
                0.0,
                {"error": error_msg}
            ),
            success=False,
            error_message=error_msg
        )
