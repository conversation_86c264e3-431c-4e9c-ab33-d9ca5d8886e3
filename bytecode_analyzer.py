#!/usr/bin/env python3
"""
Manual bytecode analyzer for complex marshal data
This script manually analyzes Python bytecode to extract the original code
"""

import re
import sys
import dis
import types
from io import StringIO


def extract_marshal_bytes(filename):
    """Extract marshal bytes from file."""
    with open(filename, 'rb') as f:
        content = f.read()
    
    pattern = rb'marshal\.loads\s*\(\s*b[\'"]([^\'"]*)[\'"]'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        binary_str = match.group(1)
        actual_bytes = binary_str.decode('unicode_escape').encode('latin-1')
        return actual_bytes
    return None


def analyze_bytecode_manually(data):
    """Manually analyze bytecode structure."""
    print("=== MANUAL BYTECODE ANALYSIS ===")
    
    # Skip the marshal header and try to find the actual bytecode
    # Python 3.9 marshal format: magic + flags + code
    
    # Look for bytecode patterns
    i = 0
    while i < len(data) - 20:
        # Look for code object signature
        if data[i:i+4] == b'c\x00\x00\x00':
            print(f"Found potential code object at offset {i}")
            
            # Try to extract code object manually
            try:
                # Skip to the actual bytecode part
                # This is a simplified approach
                code_start = i + 20  # Skip header
                
                # Look for the bytecode section
                bytecode_section = data[code_start:code_start+200]
                print(f"Bytecode section (first 50 bytes): {bytecode_section[:50].hex()}")
                
                # Analyze bytecode instructions
                analyze_instructions(bytecode_section)
                
            except Exception as e:
                print(f"Error analyzing at offset {i}: {e}")
        
        i += 1


def analyze_instructions(bytecode):
    """Analyze individual bytecode instructions."""
    print("\n=== INSTRUCTION ANALYSIS ===")
    
    # Python 3.9 opcodes (simplified)
    opcodes = {
        100: 'LOAD_CONST',
        101: 'LOAD_NAME', 
        102: 'BUILD_TUPLE',
        103: 'BUILD_LIST',
        104: 'BUILD_SET',
        105: 'BUILD_MAP',
        106: 'LOAD_ATTR',
        107: 'COMPARE_OP',
        108: 'IMPORT_NAME',
        109: 'IMPORT_FROM',
        110: 'JUMP_FORWARD',
        111: 'JUMP_IF_FALSE_OR_POP',
        112: 'JUMP_IF_TRUE_OR_POP',
        113: 'POP_JUMP_IF_FALSE',
        114: 'POP_JUMP_IF_TRUE',
        115: 'LOAD_GLOBAL',
        116: 'CONTINUE_LOOP',
        117: 'SETUP_LOOP',
        118: 'SETUP_EXCEPT',
        119: 'SETUP_FINALLY',
        120: 'LOAD_FAST',
        121: 'STORE_FAST',
        122: 'DELETE_FAST',
        124: 'LOAD_CLOSURE',
        125: 'LOAD_DEREF',
        126: 'LOAD_CLASSDEREF',
        127: 'STORE_DEREF',
        128: 'DELETE_DEREF',
        131: 'CALL_FUNCTION',
        132: 'CALL_FUNCTION_KW',
        133: 'CALL_FUNCTION_EX',
        134: 'LOAD_METHOD',
        135: 'CALL_METHOD',
        90: 'STORE_NAME',
        160: 'CALL_METHOD',  # Different in 3.9
    }
    
    instructions = []
    i = 0
    
    while i < len(bytecode) - 1:
        opcode = bytecode[i]
        arg = bytecode[i + 1] if i + 1 < len(bytecode) else 0
        
        if opcode in opcodes:
            instructions.append((opcode, opcodes[opcode], arg))
            print(f"  {i:3d}: {opcodes[opcode]:<20} {arg}")
        
        i += 2
        
        if len(instructions) > 20:  # Limit output
            break
    
    return instructions


def try_reconstruct_code(data):
    """Try to reconstruct the original code from patterns."""
    print("\n=== CODE RECONSTRUCTION ===")
    
    # Look for string constants in the data
    strings = extract_string_constants(data)
    
    if strings:
        print("Found string constants:")
        for i, s in enumerate(strings[:10]):
            print(f"  {i}: {repr(s)}")
        
        # Try to piece together the code
        reconstruct_from_strings(strings)


def extract_string_constants(data):
    """Extract string constants from marshal data."""
    strings = []
    
    # Look for string patterns in the data
    # Strings in marshal are usually preceded by length markers
    
    i = 0
    while i < len(data) - 4:
        # Look for potential string length markers
        if data[i] == 0x73:  # 's' - string marker in marshal
            try:
                # Next 4 bytes might be length
                length = int.from_bytes(data[i+1:i+5], 'little')
                if 1 <= length <= 1000:  # Reasonable string length
                    string_data = data[i+5:i+5+length]
                    try:
                        decoded = string_data.decode('utf-8')
                        if decoded.isprintable():
                            strings.append(decoded)
                    except:
                        pass
            except:
                pass
        i += 1
    
    return strings


def reconstruct_from_strings(strings):
    """Try to reconstruct code from string constants."""
    print("\n=== RECONSTRUCTION ATTEMPT ===")
    
    # Look for Python keywords and patterns
    python_keywords = ['def', 'class', 'import', 'from', 'if', 'for', 'while', 'try', 'except']
    
    code_lines = []
    
    for s in strings:
        # Check if string looks like Python code
        if any(keyword in s for keyword in python_keywords):
            code_lines.append(s)
        elif s.startswith('print(') or 'print(' in s:
            code_lines.append(s)
        elif len(s) > 20 and any(c.isalpha() for c in s):
            # Might be a long string or code
            code_lines.append(f"# Possible code: {repr(s)}")
    
    if code_lines:
        print("Reconstructed code fragments:")
        for line in code_lines:
            print(f"  {line}")
    else:
        print("Could not reconstruct meaningful code from strings")


def main():
    """Main function."""
    filename = "test.py"
    
    print(f"Manual bytecode analysis of {filename}")
    print("=" * 50)
    
    # Extract marshal data
    data = extract_marshal_bytes(filename)
    if not data:
        print("Failed to extract marshal data")
        return 1
    
    print(f"Extracted {len(data)} bytes of marshal data")
    
    # Manual analysis
    analyze_bytecode_manually(data)
    
    # Try reconstruction
    try_reconstruct_code(data)
    
    # Show raw data analysis
    print(f"\n=== RAW DATA PATTERNS ===")
    
    # Look for common patterns
    patterns = {
        b'def ': 'Function definition',
        b'class ': 'Class definition', 
        b'import ': 'Import statement',
        b'print(': 'Print function',
        b'if ': 'If statement',
        b'for ': 'For loop',
        b'while ': 'While loop',
    }
    
    for pattern, description in patterns.items():
        count = data.count(pattern)
        if count > 0:
            print(f"Found {count}x {description}")
            
            # Show context around matches
            for match in re.finditer(re.escape(pattern), data):
                start = max(0, match.start() - 20)
                end = min(len(data), match.end() + 20)
                context = data[start:end]
                try:
                    decoded = context.decode('utf-8', errors='ignore')
                    print(f"  Context: {repr(decoded)}")
                except:
                    print(f"  Context (hex): {context.hex()}")
                break  # Show only first match
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
