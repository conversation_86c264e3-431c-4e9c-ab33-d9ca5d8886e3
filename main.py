#!/usr/bin/env python3
"""
PyThon-Unshackle - The Ultimate Multi-Stage Python Deobfuscation Tool

Main CLI interface for the deobfuscation pipeline.

Usage:
    python main.py unshackle <file> [options]
    python main.py analyze <file> [options]
    python main.py --help

Author: AI Assistant
Version: 1.0.0
"""

import sys
import os
import argparse
import time
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python_unshackle.core.pipeline import Deobfuscation<PERSON><PERSON>eline, PipelineConfig
from python_unshackle.core.detector import PatternDetector
from python_unshackle.utils.logger import UnshackleLogger
from python_unshackle.utils.code_beautifier import CodeBeautifier


def create_parser() -> argparse.ArgumentParser:
    """Create the command-line argument parser."""
    parser = argparse.ArgumentParser(
        prog='python-unshackle',
        description='The Ultimate Multi-Stage Python Deobfuscation Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py unshackle obfuscated.py
  python main.py unshackle obfuscated.py --verbose --output clean.py
  python main.py analyze obfuscated.py --detect-only
  python main.py unshackle obfuscated.py --max-iterations 5 --timeout 30

For more information, visit: https://github.com/your-repo/python-unshackle
        """
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Unshackle command
    unshackle_parser = subparsers.add_parser(
        'unshackle',
        help='Deobfuscate a Python file'
    )
    unshackle_parser.add_argument(
        'input_file',
        help='Path to the obfuscated Python file'
    )
    unshackle_parser.add_argument(
        '-o', '--output',
        help='Output file path (default: <input>.unshackled)'
    )
    unshackle_parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    unshackle_parser.add_argument(
        '--max-iterations',
        type=int,
        default=10,
        help='Maximum number of deobfuscation iterations (default: 10)'
    )
    unshackle_parser.add_argument(
        '--timeout',
        type=float,
        help='Timeout in seconds for the entire process'
    )
    unshackle_parser.add_argument(
        '--min-confidence',
        type=float,
        default=0.5,
        help='Minimum confidence threshold for layer application (default: 0.5)'
    )
    unshackle_parser.add_argument(
        '--no-recursive',
        action='store_true',
        help='Disable recursive processing'
    )
    unshackle_parser.add_argument(
        '--save-intermediate',
        action='store_true',
        help='Save intermediate results for debugging'
    )
    unshackle_parser.add_argument(
        '--no-beautify',
        action='store_true',
        help='Skip code beautification'
    )
    unshackle_parser.add_argument(
        '--export-logs',
        help='Export detailed logs to specified file'
    )
    
    # Analyze command
    analyze_parser = subparsers.add_parser(
        'analyze',
        help='Analyze obfuscation patterns without deobfuscating'
    )
    analyze_parser.add_argument(
        'input_file',
        help='Path to the Python file to analyze'
    )
    analyze_parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    analyze_parser.add_argument(
        '--detect-only',
        action='store_true',
        help='Only detect patterns, do not show recommendations'
    )
    
    # Global options
    parser.add_argument(
        '--version',
        action='version',
        version='PyThon-Unshackle 1.0.0'
    )
    parser.add_argument(
        '--no-colors',
        action='store_true',
        help='Disable colored output'
    )
    
    return parser


def handle_unshackle_command(args) -> int:
    """Handle the unshackle command."""
    # Validate input file
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"Error: Input file '{args.input_file}' not found.")
        return 1
    
    if not input_path.is_file():
        print(f"Error: '{args.input_file}' is not a file.")
        return 1
    
    # Determine output file
    if args.output:
        output_path = Path(args.output)
    else:
        output_path = input_path.with_suffix(input_path.suffix + '.unshackled')
    
    # Create logger
    logger = UnshackleLogger(
        verbose=args.verbose,
        use_colors=not args.no_colors
    )
    
    # Create pipeline configuration
    config = PipelineConfig(
        max_iterations=args.max_iterations,
        min_confidence_threshold=args.min_confidence,
        enable_recursive_processing=not args.no_recursive,
        save_intermediate_results=args.save_intermediate,
        timeout_seconds=args.timeout
    )
    
    # Create and run pipeline
    pipeline = DeobfuscationPipeline(config=config, logger=logger)
    
    try:
        # Run deobfuscation
        result = pipeline.deobfuscate_file(str(input_path))
        
        if result.success and result.final_code:
            # Beautify code if requested
            final_code = result.final_code
            if not args.no_beautify:
                try:
                    beautifier = CodeBeautifier()
                    final_code = beautifier.beautify(final_code)
                    logger.debug("Code beautification applied")
                except Exception as e:
                    logger.warning(f"Code beautification failed: {e}")
            
            # Save result
            success = pipeline.save_result(
                result._replace(final_code=final_code),
                str(output_path)
            )
            
            if success:
                # Display the final code
                logger.print_code_output(final_code)
                
                # Show statistics if verbose
                if args.verbose:
                    stats = pipeline.get_statistics()
                    logger.print_statistics(stats)
                
                # Export logs if requested
                if args.export_logs:
                    logger.export_logs(args.export_logs)
                
                return 0
            else:
                return 1
        else:
            logger.error(f"Deobfuscation failed: {result.error_message or 'Unknown error'}")
            return 1
            
    except KeyboardInterrupt:
        logger.error("Deobfuscation interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def handle_analyze_command(args) -> int:
    """Handle the analyze command."""
    # Validate input file
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"Error: Input file '{args.input_file}' not found.")
        return 1
    
    # Create logger
    logger = UnshackleLogger(
        verbose=args.verbose,
        use_colors=not args.no_colors
    )
    
    try:
        # Read the file
        with open(input_path, 'rb') as f:
            data = f.read()
        
        # Try to decode as UTF-8
        try:
            text_data = data.decode('utf-8')
        except UnicodeDecodeError:
            text_data = data
        
        logger.info(f"Analyzing obfuscation patterns in '{args.input_file}'...")
        
        # Create detector and analyze
        detector = PatternDetector()
        detection_results = detector.detect_obfuscation(text_data)
        
        if detection_results:
            logger.print_separator("OBFUSCATION ANALYSIS RESULTS")
            
            for i, result in enumerate(detection_results, 1):
                print(f"\n{i}. {result.obfuscation_type.value.upper()}")
                print(f"   Confidence: {result.confidence:.2f}")
                print(f"   Evidence:")
                for evidence in result.evidence:
                    print(f"     - {evidence}")
                
                if result.additional_info:
                    print(f"   Additional Info:")
                    for key, value in result.additional_info.items():
                        print(f"     {key}: {value}")
            
            if not args.detect_only:
                # Show recommendations
                recommended_layers = detector.get_recommended_layers(detection_results)
                if recommended_layers:
                    print(f"\nRecommended deobfuscation layers:")
                    for layer in recommended_layers:
                        print(f"  - {layer}")
                    
                    print(f"\nTo deobfuscate this file, run:")
                    print(f"  python main.py unshackle {args.input_file}")
            
            logger.print_separator()
        else:
            logger.info("No obfuscation patterns detected.")
        
        return 0
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def main() -> int:
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    if args.command == 'unshackle':
        return handle_unshackle_command(args)
    elif args.command == 'analyze':
        return handle_analyze_command(args)
    else:
        print(f"Unknown command: {args.command}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
