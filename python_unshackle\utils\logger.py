"""
Logger for PyThon-Unshackle

Provides comprehensive logging and reporting capabilities for the deobfuscation
pipeline. Supports colored output, verbose modes, and detailed progress tracking.
"""

import sys
import time
from typing import Any, Dict, List, Optional
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

try:
    from colorama import Fore, Back, Style, init
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    # Fallback color constants
    class Fore:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Style:
        BRIGHT = DIM = RESET_ALL = ""


class LogLevel(Enum):
    """Log levels for different types of messages"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SUCCESS = "SUCCESS"
    LAYER = "LAYER"


@dataclass
class LogEntry:
    """Individual log entry"""
    timestamp: datetime
    level: LogLevel
    message: str
    layer_name: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


class UnshackleLogger:
    """
    Comprehensive logger for PyThon-Unshackle operations.
    
    Provides colored output, verbose reporting, and detailed tracking
    of the deobfuscation pipeline progress.
    """
    
    def __init__(self, verbose: bool = False, use_colors: bool = True):
        """
        Initialize the logger.
        
        Args:
            verbose: Enable verbose output
            use_colors: Enable colored output (if colorama is available)
        """
        self.verbose = verbose
        self.use_colors = use_colors and COLORAMA_AVAILABLE
        self.log_entries: List[LogEntry] = []
        self.start_time = time.time()
        self.layer_count = 0
        
        # Color mapping for different log levels
        self.colors = {
            LogLevel.DEBUG: Fore.CYAN,
            LogLevel.INFO: Fore.WHITE,
            LogLevel.WARNING: Fore.YELLOW,
            LogLevel.ERROR: Fore.RED,
            LogLevel.SUCCESS: Fore.GREEN,
            LogLevel.LAYER: Fore.MAGENTA
        }
    
    def info(self, message: str, **kwargs) -> None:
        """Log an info message."""
        self._log(LogLevel.INFO, message, **kwargs)
    
    def success(self, message: str, **kwargs) -> None:
        """Log a success message."""
        self._log(LogLevel.SUCCESS, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log a warning message."""
        self._log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log an error message."""
        self._log(LogLevel.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log a debug message (only shown in verbose mode)."""
        if self.verbose:
            self._log(LogLevel.DEBUG, message, **kwargs)
    
    def layer_start(self, layer_name: str, message: str, **kwargs) -> None:
        """Log the start of a layer operation."""
        self.layer_count += 1
        formatted_message = f"[LAYER {self.layer_count}] {message}"
        self._log(LogLevel.LAYER, formatted_message, layer_name=layer_name, **kwargs)
    
    def layer_success(self, layer_name: str, message: str, **kwargs) -> None:
        """Log a successful layer operation."""
        formatted_message = f"[LAYER {self.layer_count}] {message} Success."
        self._log(LogLevel.SUCCESS, formatted_message, layer_name=layer_name, **kwargs)
    
    def layer_failed(self, layer_name: str, message: str, **kwargs) -> None:
        """Log a failed layer operation."""
        formatted_message = f"[LAYER {self.layer_count}] {message} Failed."
        self._log(LogLevel.ERROR, formatted_message, layer_name=layer_name, **kwargs)
    
    def start_deobfuscation(self, filename: str) -> None:
        """Log the start of deobfuscation process."""
        self.start_time = time.time()
        self.layer_count = 0
        self.info(f"Starting deobfuscation for '{filename}'...")
        if self.verbose:
            self.debug(f"Verbose mode enabled")
            self.debug(f"Colors enabled: {self.use_colors}")
    
    def end_deobfuscation(self, success: bool, output_file: Optional[str] = None) -> None:
        """Log the end of deobfuscation process."""
        elapsed_time = time.time() - self.start_time
        
        if success:
            if output_file:
                self.success(f"Deobfuscation complete. Output saved to '{output_file}'.")
            else:
                self.success("Deobfuscation complete.")
        else:
            self.error("Deobfuscation failed.")
        
        if self.verbose:
            self.debug(f"Total processing time: {elapsed_time:.2f} seconds")
            self.debug(f"Total layers processed: {self.layer_count}")
    
    def print_separator(self, title: str = "DECOMPILED SOURCE") -> None:
        """Print a separator line with title."""
        separator = "-" * 50
        if self.use_colors:
            print(f"{Style.BRIGHT}{separator} {title} {separator}{Style.RESET_ALL}")
        else:
            print(f"{separator} {title} {separator}")
    
    def print_code_output(self, code: str) -> None:
        """Print the final decompiled code with formatting."""
        self.print_separator("DECOMPILED SOURCE")
        print(code)
        self.print_separator()
    
    def print_statistics(self, stats: Dict[str, Any]) -> None:
        """Print processing statistics."""
        if not self.verbose:
            return
        
        self.print_separator("PROCESSING STATISTICS")
        for key, value in stats.items():
            self.debug(f"{key}: {value}")
        self.print_separator()
    
    def _log(self, level: LogLevel, message: str, layer_name: Optional[str] = None, **kwargs) -> None:
        """
        Internal logging method.
        
        Args:
            level: Log level
            message: Message to log
            layer_name: Optional layer name
            **kwargs: Additional data to store
        """
        # Create log entry
        entry = LogEntry(
            timestamp=datetime.now(),
            level=level,
            message=message,
            layer_name=layer_name,
            additional_data=kwargs
        )
        self.log_entries.append(entry)
        
        # Format and print message
        formatted_message = self._format_message(entry)
        print(formatted_message)
    
    def _format_message(self, entry: LogEntry) -> str:
        """
        Format a log entry for display.
        
        Args:
            entry: Log entry to format
            
        Returns:
            Formatted message string
        """
        # Get color for this log level
        color = ""
        reset = ""
        if self.use_colors:
            color = self.colors.get(entry.level, "")
            reset = Style.RESET_ALL
        
        # Format timestamp for verbose mode
        timestamp = ""
        if self.verbose:
            timestamp = f"[{entry.timestamp.strftime('%H:%M:%S')}] "
        
        # Format the message
        level_prefix = f"[{entry.level.value}]" if entry.level != LogLevel.INFO else ""
        
        return f"{color}{timestamp}{level_prefix} {entry.message}{reset}"
    
    def get_log_entries(self) -> List[LogEntry]:
        """Get all log entries."""
        return self.log_entries.copy()
    
    def clear_logs(self) -> None:
        """Clear all log entries."""
        self.log_entries.clear()
        self.layer_count = 0
    
    def export_logs(self, filename: str) -> None:
        """
        Export logs to a file.
        
        Args:
            filename: Output filename for logs
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"PyThon-Unshackle Log Export\n")
                f.write(f"Generated: {datetime.now().isoformat()}\n")
                f.write("=" * 50 + "\n\n")
                
                for entry in self.log_entries:
                    f.write(f"[{entry.timestamp.isoformat()}] ")
                    f.write(f"[{entry.level.value}] ")
                    if entry.layer_name:
                        f.write(f"[{entry.layer_name}] ")
                    f.write(f"{entry.message}\n")
                    
                    if entry.additional_data:
                        for key, value in entry.additional_data.items():
                            f.write(f"  {key}: {value}\n")
                        f.write("\n")
            
            self.success(f"Logs exported to '{filename}'")
            
        except Exception as e:
            self.error(f"Failed to export logs: {e}")
